import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';

export interface IBreadCrampWebPartProps {
  description: string;
}

export interface IBreadcrumbItem {
  title: string;
  url?: string;
  isCurrent?: boolean;
}

export default class BreadCrampWebPart extends BaseClientSideWebPart<IBreadCrampWebPartProps> {

  private _isArabic: boolean = false;
  private _isNewsDetailsInSubsite: boolean = false;

  public async render(): Promise<void> {
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;

    // تحديد إذا كان في صفحة تفاصيل الأخبار في subsite
    this._isNewsDetailsInSubsite = this._checkIfNewsDetailsInSubsite();

    const backText = this._isArabic ? 'رجوع' : 'Back';
  
    const breadcrumbItems = this.generateDynamicBreadcrumb();
    const currentPath = window.location.pathname;
  
    const title = await this.getPageTitle(currentPath);
    if (title) {
      const lastItem = breadcrumbItems[breadcrumbItems.length - 1];
      if (lastItem) lastItem.title = title;
    }
  
    const breadcrumbHtml = this.generateBreadcrumbHtml(breadcrumbItems);
  
    const directionClass = this._isArabic ? 'flex-row-reverse' : 'flex-row';

    const sectionClass = this._isNewsDetailsInSubsite ? 'bread-cramp container-fluid news-details-subsite' : 'bread-cramp container-fluid';

    this.domElement.innerHTML = `
      <section class="${sectionClass}" id="breadCramp" dir="${this._isArabic ? 'rtl' : 'ltr'}">
        <div class="card card-color">
          <div class="card-body d-flex justify-content-between align-items-center flex-wrap ${directionClass}">
            <h2 class="fw-bold m-0 head-color">${title}</h2>
            <div class="d-flex align-items-center gap-3">
              <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-container m-0">
                  ${breadcrumbHtml}
                </ol>
              </nav>
              <button class="btn btn-outline-dark d-flex align-items-center gap-1" onclick="history.back()">
                <i class="bi bi-arrow-left"></i> ${backText}
              </button>
            </div>
          </div>
        </div>
      </section>
    `;
  }

  private _checkIfNewsDetailsInSubsite(): boolean {
  try {
    const currentUrl = window.location.pathname.toLowerCase();

    const isDetailsPage = currentUrl.includes('newsdetails.aspx') || currentUrl.includes('ourstoriesdetails.aspx');

    return isDetailsPage;
  } catch (error) {
    console.error('BreadCramp - Error checking details page:', error);
    return false;
  }
}


  private async getPageTitle(serverRelativeUrl: string): Promise<string> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/getfilebyserverrelativeurl('${serverRelativeUrl}')/ListItemAllFields?$select=Title`;

    try {
      const response: SPHttpClientResponse = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
      const data = await response.json();
      return data?.Title || '';
    } catch (error) {
      console.error('Error fetching page title:', error);
      return '';
    }
  }

  private generateDynamicBreadcrumb(): IBreadcrumbItem[] {
    const path = window.location.pathname;
    const segments = path.split('/').filter(seg => seg); // Remove empty strings
    const breadcrumbItems: IBreadcrumbItem[] = [];

    let accumulatedUrl = '';

    segments.forEach((segment, index) => {
      accumulatedUrl += `/${segment}`;

      const skipList = ['sitepages', 'pages', 'sites'];
      if (skipList.indexOf(segment.toLowerCase()) !== -1) return;

      const formattedTitle = decodeURIComponent(segment)
        .replace(/[-_]/g, ' ')
        .replace(/\.aspx/i, '')
        .replace(/\b\w/g, c => c.toUpperCase());

      breadcrumbItems.push({
        title: formattedTitle,
        url: index === segments.length - 1 ? undefined : accumulatedUrl,
        isCurrent: index === segments.length - 1
      });
    });

    return breadcrumbItems;
  }

  private generateBreadcrumbHtml(items: IBreadcrumbItem[]): string {
    return items
      .map(item => {
        if (item.isCurrent) {
          return `<li class="breadcrumb-item active" aria-current="page">${item.title}</li>`;
        } else {
          return `<li class="breadcrumb-item"><a href="${item.url}" class="btn-main-link fw-bold">${item.title}</a></li>`;
        }
      })
      .join('');
  }
}

