import { BaseClientSideWebPart } from "@microsoft/sp-webpart-base";
import { SPHttpClient, SPHttpClientResponse } from "@microsoft/sp-http";

import "./HomeSlider.css";

export interface IHomeSliderWebPartProps {
  description: string;
}

export default class HomeSliderWebPart extends BaseClientSideWebPart<IHomeSliderWebPartProps> {
  private _slidesData: any[] = [];
  private _isArabic: boolean = false;
  private _isInSubsite: boolean = false;

  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().includes("/ar/");

    const previousText = this._isArabic ? "السابق" : "Previous";
    const nextText = this._isArabic ? "التالي" : "Next";
    const imageLinkText = this._isArabic ? "رابط الصورة" : "Image link";
    const discoverMoreText = this._isArabic ? "اكتشف المزيد" : "Discover More";
    const arrow = this._isArabic ? "←" : "→";

    const sectionClass = this._isInSubsite
      ? "HomeBanner DepartmentCarousel "
      : "HomeBanner";

    this.domElement.innerHTML = `
    <style>
    img{
    cursor: pointer;
    }
    </style>
      <section class="${sectionClass}">
        <div id="qmCarousel" class="carousel slide carousel-fade" data-bs-ride="carousel">
          <div class="carousel-inner" id="carouselSlidesContainer">
            ${this._renderSlides(discoverMoreText, imageLinkText, arrow)}
          </div>
          <button class="carousel-control-prev" type="button" data-bs-target="#qmCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">${previousText}</span>
          </button>
          <button class="carousel-control-next" type="button" data-bs-target="#qmCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">${nextText}</span>
          </button>
          <div class="carousel-indicators" id="carouselIndicators">
            ${this._renderIndicators()}
          </div>
        </div>
      </section>
    `;

    this._attachViewTrackingEvents();
  }

  protected onInit(): Promise<void> {
    this._isInSubsite = this._checkIfInSubsite();
    return this._getSlidesData().then(() => {
      this.render();
    });
  }

  private _checkIfInSubsite(): boolean {
    try {
      const currentUrl = window.location.pathname.toLowerCase();
      const subsitePattern = /\/sites\/intranet-qm\/([^\/]+)\/sitepages\//i;
      const match = currentUrl.match(subsitePattern);
      if (match && match[1]) {
        const potentialSubsite = match[1].toLowerCase();
        return potentialSubsite !== "ar";
      }
      return false;
    } catch (error) {
      console.error("HomeSlider - Error checking subsite:", error);
      return false;
    }
  }

  private _getSlidesData(): Promise<void> {
    const listUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('HomeBanner')/items?$select=*,AttachmentFiles/FileName,AttachmentFiles/ServerRelativeUrl,Created,Modified,ViewCount0&$expand=AttachmentFiles&$filter=Active eq 1&$orderby=order1 asc`;

    return this.context.spHttpClient
      .get(listUrl, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
      })
      .then((data) => {
        const today = new Date();
        const activeItems = data.value
          ? data.value
            .filter((item: any) => {
              if (!item.Active) return false;
              const startDate = item.StartDate ? new Date(item.StartDate) : null;
              const endDate = item.EndDate ? new Date(item.EndDate) : null;
              const isWithinDateRange =
                (!startDate || startDate <= today) &&
                (!endDate || endDate >= today);
              return isWithinDateRange;
            })
            .sort((a: any, b: any) => (a.order1 || 0) - (b.order1 || 0))
          : [];

        this._slidesData = activeItems.map((item: any) => {
          let imageUrl = "";
          try {
            const imgData = item?.BannerImage ? JSON.parse(item.BannerImage) : null;
            if (imgData?.serverUrl && imgData?.serverRelativeUrl) {
              imageUrl = imgData.serverUrl + imgData.serverRelativeUrl;
            }
          } catch (e) {
            console.warn("Error parsing BannerImage for item:", item?.Id, e);
          }

          if (!imageUrl && item?.AttachmentFiles?.length > 0) {
            const imageFile = item.AttachmentFiles.find((file: any) => {
              const fileName = file.FileName?.toLowerCase() || "";
              return /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(fileName);
            });
            if (imageFile) {
              imageUrl = imageFile.ServerRelativeUrl;
            }
          }

          if (!imageUrl) {
            console.warn(`No image found for slide item ${item?.Id}`);
          }

          return {
            id: item?.Id || 0,
            en: {
              title: item?.BannerTitle_x002d_EN || "",
              desc: item?.Description_x002d_EN || "",
              link: item?.BannerLink_x002d_EN?.Url || "",
            },
            ar: {
              title: item?.BannerTitle_x002d_AR || "",
              desc: item?.Description_x002d_AR || "",
              link: item?.BannerLink_x002d_AR?.Url || "",
            },
            image: imageUrl,
            ViewCount0: item?.ViewCount0 || 0,
          };
        });
      })
      .catch((error) => {
        console.error("Error fetching HomeBanner data:", error);
        this._slidesData = [];
      });
  }

  private _renderSlides(discoverMoreText: string, imageLinkText: string, arrow: string): string {
    return this._slidesData
      .map((slide, index) => {
        const data = this._isArabic ? slide.ar : slide.en;
        const isRTL = this._isArabic;
        const viewsText = this._isArabic ? "مشاهدة" : "views";

        return `
        <div class="carousel-item ${index === 0 ? "active" : ""}" data-slide-id="${slide.id}">
          <picture>
            <source srcset="${slide.image}" type="image/webp" />
            <source srcset="${slide.image}" type="image/jpeg" />
            ${`<a href="${data.link || slide.image}" aria-label="${imageLinkText}" class="banner-link" data-banner-id="${slide.id}" data-link="${data.link || slide.image}" target="_blank">
                <img src="${slide.image}" alt="slide image" class="d-block w-100 object-fit-cover" loading="lazy" />
              </a>`
          }

          </picture>
          ${this._renderCaption(data, isRTL, discoverMoreText, arrow, slide.id, slide.ViewCount0, viewsText)}
        </div>
      `;
      })
      .join("");
  }

  private _renderCaption(
    data: { title: string; desc: string; link: string },
    isRTL: boolean,
    discoverMoreText: string,
    arrow: string,
    slideId: number,
    ViewCount0: number,
    viewsText: string
  ): string {
    if (!data.title && !data.desc) return "";

    return `
      <div class="carousel-caption ${isRTL ? "text-end" : "text-start"} slider-over shadow qm-caption-box" dir="${isRTL ? "rtl" : "ltr"}">
        ${data.title ? `<h2 class="fw-bold head-color mb-3">${data.title}</h2>` : ""}
        ${data.desc ? `<p class="text-color ">${data.desc}</p>` : ""}
        <div class="d-flex ${isRTL ? "justify-content-end" : "justify-content-start"} align-items-center gap-3 mt-3">
          ${data.link
        ? `<a href="${data.link}" class="btn btn-link text-decoration-none fw-bold btn-main-link banner-link" data-banner-id="${slideId}" data-link="${data.link}" target="_blank">${discoverMoreText} ${arrow}</a>`
        : ""}
        </div>
      </div>
    `;
  }

  private _renderIndicators(): string {
    return this._slidesData
      .map((_, index) =>
        `<button type="button" data-bs-target="#qmCarousel" data-bs-slide-to="${index}" class="${index === 0 ? "active" : ""}" aria-label="Slide ${index + 1}"></button>`
      )
      .join("");
  }

  private _attachViewTrackingEvents(): void {
    const bannerLinks = this.domElement.querySelectorAll(".banner-link");
    bannerLinks.forEach((link) => {
      link.addEventListener("click", (event) => {
        event.preventDefault();
        const bannerId = (event.currentTarget as HTMLElement).getAttribute("data-banner-id");
        const linkUrl = (event.currentTarget as HTMLElement).getAttribute("data-link");

        if (bannerId && linkUrl) {
          const idNum = parseInt(bannerId);
          this._incrementViewCount0(idNum).then(() => {
            window.open(linkUrl, "_blank");
          });
        }
      });
    });

    

    
  }


  private async _incrementViewCount0(bannerId: number): Promise<void> {
    try {

      const getUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('HomeBanner')/items(${bannerId})`;

      const getResponse = await this.context.spHttpClient.get(getUrl, SPHttpClient.configurations.v1);
      if (!getResponse.ok) throw new Error(`Failed to get current view count: ${getResponse.status}`);

      const currentItem = await getResponse.json();
      const currentViewCount0 = currentItem.ViewCount0 || 0;
      const newViewCount0 = currentViewCount0 + 1;

      const updateUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('HomeBanner')/items(${bannerId})`;
      const updateResponse = await this.context.spHttpClient.post(updateUrl, SPHttpClient.configurations.v1, {
        headers: {
          Accept: "application/json;odata=nometadata",
          "Content-type": "application/json;odata=nometadata",
          "odata-version": "",
          "IF-MATCH": "*",
          "X-HTTP-Method": "MERGE",
        },
        body: JSON.stringify({ ViewCount0: newViewCount0 }),
      });

      if (updateResponse.ok) {
        this._updateLocalViewCount0(bannerId, newViewCount0);
      } else {
      }
    } catch (error) {
    }
  }

  private _updateLocalViewCount0(bannerId: number, newViewCount0: number): void {
    const slideIndex = this._slidesData.findIndex((slide) => slide.id === bannerId);
    if (slideIndex !== -1) {
      this._slidesData[slideIndex].ViewCount0 = newViewCount0;
    }

    const ViewCount0Element = this.domElement.querySelector(`span.view-count[data-banner-id="${bannerId}"]`);
    if (ViewCount0Element) {
      ViewCount0Element.textContent = newViewCount0.toLocaleString();
    }
  }
}
