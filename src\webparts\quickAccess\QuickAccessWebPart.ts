import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';



export interface IQuickAccessWebPartProps {
  description: string;
}

export default class QuickAccessWebPart extends BaseClientSideWebPart<IQuickAccessWebPartProps> {

  private _isArabic: boolean = false;

  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;
    this.loadData();
  }

  private async loadData(): Promise<void> {
    const listUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Quick Access')/items?$filter=Active eq '1'`;

    try {
      const response: SPHttpClientResponse = await this.context.spHttpClient.get(listUrl, SPHttpClient.configurations.v1);
      const data: any = await response.json();
      const items: any[] = data.value;

      let html = `
      <section class="q-section h-100">
        <div class="card section-card card-color h-100 p-3">
          <div class="container-fluid">
            <h2 class="fw-bold tittle head-color">${this._isArabic ? 'روابط سريعة' : 'Quick Access'}</h2>
            <div class="q-box d-flex flex-wrap justify-content-center">`;

      items.forEach(item => {
        const title = this._isArabic ? item.Title_AR || '' : item.Title || '';
        const link = this._isArabic ? item.Link_AR : item.Link_EN?.Url || '#';
        let Image: string = require('./assets/images.jpg');
        if (item.Image) {
          const imgData = JSON.parse(item.Image);
          Image = imgData.serverUrl + imgData.serverRelativeUrl;
        } else {
          Image = require('./assets/images.jpg');
        }


        html += `
          <a href="${link}" class="mx-2 text-decoration-none">
            <div class="img-container">
              <img src="${Image}" alt="${title}">
            </div>
            <h3 class="text-center head-color mt-2">${title}</h3>
          </a>`;
      });

      html += `</div></div></div></section>`;

      this.domElement.innerHTML = html;
    } catch (error) {
      console.error("Error loading Quick Access data:", error);
      this.domElement.innerHTML = `<p class="text-danger">خطأ أثناء تحميل الروابط السريعة.</p>`;
    }
  }
}
