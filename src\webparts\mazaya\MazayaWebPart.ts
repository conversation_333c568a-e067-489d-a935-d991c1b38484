import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { HttpClient, IHttpClientOptions, HttpClientResponse } from '@microsoft/sp-http';
import flatpickr from 'flatpickr';
import 'flatpickr/dist/flatpickr.min.css';

export interface IMazayaWebPartProps {
  description: string;
}

export interface Offer {
  id: string;
  title: string;
  bannerUrl: string;
  badgeTitle: string;
  discountType: string;
  amountCAP: number;
  amountPercentage: number;
  expiryDate: string;
  categoryId?: string; // إضافة categoryId
  supplier: { logo: string; name: string };
}

export default class MazayaWebPart extends BaseClientSideWebPart<IMazayaWebPartProps> {
  private token: string | null = null;
  private offers: Offer[] = [];
  private fromDate: string | null = null;
  private toDate: string | null = null;
  private currentPage: number = 1;
  private itemsPerPage: number = 10;
  private totalOffers: number = 0;
  private searchTerm: string = '';
  private categories: { id: string; name: string }[] = [];
  private suppliers: { id: string; name: string }[] = [];
  private offerTypes: string[] = [];

  private selectedCategory: string | null = null;
  private selectedSupplier: string | null = null;
  private selectedOfferType: string | null = null;

  public async render(): Promise<void> {
    this.domElement.innerHTML = `<div>Loading…</div>`;

    // تحقق من وجود categoryId في localStorage أو URL
    this.checkForCategoryFilter();

    await this.loadData();
    this.renderUI();
  }

  // تحقق من وجود فلتر category من URL parameters بس
  private checkForCategoryFilter(): void {
    try {
      // من URL parameters بس
      const urlParams = new URLSearchParams(window.location.search);
      const categoryIdFromUrl = urlParams.get('categoryId');

      if (categoryIdFromUrl) {
        this.selectedCategory = categoryIdFromUrl;
        console.log('Category filter from URL:', categoryIdFromUrl);
      }

    } catch (error) {
      console.error('Error checking category filter:', error);
    }
  }

  // محاولة مطابقة العرض مع الفئة بناءً على categoryId (إذا مكانش موجود في API)
  private matchOfferWithCategory(_offer: any, _categoryId: string): boolean {
    // بما إن مفيش localStorage، هنعتمد على الـ API بس
    // إذا الـ API مرجعش categoryId في العرض، يبقى مفيش مطابقة
    return false;
  }

  // عرض معلومات الفلتر الحالي
  private getCurrentFilterInfo(): string {
    if (!this.selectedCategory) return '';

    // جلب اسم الفئة من قائمة الفئات المحملة
    const category = this.categories.find(c => c.id === this.selectedCategory);
    const categoryName = category ? category.name : this.selectedCategory;

    return `
      <div class="alert alert-info d-flex justify-content-between align-items-center mb-3">
        <div>
          <i class="bi bi-funnel-fill me-2"></i>
          <strong>Filtered by Category:</strong> ${categoryName}
          <small class="text-muted ms-2">(${this.offers.length} offers found)</small>
        </div>
        <button class="btn btn-sm btn-outline-secondary" onclick="window.mazayaWebPart.clearCategoryFilter()">
          <i class="bi bi-x-circle me-1"></i>Clear Filter
        </button>
      </div>
    `;
  }

  // مسح فلتر الفئة
  public clearCategoryFilter(): void {
    this.selectedCategory = null;
    this.currentPage = 1;

    // إعادة تحميل الصفحة بدون categoryId في URL
    const url = new URL(window.location.href);
    url.searchParams.delete('categoryId');
    window.location.href = url.toString();
  }

  private async loadData(): Promise<void> {
    await this.login();
    if (this.token) {
      await this.fetchFilters();
      await this.fetchOffers();
    }
  }

  private initializeDatePickers(): void {
    const fromInput = this.domElement.querySelector(".form-control[aria-label='From Date']") as HTMLInputElement;
    const toInput = this.domElement.querySelector(".form-control[aria-label='To Date']") as HTMLInputElement;

    if (fromInput) {
      flatpickr(fromInput, { dateFormat: 'Y-m-d' });
    }
    if (toInput) {
      flatpickr(toInput, { dateFormat: 'Y-m-d' });
    }
  }
  private async login(): Promise<void> {
    const url = 'https://mazaya-backend.qm.org.qa/api/Account/login';
    const body = JSON.stringify({
      username: '<EMAIL>',
      password: 'Qatar@2025'
    });
    const options: IHttpClientOptions = {
      headers: { 'Content-Type': 'application/json' },
      body
    };
    const resp: HttpClientResponse = await this.context.httpClient.post(url, HttpClient.configurations.v1, options);
    if (resp.ok) {
      const data = await resp.json();
      this.token = data.accessToken || data;
    } else {
      console.error('Login failed:', resp.statusText);
    }
  }

  private async fetchOffers(): Promise<void> {
    const defaultStartDate = '2000-01-01';
    const defaultEndDate = '2099-12-31';

    const url = 'https://mazaya-backend.qm.org.qa/api/Offers/PaginatedList';
    const body = JSON.stringify({
      pageNumber: this.currentPage,
      pageSize: this.itemsPerPage,
      search: {
        name: this.searchTerm || null,
        supplierId: this.selectedSupplier || null,
        startDate: this.fromDate || defaultStartDate,
        endDate: this.toDate || defaultEndDate,
        offerId: null,
        categoryId: this.selectedCategory || null,
        offerType: this.selectedOfferType || null
      },
      orderBy: '',
      isDescending: true
    });

    const options: IHttpClientOptions = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`
      },
      body
    };

    const resp: HttpClientResponse = await this.context.httpClient.post(url, HttpClient.configurations.v1, options);
    if (resp.ok) {
      const data = await resp.json();
      let offers = data.data.items || [];

      // إذا الـ API مرجعش نتائج مفلترة بالـ categoryId، فلتر محلياً
      if (this.selectedCategory && offers.length > 0) {
        console.log('Filtering offers by categoryId:', this.selectedCategory);
        console.log('Offers before filtering:', offers.length);

        // فلترة العروض بناءً على categoryId
        offers = offers.filter((offer: any) => {
          // تحقق من وجود categoryId في العرض
          if (offer.categoryId && offer.categoryId === this.selectedCategory) {
            return true;
          }

          // إذا مفيش categoryId، جرب تطابق اسم الفئة مع supplier أو title
          if (!offer.categoryId && this.selectedCategory) {
            return this.matchOfferWithCategory(offer, this.selectedCategory);
          }

          return false;
        });

        console.log('Offers after filtering:', offers.length);
      }

      this.offers = offers;
      this.totalOffers = data.data.totalPages;
    } else {
      console.error('Fetch offers failed:', resp.statusText);
    }
  }

  private async fetchFilters(): Promise<void> {
    // جلب التصنيفات
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`
    };
    const resp1 = await this.context.httpClient.get(
      'https://mazaya-backend.qm.org.qa/api/Categories',
      HttpClient.configurations.v1,
      { headers }
    );
    if (resp1.ok) {
      const json = await resp1.json();
      this.categories = Array.isArray(json.data) ? json.data : [];
    }

    // جلب الموردين
    const resp2 = await this.context.httpClient.get(
      'https://mazaya-backend.qm.org.qa/api/Suppliers',
      HttpClient.configurations.v1,
      { headers }
    );
    if (resp2.ok) {
      const json = await resp2.json();
      this.suppliers = Array.isArray(json.data) ? json.data : [];
    }

    // جلب أنواع العروض
    const resp3 = await this.context.httpClient.get(
      'https://mazaya-backend.qm.org.qa/api/OfferType',
      HttpClient.configurations.v1,
      { headers }
    );
    if (resp3.ok) {
      const json = await resp3.json();
      this.offerTypes = Array.isArray(json.data) ? json.data.map((t: any) => t.name) : [];

    }
  }

  private renderUI(): void {
    const cards = this.offers.length > 0
      ? this.offers.map(o => `
      <div class="col-lg-4 col-md-6">
         <div class="card card-color rounded-4 overflow-hidden h-100 offer-card" data-offer-id="${o.id}" style="cursor:pointer;">
          <div class="position-relative">
           ${o.amountPercentage ? 
            `<span class="position-absolute top-0 end-0 m-2 badge bg-main text-white fs-6 rounded-pill" style="z-index:2;">${o.amountPercentage + '%'}</span>
              `
              : ''
           } 

            <img src="${o.supplier.logo || require('./assets/img.jpg')}" class="w-100 object-fit-cover  style="height:180px;" alt="${o.supplier.name}" />
            <span class="position-absolute bottom-0 start-0 px-3 py-2 bg-dark bg-opacity-75 text-white w-100" style="font-size:1rem;font-weight:600;">
              ${o.title}
            </span>
          </div>
          <div class="d-flex justify-content-between align-items-center px-3 py-2">
            <div class="d-flex align-items-center gap-2">
              <img class="rounded-5 p-1" src="${o.supplier.logo || require('./assets/img.jpg')}" alt="${o.supplier.name}" style="height:45px;" />
              <span class="fw-bold head-color" >${o.supplier.name}</span>
            </div>
              ${o.expiryDate ? `<span class="small text-color">EX Date <span class="text-pink fw-bold">${new Date(o.expiryDate).toLocaleDateString()}</span></span>` : ''}
            </div>
        </div>
      </div>
    `).join('')
      : `<div class="col-12 text-center py-5">
      <h4 class="text-muted">No offers found</h4>
    </div>`;


    const totalPages = Math.ceil(this.totalOffers / this.itemsPerPage);
    const pageNumbers: string[] = [];

    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) pageNumbers.push(i.toString());
    } else {
      pageNumbers.push('1');
      if (this.currentPage > 4) pageNumbers.push('...');
      const start = Math.max(2, this.currentPage - 1);
      const end = Math.min(totalPages - 1, this.currentPage + 1);
      for (let i = start; i <= end; i++) pageNumbers.push(i.toString());
      if (this.currentPage < totalPages - 3) pageNumbers.push('...');
      pageNumbers.push(totalPages.toString());
    }

    const pageItems = pageNumbers.map(p =>
      p === '...'
        ? `<span class="page-link">...</span>`
        : `<li class="page-item ${Number(p) === this.currentPage ? 'active' : ''}">
         <a class="page-link" href="#" data-page="${p}">${p}</a>
       </li>`
    ).join('');

    const paginationHtml = `
<nav aria-label="Marketplace pagination" class="mt-5">
  <div class="d-flex justify-content-between align-items-center">
    <button class="btn btn-outline-secondary prev-btn" ${this.currentPage === 1 ? 'disabled' : ''}>
      <i class="bi bi-chevron-left me-1"></i> Previous
    </button>
    <ul class="pagination mb-0">
      ${pageItems}
    </ul>
    <button class="btn btn-outline-secondary next-btn" ${this.currentPage === totalPages ? 'disabled' : ''}>
      Next <i class="bi bi-chevron-right ms-1"></i>
    </button>
  </div>
</nav>`;


    this.domElement.innerHTML = `
      <section class="py-4">
        <div class="row">
          ${this.getFilterHTML()}
          <div class="col-lg-9 mazaya-list">
            <div class="card card-color p-3">
              ${this.getCurrentFilterInfo()}
              <div class="d-flex justify-content-end align-items-center mb-4">
                <!-- Sort dropdown -->
              </div>
              <div class="row g-4">
                ${cards}
              </div>
              <div class="d-flex justify-content-center mt-4">
                ${paginationHtml}
              </div>
            </div>
          </div>
        </div>
      </section>
    `;
    this.domElement.querySelectorAll('.offer-card').forEach(card => {
      card.addEventListener('click', () => {
        const offerId = card.getAttribute('data-offer-id');
        if (offerId) {
          const detailUrl = `/sites/intranet-qm/SitePages/MazayaDetails.aspx?offerId=${offerId}`;
          window.location.href = detailUrl;
        }
      });
    });

    this.setupPaginationEvents();
    this.setupFilterEvents();
    this.initializeDatePickers();

    // تعيين مرجع عام للوصول للدوال
    (window as any).mazayaWebPart = this;
  }

  private setupPaginationEvents(): void {
    const totalPages = Math.ceil(this.totalOffers / this.itemsPerPage);

    this.domElement.querySelector('.prev-btn')?.addEventListener('click', async () => {
      if (this.currentPage > 1) {
        this.currentPage--;
        await this.fetchOffers();
        this.renderUI();
      }
    });

    this.domElement.querySelector('.next-btn')?.addEventListener('click', async () => {
      if (this.currentPage < totalPages) {
        this.currentPage++;
        await this.fetchOffers();
        this.renderUI();
      }
    });

    this.domElement.querySelectorAll('.page-link[data-page]').forEach(a => {
      a.addEventListener('click', async e => {
        e.preventDefault();
        const p = Number((e.target as HTMLElement).getAttribute('data-page')!);
        if (p && p !== this.currentPage) {
          this.currentPage = p;
          await this.fetchOffers();
          this.renderUI();
        }
      });
    });
  }


  private setupFilterEvents(): void {
    const searchInput = this.domElement.querySelector<HTMLInputElement>('.filters-form input[type="text"]');
    const fromDateInput = this.domElement.querySelector<HTMLInputElement>('.form-control[aria-label="From Date"]');
    const toDateInput = this.domElement.querySelector<HTMLInputElement>('.form-control[aria-label="To Date"]');
    const applyBtn = this.domElement.querySelector<HTMLButtonElement>('.filters-form button.btn-main');
    const resetBtn = this.domElement.querySelector<HTMLButtonElement>('button[type="reset"]');

    applyBtn?.addEventListener('click', async () => {
      this.searchTerm = searchInput?.value || '';
      this.fromDate = fromDateInput?.value || null;
      this.toDate = toDateInput?.value || null;
      this.currentPage = 1;
      await this.fetchOffers();
      this.renderUI();
    });

    const catSelect = this.domElement.querySelector<HTMLSelectElement>('.filter-category');
    const supSelect = this.domElement.querySelector<HTMLSelectElement>('.filter-supplier');
    const typeBtns = this.domElement.querySelectorAll<HTMLButtonElement>('.offer-type-btn');

    catSelect?.addEventListener('change', async () => {
      this.selectedCategory = catSelect.value || null;
      this.currentPage = 1;
      await this.fetchOffers(); this.renderUI();
    });

    supSelect?.addEventListener('change', async () => {
      this.selectedSupplier = supSelect.value || null;
      this.currentPage = 1;
      await this.fetchOffers(); this.renderUI();
    });

    typeBtns.forEach(btn => {
      btn.addEventListener('click', async () => {
        const t = btn.getAttribute('data-type')!;
        this.selectedOfferType = this.selectedOfferType === t ? null : t;
        this.currentPage = 1;
        await this.fetchOffers(); this.renderUI();
      });
    });

    // ✅ Reset button
    resetBtn?.addEventListener('click', async () => {
      this.searchTerm = '';
      this.fromDate = null;
      this.toDate = null;
      this.selectedCategory = null;
      this.selectedSupplier = null;
      this.selectedOfferType = null;
      this.currentPage = 1;
      await this.fetchOffers();
      this.renderUI();
    });
  }


  private getFilterHTML(): string {
    return `
      <div class="col-lg-3 mazaya-filter">
        <div class="card card-color mb-4">
          <div class="card-header bg-transparent border-bottom">
            <h5 class="mb-0 fw-bold head-color">Filters</h5>
          </div>
          <div class="card-body">
            <form class="filters-form">
              <div class="mb-3 position-relative">
                <label class="form-label fw-bold" style="font-size:1.1rem;">Search</label>
                <input type="text" class="form-control" placeholder="Search" style="font-weight:600; font-size:1.2rem;">
                <span class="position-absolute end-0 top-0 mt-3 me-3 text-muted" style="z-index:2;">
                  <i class="bi bi-search"></i>
                </span>
              </div>
              <div class="mb-3">
              <label class="form-label fw-bold" style="font-size:1.1rem;">Category</label>
              <select class="form-select filter-category">
                <option value="">All Categories</option>
                ${this.categories.map(c => `<option value="${c.id}" ${this.selectedCategory === c.id ? 'selected' : ''}>${c.name}</option>`).join('')}
              </select>
              </div>
              <div class="mb-3">
              <label class="form-label fw-bold" style="font-size:1.1rem;">Brand</label>
              <select class="form-select filter-supplier">
                <option value="">All Brand</option>
                ${this.suppliers.map(s => `<option value="${s.id}" ${this.selectedSupplier === s.id ? 'selected' : ''}>${s.name}</option>`).join('')}
              </select>
              </div>
              <div class="mb-3">
              <label class="form-label fw-bold" style="font-size:1.1rem;">Offer Type</label>
              <div class="d-grid gap-2 mt-2">
                ${this.offerTypes.map((t: string) => `
                  <button type="button" class="btn btn-outline-primary  w-100 offer-type-btn ${this.selectedOfferType === t ? 'active' : ''}" data-type="${t}">${t}</button>
                `).join('')}
              </div>
              </div>
              <div class="mb-3">
                <label class="form-label fw-bold" style="font-size:1.1rem;">Offer Date</label>
                <div class="row g-2">
                  <div class="col-6 position-relative">
                    <input type="text" class="form-control" placeholder="From" style="font-weight:600;" aria-label="From Date">
                    <span class="position-absolute end-0 top-0 mt-2 me-3 text-pink" style="z-index:2;">
                      <i class="bi bi-calendar-event"></i>
                    </span>
                  </div>
                  <div class="col-6 position-relative">
                    <input type="text" class="form-control" placeholder="To" style="font-weight:600;" aria-label="To Date">
                    <span class="position-absolute end-0 top-0 mt-2 me-3 text-pink" style="z-index:2;">
                      <i class="bi bi-calendar-event"></i>
                    </span>
                  </div>
                </div>
              </div>
              <div class="d-grid gap-2 mt-4">
                <button type="button" class="btn btn-main text-white fw-bold py-3" style="font-size:1.2rem;">Apply Filter</button>
                <button type="reset" class="btn btn-outline-secondary fw-bold py-3" style="font-size:1.2rem;">Reset</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    `;
  }
}
