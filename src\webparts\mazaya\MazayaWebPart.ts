import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { HttpClient, IHttpClientOptions, HttpClientResponse } from '@microsoft/sp-http';
import flatpickr from 'flatpickr';
import 'flatpickr/dist/flatpickr.min.css';

export interface IMazayaWebPartProps {
  description: string;
}

export interface Offer {
  id: string;
  title: string;
  bannerUrl: string;
  badgeTitle: string;
  discountType: string;
  amountCAP: number;
  amountPercentage: number;
  expiryDate: string;
  supplier: { logo: string; name: string };
}

export default class MazayaWebPart extends BaseClientSideWebPart<IMazayaWebPartProps> {
  private token: string | null = null;
  private offers: Offer[] = [];
  private fromDate: string | null = null;
  private toDate: string | null = null;
  private currentPage: number = 1;
  private itemsPerPage: number = 10;
  private totalOffers: number = 0;
  private totalPages: number = 0; // عدد الصفحات للـ suppliers
  private searchTerm: string = '';
  private categories: { id: string; name: string }[] = [];
  private suppliers: { id: string; name: string }[] = [];
  private offerTypes: string[] = [];

  private selectedCategory: string | null = null;
  private selectedSupplier: string | null = null;
  private selectedOfferId: string | null = null;
  private categorySuppliersIds: string[] = []; // IDs الـ suppliers الخاصة بالفئة المحددة
  private allCategorySuppliers: any[] = []; // جميع suppliers الفئة المحددة (cache)
  private isShowingCategorySuppliers: boolean = false; // هل يتم عرض suppliers الفئة حالياً

  public async render(): Promise<void> {
    this.domElement.innerHTML = `<div>Loading…</div>`;
    await this.loadData();
    this.renderUI();
  }

  private async loadData(): Promise<void> {
    await this.login();
    if (this.token) {
      await this.fetchFilters();
      await this.fetchOffers();
    }
  }

  private initializeDatePickers(): void {
    const fromInput = this.domElement.querySelector(".form-control[aria-label='From Date']") as HTMLInputElement;
    const toInput = this.domElement.querySelector(".form-control[aria-label='To Date']") as HTMLInputElement;

    if (fromInput) {
      flatpickr(fromInput, { dateFormat: 'Y-m-d' });
    }
    if (toInput) {
      flatpickr(toInput, { dateFormat: 'Y-m-d' });
    }
  }
  private async login(): Promise<void> {
    const url = 'https://mazaya-backend.qm.org.qa/api/Account/login';
    const body = JSON.stringify({
      username: '<EMAIL>',
      password: 'Qatar@2025'
    });
    const options: IHttpClientOptions = {
      headers: { 'Content-Type': 'application/json' },
      body
    };
    const resp: HttpClientResponse = await this.context.httpClient.post(url, HttpClient.configurations.v1, options);
    if (resp.ok) {
      const data = await resp.json();
      this.token = data.accessToken || data;
    } else {
      console.error('Login failed:', resp.statusText);
    }
  }

  private async fetchOffers(): Promise<void> {
    const defaultStartDate = null;
    const defaultEndDate = null;

    const url = 'https://mazaya-backend.qm.org.qa/api/Offers/PaginatedList';
    const body = JSON.stringify({
      pageNumber: this.currentPage,
      pageSize: this.itemsPerPage,
      search: {
        name: this.searchTerm || null,
        supplierId: this.selectedSupplier || null,
        startDate: this.fromDate || defaultStartDate,
        endDate: this.toDate || defaultEndDate,
        offerId: this.selectedOfferId || null,
      },
      orderBy: '',
      isDescending: true
    });

    const options: IHttpClientOptions = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`
      },
      body
    };

    const resp: HttpClientResponse = await this.context.httpClient.post(url, HttpClient.configurations.v1, options);
    if (resp.ok) {
      const data = await resp.json();
      let offers = data.data.items || [];

      console.log('Offers fetched from API:', offers.length);

      // مفيش فلترة هنا، الفلترة بتحصل في fetchAndDisplayCategoryOffers
      this.offers = offers;
      this.totalOffers = data.data.totalPages;
    } else {
      console.error('Fetch offers failed:', resp.statusText);
    }
  }

  private async fetchFilters(): Promise<void> {
    // جلب التصنيفات
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`
    };
    const resp1 = await this.context.httpClient.get(
      'https://mazaya-backend.qm.org.qa/api/Categories',
      HttpClient.configurations.v1,
      { headers }
    );
    if (resp1.ok) {
      const json = await resp1.json();
      this.categories = Array.isArray(json.data) ? json.data : [];
    }

    // جلب الموردين
    const resp2 = await this.context.httpClient.get(
      'https://mazaya-backend.qm.org.qa/api/Suppliers',
      HttpClient.configurations.v1,
      { headers }
    );
    if (resp2.ok) {
      const json = await resp2.json();
      this.suppliers = Array.isArray(json.data) ? json.data : [];
    }

    // جلب أنواع العروض
    const resp3 = await this.context.httpClient.get(
      'https://mazaya-backend.qm.org.qa/api/OfferType',
      HttpClient.configurations.v1,
      { headers }
    );
    if (resp3.ok) {
      const json = await resp3.json();
      this.offerTypes = Array.isArray(json.data) ? json.data : []; // جلب الـ object كامل مش الاسم بس

    }
  }

  private renderUI(): void {
    const cards = this.offers.length > 0
      ? this.offers.map(o => `
      <div class="col-lg-4 col-md-6">
         <div class="card card-color rounded-4 overflow-hidden h-100 offer-card" data-offer-id="${o.id}" style="cursor:pointer;">
          <div class="position-relative">
           ${o.amountPercentage ? 
            `<span class="position-absolute top-0 end-0 m-2 badge bg-main text-white fs-6 rounded-pill" style="z-index:2;">${o.amountPercentage + '%'}</span>
              `
              : ''
           } 

            <img src="${o.supplier.logo || require('./assets/img.jpg')}" class="w-100 object-fit-cover  style="height:180px;" alt="${o.supplier.name}" />
            <span class="position-absolute bottom-0 start-0 px-3 py-2 bg-dark bg-opacity-75 text-white w-100" style="font-size:1rem;font-weight:600;">
              ${o.title}
            </span>
          </div>
          <div class="d-flex justify-content-between align-items-center px-3 py-2">
            <div class="d-flex align-items-center gap-2">
              <img class="rounded-5 p-1" src="${o.supplier.logo || require('./assets/img.jpg')}" alt="${o.supplier.name}" style="height:45px;" />
              <span class="fw-bold head-color" >${o.supplier.name}</span>
            </div>
              ${o.expiryDate ? `<span class="small text-color">EX Date <span class="text-pink fw-bold">${new Date(o.expiryDate).toLocaleDateString()}</span></span>` : ''}
            </div>
        </div>
      </div>
    `).join('')
      : `<div class="col-12 text-center py-5">
      <h4 class="text-muted">No offers found</h4>
    </div>`;


    // حساب عدد الصفحات حسب الحالة
    const totalPages = this.isShowingCategorySuppliers ?
      this.totalPages :
      Math.ceil(this.totalOffers / this.itemsPerPage);
    const pageNumbers: string[] = [];

    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) pageNumbers.push(i.toString());
    } else {
      pageNumbers.push('1');
      if (this.currentPage > 4) pageNumbers.push('...');
      const start = Math.max(2, this.currentPage - 1);
      const end = Math.min(totalPages - 1, this.currentPage + 1);
      for (let i = start; i <= end; i++) pageNumbers.push(i.toString());
      if (this.currentPage < totalPages - 3) pageNumbers.push('...');
      pageNumbers.push(totalPages.toString());
    }

    const pageItems = pageNumbers.map(p =>
      p === '...'
        ? `<span class="page-link">...</span>`
        : `<li class="page-item ${Number(p) === this.currentPage ? 'active' : ''}">
         <a class="page-link" href="#" data-page="${p}">${p}</a>
       </li>`
    ).join('');

    const paginationHtml = `
<nav aria-label="Marketplace pagination" class="mt-5">
  <div class="d-flex justify-content-between align-items-center">
    <button class="btn btn-outline-secondary prev-btn" ${this.currentPage === 1 ? 'disabled' : ''}>
      <i class="bi bi-chevron-left me-1"></i> Previous
    </button>
    <ul class="pagination mb-0">
      ${pageItems}
    </ul>
    <button class="btn btn-outline-secondary next-btn" ${this.currentPage === totalPages ? 'disabled' : ''}>
      Next <i class="bi bi-chevron-right ms-1"></i>
    </button>
  </div>
</nav>`;


    this.domElement.innerHTML = `
      <section class="py-4">
        <div class="row">
          ${this.getFilterHTML()}
          <div class="col-lg-9 mazaya-list">
            <div class="card card-color p-3">
              <div class="d-flex justify-content-end align-items-center mb-4">
                <!-- Sort dropdown -->
              </div>
              <div class="row g-4">
                ${cards}
              </div>
              <div class="d-flex justify-content-center mt-4">
                ${paginationHtml}
              </div>
            </div>
          </div>
        </div>
      </section>
    `;
    this.domElement.querySelectorAll('.offer-card').forEach(card => {
      card.addEventListener('click', () => {
        const offerId = card.getAttribute('data-offer-id');
        if (offerId) {
          const detailUrl = `/sites/intranet-qm/SitePages/MazayaDetails.aspx?offerId=${offerId}`;
          window.location.href = detailUrl;
        }
      });
    });

    this.setupPaginationEvents();
    this.setupFilterEvents();
    this.initializeDatePickers();

    // تعيين مرجع عام للوصول للدوال
    (window as any).mazayaWebPart = this;
  }

  private setupPaginationEvents(): void {
    this.domElement.querySelector('.prev-btn')?.addEventListener('click', async () => {
      if (this.currentPage > 1) {
        this.currentPage--;

        // إذا فيه عرض suppliers، استخدم الـ pagination المحلي
        if (this.isShowingCategorySuppliers) {
          this.applySuppliersStaticPagination();
        } else {
          await this.fetchOffers();
          this.renderUI();
        }
      }
    });

    this.domElement.querySelector('.next-btn')?.addEventListener('click', async () => {
      // حساب عدد الصفحات الصحيح حسب الحالة
      const maxPages = this.isShowingCategorySuppliers ?
        this.totalPages :
        Math.ceil(this.totalOffers / this.itemsPerPage);

      if (this.currentPage < maxPages) {
        this.currentPage++;

        // إذا فيه عرض suppliers، استخدم الـ pagination المحلي
        if (this.isShowingCategorySuppliers) {
          this.applySuppliersStaticPagination();
        } else {
          await this.fetchOffers();
          this.renderUI();
        }
      }
    });

    this.domElement.querySelectorAll('.page-link[data-page]').forEach(a => {
      a.addEventListener('click', async e => {
        e.preventDefault();
        const p = Number((e.target as HTMLElement).getAttribute('data-page')!);
        if (p && p !== this.currentPage) {
          this.currentPage = p;
          await this.fetchOffers();
          this.renderUI();
        }
      });
    });
  }


  private setupFilterEvents(): void {
    const searchInput = this.domElement.querySelector<HTMLInputElement>('.filters-form input[type="text"]');
    const fromDateInput = this.domElement.querySelector<HTMLInputElement>('.form-control[aria-label="From Date"]');
    const toDateInput = this.domElement.querySelector<HTMLInputElement>('.form-control[aria-label="To Date"]');
    const applyBtn = this.domElement.querySelector<HTMLButtonElement>('.filters-form button.btn-main');
    const resetBtn = this.domElement.querySelector<HTMLButtonElement>('button[type="reset"]');

    applyBtn?.addEventListener('click', async () => {
      this.searchTerm = searchInput?.value || '';
      this.fromDate = fromDateInput?.value || null;
      this.toDate = toDateInput?.value || null;
      this.currentPage = 1;
      await this.fetchOffers();
      this.renderUI();
    });

    const catSelect = this.domElement.querySelector<HTMLSelectElement>('.filter-category');
    const supSelect = this.domElement.querySelector<HTMLSelectElement>('.filter-supplier');
    const typeBtns = this.domElement.querySelectorAll<HTMLButtonElement>('.offer-type-btn');

    catSelect?.addEventListener('change', async () => {
      this.selectedCategory = catSelect.value || null;
      this.currentPage = 1;
      await this.fetchOffers(); this.renderUI();
    });

    supSelect?.addEventListener('change', async () => {
      this.selectedSupplier = supSelect.value || null;
      this.currentPage = 1;
      await this.fetchOffers(); this.renderUI();
    });

    typeBtns.forEach(btn => {
      btn.addEventListener('click', async () => {
        const t = btn.getAttribute('data-type')!;
        this.selectedOfferId = this.selectedOfferId === t ? null : t;
        this.currentPage = 1;
        await this.fetchOffers(); this.renderUI();
      });
    });

    // ✅ Reset button
    resetBtn?.addEventListener('click', async () => {
      this.searchTerm = '';
      this.fromDate = null;
      this.toDate = null;
      this.selectedCategory = null;
      this.selectedSupplier = null;
      this.selectedOfferId = null;
      this.currentPage = 1;
      await this.fetchOffers();
      this.renderUI();
    });
  }


  private getFilterHTML(): string {
    return `
      <div class="col-lg-3 mazaya-filter">
        <div class="card card-color mb-4">
          <div class="card-header bg-transparent border-bottom">
            <h5 class="mb-0 fw-bold head-color">Filters</h5>
          </div>
          <div class="card-body">
            <form class="filters-form">
              <div class="mb-3 position-relative">
                <label class="form-label fw-bold" style="font-size:1.1rem;">Search</label>
                <input type="text" class="form-control" placeholder="Search" style="font-weight:600; font-size:1.2rem;">
                <span class="position-absolute end-0 top-0 mt-3 me-3 text-muted" style="z-index:2;">
                  <i class="bi bi-search"></i>
                </span>
              </div>
              <div class="mb-3">
              <label class="form-label fw-bold" style="font-size:1.1rem;">Category</label>
              <select class="form-select filter-category" onchange="window.mazayaWebPart.onCategoryChange(this.value)">
                <option value="">All Categories</option>
                ${this.categories.map(c => `<option value="${c.id}" ${this.selectedCategory === c.id ? 'selected' : ''}>${c.name}</option>`).join('')}
              </select>
              </div>
              <div class="mb-3">
              <label class="form-label fw-bold" style="font-size:1.1rem;">Brand</label>
              <select class="form-select filter-supplier">
                <option value="">All Brand</option>
                ${this.suppliers.map(s => `<option value="${s.id}" ${this.selectedSupplier === s.id ? 'selected' : ''}>${s.name}</option>`).join('')}
              </select>
              </div>
              <div class="mb-3">
              <label class="form-label fw-bold" style="font-size:1.1rem;">Offer Type</label>
              <div class="d-grid gap-2 mt-2">
                ${this.offerTypes.map((t: any) => `
                  <button type="button" class="btn btn-outline-primary  w-100 offer-type-btn ${this.selectedOfferId === t.id ? 'active' : ''}" data-type="${t.id}">${t.name}</button>
                `).join('')}
              </div>
              </div>
              <div class="mb-3">
                <label class="form-label fw-bold" style="font-size:1.1rem;">Offer Date</label>
                <div class="row g-2">
                  <div class="col-6 position-relative">
                    <input type="text" class="form-control" placeholder="From" style="font-weight:600;" aria-label="From Date">
                    <span class="position-absolute end-0 top-0 mt-2 me-3 text-pink" style="z-index:2;">
                      <i class="bi bi-calendar-event"></i>
                    </span>
                  </div>
                  <div class="col-6 position-relative">
                    <input type="text" class="form-control" placeholder="To" style="font-weight:600;" aria-label="To Date">
                    <span class="position-absolute end-0 top-0 mt-2 me-3 text-pink" style="z-index:2;">
                      <i class="bi bi-calendar-event"></i>
                    </span>
                  </div>
                </div>
              </div>
              <div class="d-grid gap-2 mt-4">
                <button type="button" class="btn btn-main text-white fw-bold py-3" style="font-size:1.2rem;">Apply Filter</button>
                <button type="reset" class="btn btn-outline-secondary fw-bold py-3" style="font-size:1.2rem;">Reset</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    `;
  }

  // دالة عامة لتعيين فلتر الفئة
  public async setCategoryFilter(categoryId: string): Promise<void> {
    console.log('Setting category filter:', categoryId);

    // مسح الـ cache القديم إذا كان فيه فئة مختلفة
    if (this.selectedCategory !== categoryId) {
      this.allCategorySuppliers = [];
      console.log('Cleared old category suppliers cache');
    }

    this.selectedCategory = categoryId;
    this.currentPage = 1;
    this.isShowingCategorySuppliers = false; // هيبقى true بعد جلب البيانات

    // جلب وعرض الـ suppliers الخاصة بالفئة
    await this.fetchAndDisplayCategorySuppliers(categoryId);
  }

  // دالة للتعامل مع تغيير الفئة من الـ dropdown
  public async onCategoryChange(categoryId: string): Promise<void> {
    console.log('=== Category Dropdown Changed ===');
    console.log('Selected category ID:', categoryId);

    if (!categoryId || categoryId === '') {
      console.log('No category selected, clearing filter...');
      this.clearCategoryFilter();
    } else {
      console.log('Category selected, applying filter...');
      await this.setCategoryFilter(categoryId);
    }
  }

  // جلب وعرض الـ suppliers الخاصة بفئة معينة
  private async fetchAndDisplayCategorySuppliers(categoryId: string): Promise<void> {
    try {
      console.log('Fetching suppliers for category:', categoryId);

      const url = `https://mazaya-backend.qm.org.qa/api/Suppliers/Categoires/${categoryId}`;
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`
      };

      const response = await this.context.httpClient.get(url, HttpClient.configurations.v1, { headers });

      if (response.ok) {
        const data = await response.json();
        console.log('Category suppliers response:', data);

        // حفظ الـ suppliers الكاملة
        this.allCategorySuppliers = Array.isArray(data.data) ? data.data : [];
        console.log('Total suppliers for category:', this.allCategorySuppliers.length);

        // تطبيق pagination على الـ suppliers
        this.applySuppliersStaticPagination();
        this.isShowingCategorySuppliers = true;

      } else {
        console.error('Failed to fetch category suppliers:', response.statusText);
        this.allCategorySuppliers = [];
        this.isShowingCategorySuppliers = false;
        this.renderUI();
      }

    } catch (error) {
      console.error('Error fetching category suppliers:', error);
      this.allCategorySuppliers = [];
      this.isShowingCategorySuppliers = false;
      this.renderUI();
    }
  }

  // تطبيق pagination استاتك على الـ suppliers
  private applySuppliersStaticPagination(): void {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;

    // عرض الـ suppliers بدلاً من العروض
    const suppliersToShow = this.allCategorySuppliers.slice(startIndex, endIndex);

    // حساب عدد الصفحات الصحيح
    this.totalPages = Math.ceil(this.allCategorySuppliers.length / this.itemsPerPage);
    this.totalOffers = this.allCategorySuppliers.length; // العدد الكلي للـ suppliers

    console.log(`Showing suppliers: ${suppliersToShow.length} of ${this.allCategorySuppliers.length} (Page ${this.currentPage} of ${this.totalPages})`);

    // عرض الـ suppliers في نفس مكان العروض
    this.renderSuppliersAsOffers(suppliersToShow);
  }

  // عرض الـ suppliers في شكل عروض
  private renderSuppliersAsOffers(suppliers: any[]): void {
    // تحويل الـ suppliers لشكل يشبه العروض للعرض
    this.offers = suppliers.map(supplier => ({
      id: supplier.id,
      title: supplier.name,
      description: supplier.description,
      supplier: {
        name: supplier.name,
        logo: supplier.logo
      },
      // إضافة خصائص مطلوبة للـ Offer interface
      bannerUrl: supplier.logo || '',
      badgeTitle: 'Supplier',
      discountType: 'Supplier',
      amountCAP: 0,
      discountValue: 0,
      // إضافة خصائص إضافية للعرض
      categoryName: supplier.categoryName,
      isSupplierView: true // علامة إن ده supplier مش offer
    } as any));

    this.renderUI();
  }



  // مسح فلتر الفئة
  public clearCategoryFilter(): void {
    console.log('Clearing category filter...');

    // مسح جميع البيانات المتعلقة بالفئة
    this.selectedCategory = null;
    this.categorySuppliersIds = [];
    this.allCategorySuppliers = []; // مسح الـ cache
    this.isShowingCategorySuppliers = false;
    this.currentPage = 1;

    console.log('Category filter cleared, returning to normal offers');

    // العودة للعروض العادية
    this.fetchOffers().then(() => {
      this.renderUI();
    });
  }

  // اختبار النظام الجديد
  public testCategoryFilter(categoryId: string): void {
    console.log('=== Testing Category Filter ===');
    console.log('Category ID:', categoryId);

    // تطبيق الفلتر
    this.setCategoryFilter(categoryId);

    // عرض النتائج بعد ثانية
    setTimeout(() => {
      console.log('=== Test Results ===');
      console.log('Selected category:', this.selectedCategory);
      console.log('Category suppliers IDs:', this.categorySuppliersIds);
      console.log('Filtered offers count:', this.offers.length);

      if (this.offers.length > 0) {
        console.log('Sample filtered offers:');
        this.offers.slice(0, 3).forEach((offer, index) => {
          console.log(`${index + 1}. ${offer.title} - Supplier: ${offer.supplier?.name || 'Unknown'}`);
        });
      } else {
        console.log('No offers found for this category');
      }
    }, 1000);
  }

  // عرض معلومات الفلتر الحالي
  public showCurrentFilter(): void {
    console.log('=== Current Filter Info ===');
    console.log('Selected category:', this.selectedCategory);
    console.log('Is showing category suppliers:', this.isShowingCategorySuppliers);
    console.log('Cached category suppliers:', this.allCategorySuppliers.length);
    console.log('Currently displayed items:', this.offers.length);
    console.log('Current page:', this.currentPage);
    console.log('Total pages:', this.totalOffers);

    if (this.selectedCategory) {
      console.log(`Showing suppliers for category: ${this.selectedCategory}`);
      console.log(`Total suppliers: ${this.allCategorySuppliers.length}`);
    } else {
      console.log('No category filter is currently active');
    }
  }

  // دالة لإعادة تحميل suppliers الفئة (مسح الـ cache وجلب من جديد)
  public refreshCategorySuppliers(): void {
    if (!this.selectedCategory) {
      console.log('No category selected to refresh');
      return;
    }

    console.log('Refreshing category suppliers...');
    this.allCategorySuppliers = []; // مسح الـ cache
    this.currentPage = 1;

    this.fetchAndDisplayCategorySuppliers(this.selectedCategory);
  }

  // اختبار pagination الـ suppliers
  public testSuppliersPagination(): void {
    console.log('=== Testing Suppliers Pagination ===');
    console.log('Total suppliers:', this.allCategorySuppliers.length);
    console.log('Items per page:', this.itemsPerPage);
    console.log('Total pages:', this.totalPages);
    console.log('Current page:', this.currentPage);
    console.log('Currently showing:', this.offers.length, 'suppliers');

    if (this.allCategorySuppliers.length > this.itemsPerPage) {
      console.log('Pagination should be working. Try clicking next/prev buttons.');
    } else {
      console.log('All suppliers fit in one page, no pagination needed.');
    }
  }

  // الانتقال لصفحة معينة
  public goToPage(pageNumber: number): void {
    if (this.isShowingCategorySuppliers) {
      if (pageNumber >= 1 && pageNumber <= this.totalPages) {
        this.currentPage = pageNumber;
        this.applySuppliersStaticPagination();
        console.log(`Moved to page ${pageNumber}`);
      } else {
        console.log(`Invalid page number. Valid range: 1-${this.totalPages}`);
      }
    } else {
      console.log('Not currently showing suppliers pagination');
    }
  }
}
