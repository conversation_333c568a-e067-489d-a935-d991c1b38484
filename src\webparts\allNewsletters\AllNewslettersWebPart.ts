
import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';

export interface IAllNewsletterItem {
  Id: number;
  Title: string;
  Language?: string;
  FileRef?: string;
  FileLeafRef?: string;
  Created?: string;
  Modified?: string;
  File?: {
    ServerRelativeUrl: string;
    Name: string;
  };
  Author?: {
    Title: string;
  };
}

export interface INewsletterWebPartProps {
  description: string;
}

export default class NewsletterWebPart extends BaseClientSideWebPart<INewsletterWebPartProps> {
  // private _newsletterItems: IAllNewsletterItem[] = [];
  private _filteredNewsletterItems: IAllNewsletterItem[] = [];
  private _isArabic: boolean = false;
  private _isLoading: boolean = false;
  private _searchQuery: string = '';
  private _currentPage: number = 1;
  private _itemsPerPage: number = 10;
  private _totalPages: number = 1;
  private _searchTimeout: any = null;

  public async onInit(): Promise<void> {
    await super.onInit();

    this._isArabic = this.context.pageContext.cultureInfo.currentCultureName.startsWith('ar');

    await this._loadNewsletters();
  }

  public render(): void {
    const loadingText = this._isArabic ? 'جاري التحميل...' : 'Loading...';
    const noNewslettersText = this._isArabic ? 'لا توجد نشرات إخبارية' : 'No newsletters found';
    const errorText = this._isArabic ? 'حدث خطأ أثناء تحميل البيانات' : 'Error loading data';

    this.domElement.innerHTML = `
      <div class="newsletter-section news-page">
        <div class="card section-card card-color pt-2 mt-3 bg-remover">
          <div class="container-fluid">
            <h2 class="fw-bold tittle head-color">${this._isArabic ? 'جميع النشرات الإخبارية' : 'All Newsletters'}</h2>
            ${this._renderSearchBox()}
            ${this._isLoading ? `
              <div class="loading-container">
                <div class="spinner-border" role="status"></div>
                <p class="mt-3">${loadingText}</p>
              </div>
            ` : this._renderNewsletters(noNewslettersText, errorText)}
          </div>
        </div>
      </div>
    `;

    // إضافة event listeners
    if (!this._isLoading) {
      this._attachEventListeners();
    }
  }

  private getDocumentLibraryEndpoint(libraryTitle: string, selectFields?: string, expandFields?: string, filterQuery?: string, orderQuery?: string, topQuery?: string): string {
    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    // const siteUrl = this.context.pageContext.site.absoluteUrl;
    // const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    const baseUrl = `${currentWebUrl}/_api/web/lists/getbytitle('${libraryTitle}')/items`;

    const queryParams: string[] = [];

    if (selectFields) {
      queryParams.push(`$select=${selectFields}`);
    }

    if (expandFields) {
      queryParams.push(`$expand=${expandFields}`);
    }

    if (filterQuery) {
      queryParams.push(filterQuery);
    }

    if (orderQuery) {
      queryParams.push(orderQuery);
    }

    if (topQuery) {
      queryParams.push(topQuery);
    }

  

    return queryParams.length > 0
      ? `${baseUrl}?${queryParams.join('&')}`
      : baseUrl;
  }

  private async _loadNewsletters(): Promise<void> {
    // تحميل البيانات الأساسية (بدون بحث)
    await this._performSearch();
  }

  private _calculatePagination(): void {
    this._totalPages = Math.ceil(this._filteredNewsletterItems.length / this._itemsPerPage);
    if (this._currentPage > this._totalPages) {
      this._currentPage = 1;
    }
  }

  private _renderSearchBox(): string {
    const searchPlaceholder = this._isArabic ? 'ابحث في النشرات الإخبارية...' : 'Search Newsletters...';
    const clearText = this._isArabic ? 'مسح' : 'Clear';

    return `
      
        <div class="input-group">
        <span class="input-group-text">
           <i class="bi bi-search"></i></span>
          <input
            type="text"
            class="form-control"
            placeholder="${searchPlaceholder}"
            value="${this._searchQuery}"
            id="newsletter-search-input"
          />
          <button
            class="btn btn-outline-secondary ${this._searchQuery ? 'd-block' : 'd-none'}"
            type="button"
            id="clear-search"
            title="${clearText}"
          >
            ✕
          </button>
        </div>
      
    `;
  }





  private _renderNewsletters(noNewslettersText: string, errorText: string): string {
    if (!this._filteredNewsletterItems || this._filteredNewsletterItems.length === 0) {
      return `
        <div class="no-newsletters">
          <i class="bi bi-file-earmark-pdf"></i>
          <p>${noNewslettersText}</p>
          ${errorText ? `<small class="text-danger">${errorText}</small>` : ''}
        </div>
      `;
    }

    const startIndex = (this._currentPage - 1) * this._itemsPerPage;
    const endIndex = startIndex + this._itemsPerPage;
    const currentPageItems = this._filteredNewsletterItems.slice(startIndex, endIndex);

    return `
      <div class="newsletter-grid p-3">
        ${currentPageItems.map((newsletter, index) => this._renderNewsletterCard(newsletter, startIndex + index)).join('')}
      </div>

      ${this._renderPagination()}
    `;
  }

  private _renderNewsletterCard(newsletter: IAllNewsletterItem, index?: number): string {
    const title = newsletter.Title || newsletter.FileLeafRef || 'Newsletter';
    const fileUrl = newsletter.FileRef || newsletter.File?.ServerRelativeUrl || '';

    return `
      <div class="">
        <div class="news-letter-item py-2" data-newsletter-id="${newsletter.Id}" data-file-url="${fileUrl}">
          <a href="${fileUrl}" target="_blank" class="text-decoration-none text-color">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="25" height="25"
                viewBox="0 0 25 25">
                <defs>
                    <clipPath id="clip-path">
                        <rect id="Rectangle_6580" data-name="Rectangle 6580" width="25" height="25" fill="#fff"></rect>
                    </clipPath>
                </defs>
                <g id="article-svgrepo-com" clip-path="url(#clip-path)">
                    <path id="Path_78321" data-name="Path 78321"
                        d="M5.682,19.73V1.784A.767.767,0,0,1,6.463,1H23.632a.769.769,0,0,1,.78.784v20.29a2.29,2.29,0,0,1-2.341,2.337H3.341S1,24.416,1,21.294V9.588A.767.767,0,0,1,1.78,8.8H3.341m7.024-3.122h3.9M10.365,8.8H19.73m-9.365,3.122H19.73m-9.365,3.122H19.73m-9.365,3.122H19.73"
                        transform="translate(-0.22 -0.22)" fill="none" stroke="var(--head-color)" stroke-linecap="round"
                        stroke-linejoin="round" stroke-width="2"></path>
                </g>
            </svg>
            <span>${title}</span>
          </a>
        </div>
      </div>
    `;
  }

  private _renderPagination(): string {
    if (this._totalPages <= 1) {
      return '';
    }

    const prevText = this._isArabic ? 'السابق' : 'Previous';
    const nextText = this._isArabic ? 'التالي' : 'Next';
    const firstText = this._isArabic ? 'الأولى' : 'First';
    const lastText = this._isArabic ? 'الأخيرة' : 'Last';

    let paginationHTML = `
      <div class="pagination justify-content-center flex-wrap gap-1">
    `;

    // First page button (if not on first page and there are many pages)
    if (this._currentPage > 3 && this._totalPages > 7) {
      paginationHTML += `
        <button class="pagination-btn" data-page="1" title="${firstText}">
          1
        </button>
        <span class="pagination-dots">...</span>
      `;
    }

    // Previous button
    paginationHTML += `
      <button class="page-link rounded-pill px-3" id="prev-page" ${this._currentPage === 1 ? 'disabled' : ''} title="${prevText}">
        <i class="bi bi-arrow-left"></i>
        ${prevText}
      </button>
    `;

    // Page numbers
    const maxVisiblePages = 5;
    let startPage = Math.max(1, this._currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(this._totalPages, startPage + maxVisiblePages - 1);

    // Adjust start page if we're near the end
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      paginationHTML += `
        <button class="page-link ${i === this._currentPage ? 'active' : ''}" data-page="${i}">
          ${i}
        </button>
      `;
    }

    // Next button
    paginationHTML += `
      <button class="page-link rounded-pill px-3" id="next-page" ${this._currentPage === this._totalPages ? 'disabled' : ''} title="${nextText}">
        ${nextText}
        <i class="bi bi-arrow-right"></i>
      </button>
    `;

    // Last page button (if not on last page and there are many pages)
    if (this._currentPage < this._totalPages - 2 && this._totalPages > 7) {
      paginationHTML += `
        <span class="pagination-dots">...</span>
        <button class="pagination-btn" data-page="${this._totalPages}" title="${lastText}">
          ${this._totalPages}
        </button>
      `;
    }

    paginationHTML += `
      </div>
    `;

    return paginationHTML;
  }

  private _attachEventListeners(): void {

    // Search functionality with debouncing
    const searchInput = this.domElement.querySelector('#newsletter-search-input') as HTMLInputElement;
    const clearButton = this.domElement.querySelector('#clear-search') as HTMLButtonElement;

    if (searchInput) {
      searchInput.addEventListener('input', (event) => {
        const target = event.target as HTMLInputElement;
        this._searchQuery = target.value;

        // Clear previous timeout
        if (this._searchTimeout) {
          clearTimeout(this._searchTimeout);
        }

        // Set new timeout for debouncing (300ms delay)
        this._searchTimeout = setTimeout(async () => {
          await this._performSearch();
        }, 300);

        // Update clear button visibility immediately
        const clearBtn = this.domElement.querySelector('#clear-search');
        if (clearBtn) {
          if (this._searchQuery.trim()) {
            clearBtn.classList.add('show');
          } else {
            clearBtn.classList.remove('show');
          }
        }
      });

      // Handle Enter key for immediate search
      searchInput.addEventListener('keydown', async (event) => {
        if (event.key === 'Enter') {
          event.preventDefault();
          if (this._searchTimeout) {
            clearTimeout(this._searchTimeout);
          }
          await this._performSearch();
        }
      });
    }

    if (clearButton) {
      clearButton.addEventListener('click', async () => {
        this._searchQuery = '';
        if (searchInput) {
          searchInput.value = '';
        }
        clearButton.classList.remove('show');
        await this._performSearch();
      });
    }

    // Newsletter items click functionality
    const newsletterItems = this.domElement.querySelectorAll('.news-letter-item');
    newsletterItems.forEach((item) => {
      item.addEventListener('click', (event) => {
        event.preventDefault();

        const itemElement = event.currentTarget as HTMLElement;
        const fileUrl = itemElement.getAttribute('data-file-url');
        const newsletterId = itemElement.getAttribute('data-newsletter-id');

        if (fileUrl) {
          this._downloadNewsletter(fileUrl, newsletterId || undefined);
        }
      });
    });

    // Pagination functionality
    const prevButton = this.domElement.querySelector('#prev-page');
    const nextButton = this.domElement.querySelector('#next-page');
    const pageButtons = this.domElement.querySelectorAll('[data-page]');

   

    if (prevButton) {
      prevButton.addEventListener('click', (event) => {
        event.preventDefault();
        if (this._currentPage > 1) {
          this._currentPage--;
          this.render();
        }
      });
    }

    if (nextButton) {
      nextButton.addEventListener('click', (event) => {
        event.preventDefault();
        if (this._currentPage < this._totalPages) {
          this._currentPage++;
          this.render();
        }
      });
    }

    pageButtons.forEach((button) => {
      button.addEventListener('click', (event) => {
        event.preventDefault();
        const target = event.target as HTMLButtonElement;
        const page = parseInt(target.getAttribute('data-page') || '1');


        if (page && page !== this._currentPage && page >= 1 && page <= this._totalPages) {
          this._currentPage = page;
          this.render();
        }
      });
    });
  }

  private async _performSearch(): Promise<void> {
    try {
      this._isLoading = true;
      this._currentPage = 1;
      this.render();


      const listName = 'News Letter';
      const selectFields = '*,File/ServerRelativeUrl,File/Name,Author/Title';
      const expandFields = 'File,Author';

      // بناء الـ filter query
      let filterQuery = '$filter=';

      // فلتر اللغة
      const languageFilter = this._isArabic ? "Language eq 'AR'" : "Language eq 'EN'";
      filterQuery += languageFilter;

      // إضافة البحث إذا كان موجود
      if (this._searchQuery.trim()) {
        const searchTerm = this._searchQuery.trim().replace(/'/g, "''");
        const searchFilter = `(substringof('${searchTerm}',Title) or substringof('${searchTerm}',FileLeafRef))`;
        filterQuery += ` and ${searchFilter}`;
      }

      

      const apiUrl = this.getDocumentLibraryEndpoint(listName, selectFields, expandFields, filterQuery);

      

      const response: SPHttpClientResponse = await this.context.spHttpClient.get(
        apiUrl,
        SPHttpClient.configurations.v1
      );

      if (response.ok) {
        const data = await response.json();
        this._filteredNewsletterItems = data.value || [];
        this._calculatePagination();
      } else {
        console.error('All Newsletters - Search failed:', response.status, response.statusText);
        this._filteredNewsletterItems = [];
        this._calculatePagination();
      }
    } catch (error) {
      console.error('All Newsletters - Search error:', error);
      this._filteredNewsletterItems = [];
      this._calculatePagination();
    } finally {
      this._isLoading = false;
      this.render();
    }
  }

  private _downloadNewsletter(fileUrl: string, newsletterId?: string): void {
    try {

      const fullUrl = fileUrl.startsWith('http')
        ? fileUrl
        : `${this.context.pageContext.site.absoluteUrl}${fileUrl}`;

      window.open(fullUrl, '_blank');

    } catch (error) {
      console.error('Newsletter - Error downloading file:', error);

      const link = document.createElement('a');
      link.href = fileUrl;
      link.target = '_blank';
      link.download = '';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
}
