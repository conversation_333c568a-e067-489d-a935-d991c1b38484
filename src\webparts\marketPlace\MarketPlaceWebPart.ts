import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient } from '@microsoft/sp-http';
import flatpickr from 'flatpickr';
import 'flatpickr/dist/flatpickr.min.css';
import Swal from 'sweetalert2';

export interface IInternalVacanciesWebPartProps {
  description: string;
  customPageTitle?: string;
}

interface MarketplaceItem {
  Id: number;
  Title: string;
  Title_AR: string;
  Description_EN: string;
  Description_AR: string;
  Price: number;
  Created: string;
  EMail?: string;
  Author: { EMail: string; Title: string };
  Type_EN: string;
  Type_AR: string;
  Status_EN: string,
  Status_Ar: string
  Category_EN: { Title: string };
  Category_AR: { Category_AR: string };
  AttachmentFiles: { FileName: string; ServerRelativeUrl: string }[];
  // AddToWishList: boolean;
}



export default class InternalVacanciesWebPart extends BaseClientSideWebPart<IInternalVacanciesWebPartProps> {
  private newsItems: MarketplaceItem[] = [];
  private wishlistItems: MarketplaceItem[] = [];
  private myItems: MarketplaceItem[] = [];
  private deletedAttachmentFilenames: string[] = [];
  private categoriesData: { Id: number; Title?: string; Category_AR?: string }[] = [];
  private _isArabic: boolean = false;
  private _isEditMode: boolean = false;
  private currentSortOrder: string = 'newest';
  private activeTab: string = 'all-items';

  private paginationState: {
    [key: string]: { currentPage: number; itemsPerPage: number; totalItems: number };
  } = {
      'all-items': { currentPage: 1, itemsPerPage: 10, totalItems: 0 },
      'wishlist': { currentPage: 1, itemsPerPage: 10, totalItems: 0 },
      'my-items': { currentPage: 1, itemsPerPage: 10, totalItems: 0 },
    };

  protected async onInit(): Promise<void> {
    await super.onInit();
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;
    this.manageWishlist('get');
    await Promise.all([
      this.fetchNewsData('all-items'),
      this.fetchNewsData('wishlist'),
      this.fetchNewsData('my-items'),
    ]);
    this.render(); // Render first to create DOM

  }

  private async fetchNewsData(
    tabId: string = 'all-items',
    searchText: string = '',
    fromDate?: string,
    toDate?: string,
    category?: string,
    type?: string
  ): Promise<void> {
    try {
      let filters: string[] = [];
      if (tabId === 'all-items' || tabId === 'wishlist') {
        filters.push(`(Status_EN eq 'Active' or Status_Ar eq 'متاح')`);
      }

      if (searchText) {
        const escapedSearch = searchText.replace(/'/g, "''");
        filters.push(`(substringof('${escapedSearch}', Title) or substringof('${escapedSearch}', Title_AR))`);
      }

      if (category) {
        const escapedCategory = category.replace(/'/g, "''");
        filters.push(`(Category_EN/Title eq '${escapedCategory}' or Category_AR/Category_AR eq '${escapedCategory}')`);
      }

      if (type) {
        const typeEN = type === 'جديد' ? 'New' : type === 'مستعمل' ? 'Used' : type;
        const typeAR = type === 'New' ? 'جديد' : type === 'Used' ? 'مستعمل' : type;
        filters.push(`(Type_EN eq '${typeEN}' or Type_AR eq '${typeAR}')`);
      }

      if (fromDate) {
        filters.push(`Created ge datetime'${fromDate}T00:00:00Z'`);
      }

      if (toDate) {
        const to = new Date(toDate);
        to.setHours(23, 59, 59, 999); // Set to end of day
        filters.push(`Created le datetime'${to.toISOString()}'`);
      }

      const filterQuery = filters.length > 0 ? `&$filter=${filters.join(' and ')}` : '';

      let orderQuery = '';
      switch (this.currentSortOrder) {
        case 'newest':
          orderQuery = '&$orderby=Created desc';
          break;
        case 'oldest':
          orderQuery = '&$orderby=Created asc';
          break;
        case 'price-low':
          orderQuery = '&$orderby=Price asc';
          break;
        case 'price-high':
          orderQuery = '&$orderby=Price desc';
          break;
        case 'name':
          orderQuery = '&$orderby=Title asc';
          break;
        default:
          orderQuery = '&$orderby=Created desc';
      }

      const selectFields = 'Id,Title,Title_AR,Description_EN,Description_AR,Price,Created,Author/EMail,Author/Title,Type_EN,Type_AR,Status_EN,Status_Ar,Category_EN/Title,Category_AR/Category_AR,AttachmentFiles/FileName,AttachmentFiles/ServerRelativeUrl';
      const expandFields = 'Author,Category_EN,Category_AR,AttachmentFiles';
      const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Marketplace List')/items?$select=${selectFields}&$expand=${expandFields}${filterQuery}${orderQuery}`;

      const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();

      const items: MarketplaceItem[] = data.value;

      if (tabId === 'all-items') {
        this.newsItems = items;
        this.paginationState['all-items'].totalItems = this.newsItems.length;
      } else if (tabId === 'wishlist') {
        const wishlistIds = this.manageWishlist('get');
        this.wishlistItems = items.filter((item: MarketplaceItem) => wishlistIds.includes(item.Id.toString()));
        this.paginationState['wishlist'].totalItems = this.wishlistItems.length;
      } else if (tabId === 'my-items') {
        this.myItems = items.filter((item: MarketplaceItem) =>
          item.Author?.EMail?.toLowerCase() === this.context.pageContext.user.email?.toLowerCase()
        );
        this.paginationState['my-items'].totalItems = this.myItems.length;
      }

      this.render();
    } catch (error) {
      console.error(`Error fetching data for ${tabId}:`, error);
      Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'error',
        title: this._isArabic ? 'فشل في تحميل البيانات' : 'Failed to load data',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: { popup: 'swal2-bootstrap', title: 'h6' },
      });
    }
  }

  public render(): void {
    const isArabic = this._isArabic;
    const translate = (en: string, ar: string) => (isArabic ? ar : en);

    const getItemsHtml = (items: MarketplaceItem[], tabId: string): string => {
      if (!items || items.length === 0) {
        return `
          <div class="text-center py-5">
            <i class="bi bi-info-circle display-1 text-color"></i>
            <h4 class="mt-3 head-color">${translate('No Items Found', 'لا توجد عناصر')}</h4>
            <p class="text-color">${translate('No items match the current filters or selection.', 'لا توجد عناصر تطابق الفلاتر الحالية أو الاختيار.')}</p>
          </div>
        `;
      }

      const wishlistIds = this.manageWishlist('get'); // Get wishlist from localStorage

      return items.map((item) => {
        const outlookLink = `mailto:${item.Author?.EMail || item.EMail || ''}`;
        const teamsLink = `https://teams.microsoft.com/l/chat/0/0?users=${item.Author?.EMail || item.EMail || ''}`;
        const attachments = item.AttachmentFiles || [];
        const imageAttachments = attachments.filter((att) =>
          /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(att.FileName)
        );

        let imageHtml = '';
        if (imageAttachments.length === 1) {
          imageHtml = `
            <img src="${imageAttachments[0].ServerRelativeUrl}" class="card-img-top modal-trigger" style="cursor: pointer;" data-item-id="${item.Id}" alt="${translate('Product Image', 'صورة المنتج')}">
          `;
        } else if (imageAttachments.length > 1) {
          const carouselId = `productSlider_${item.Id}_${tabId}`;
          const indicators = imageAttachments
            .map(
              (_, idx) =>
                `<button type="button" data-bs-target="#${carouselId}" data-bs-slide-to="${idx}" class="${idx === 0 ? 'active' : ''}" aria-label="${translate('Slide', 'الصورة')} ${idx + 1}" ${idx === 0 ? 'aria-current="true"' : ''}></button>`
            )
            .join('');
          const slides = imageAttachments
            .map(
              (att, idx) =>
                `<div class="carousel-item ${idx === 0 ? 'active' : ''}">
                   <img src="${att.ServerRelativeUrl}" class="d-block w-100 modal-trigger" data-item-id="${item.Id}" style="cursor: pointer;" alt="${translate('Product Image', 'صورة المنتج')} ${idx + 1}">
                 </div>`
            )
            .join('');
          imageHtml = `
            <div id="${carouselId}" class="carousel slide product-slider" data-bs-ride="carousel">
              <div class="carousel-inner">${slides}</div>
              <button class="carousel-control-prev" type="button" data-bs-target="#${carouselId}" data-bs-slide="prev" aria-label="${translate('Previous Image', 'الصورة السابقة')}">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">${translate('Previous', 'السابق')}</span>
              </button>
              <button class="carousel-control-next" type="button" data-bs-target="#${carouselId}" data-bs-slide="next" aria-label="${translate('Next Image', 'الصورة التالية')}">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">${translate('Next', 'التالي')}</span>
              </button>
              <div class="carousel-indicators">${indicators}</div>
            </div>
          `;
        } else {
          imageHtml = `<img src="${require('./assets/img.jpg')}" class="card-img-top" alt="${translate('No Image Active', 'لا توجد صورة متاحة')}">`;
        }

        const dropdownId = `shareDropdown_${item.Id}_${tabId}`;
        const actionButtons = tabId === 'my-items'
          ? `
          <button class="btn delete-item mb-5" data-id="${item.Id}" title="${translate('Delete', 'حذف')}">
          <i class="bi bi-trash main-color"></i>
          <span class="text-color">${translate('Delete', 'حذف')}</span>  
          </button>
          <button class="btn edit-item mb-5" data-id="${item.Id}" title="${translate('Edit', 'تعديل')}">
          <i class="bi bi-pencil main-color"></i>
          <span class="text-color">${translate('Edit', 'تعديل')}</span>  
          </button>
        `
          : '';
        return `
          <div class="col-12">
            <div class="card product-card h-100">
              <div class="card-img-container">
              ${imageHtml}
              <div class="product-actions">
                <button class="btn btn-sm btn-light wishlist-toggle" data-id="${item.Id}" title="${translate('Add to wishlist', 'إضافة إلى قائمة الرغبات')}">
                  <i class="bi ${wishlistIds.includes(item.Id.toString()) ? 'bi-heart-fill text-danger' : 'bi-heart'}"></i>
                </button>
              </div>
              <span class="badge bg-success position-absolute top-0 start-0 m-2">${this._isArabic ? item.Type_AR || '' : item.Type_EN || ''}</span>
              </div>
              <div class="card-body">
              <div class="d-flex justify-content-between align-items-start mb-2">
              <span class="text-color"><i class="icon-Calendar main-color"></i>${this.formatDateSimple(item.Created) || ''}</span>
                  <div class="ms-auto d-flex">${actionButtons}
                  <div class="dropdown ">
                    <a class="text-decoration-none text-color share-btn d-flex align-items-center" href="#" id="${dropdownId}" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                      <i class="bi bi-share main-color me-1"></i> ${translate('Share', 'مشاركة')}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="${dropdownId}">
                      <li><a class="dropdown-item d-flex align-items-center gap-2 send-email-btn" data-item-id="${item.Id}" href="#"><i class="bi bi-send main-color"></i> ${translate('Send', 'إرسال')}</a></li>
                      <li><a class="dropdown-item d-flex align-items-center gap-2 copy-link-btn" data-item-id="${item.Id}" href="#"><i class="bi bi-clipboard main-color"></i> ${translate('Copy Link', 'نسخ الرابط')}</a></li>
                      <li><a class="dropdown-item d-flex align-items-center gap-2" href="#"><i class="bi bi-share-fill main-color"></i> ${translate('Share to Engage', 'مشاركة على إنغيج')}</a></li>
                    </ul>
                  </div>
                  </div>
                </div>
                <h6 class="card-title fw-bold">${this._isArabic ? item.Title_AR || item.Title || '' : item.Title || ''}</h6>
                <p class="card-text text-color small">${this._isArabic ? item.Description_AR || item.Description_EN || '' : item.Description_EN || ''}</p>
                <div class="d-flex justify-content-between align-items-center">
                  <span class="h5 text-main fw-bold mb-0 head-color">${item.Price || ''} <span>QAR</span></span>
                  <div class="btn-group marketplace-actions" role="group">
                    <button class="btn btn-outline-main btn-sm marketplace-action-btn" onclick="window.location.href='${outlookLink}'">
                      <i class="bi bi-envelope me-1"></i> ${translate('Send Email', 'إرسال بريد')}
                    </button>
                    <button class="btn btn-outline-main btn-sm marketplace-action-btn" onclick="window.location.href='${teamsLink}'">
                      <i class="bi bi-chat-dots me-1"></i> ${translate('Start Chat', 'بدء محادثة')}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        `;
      }).join('');
    };

    this.domElement.innerHTML = `
      <main id="main-content" class="marketplace-page">
        <div class="row">
          ${this.getFilterSidebarHtml()}
          <div class="col-lg-9">
            <div class="card card-color p-3 mt-3">
              ${this.getMarketplaceTabsHtml()}
              <div class="tab-content" id="marketplaceTabContent">
                <div class="tab-pane fade ${this.activeTab === 'all-items' ? 'show active' : ''}" id="all-items" role="tabpanel">
                  <div class="row g-4">
                    ${getItemsHtml(this.getPagedNewsItems('all-items'), 'all-items')}
                  </div>
                  ${this.paginationState['all-items'].totalItems > 0 ? this.getPaginationHtml('all-items') : ''}
                </div>
                <div class="tab-pane fade ${this.activeTab === 'wishlist' ? 'show active' : ''}" id="wishlist" role="tabpanel">
                  <div class="row g-4">
                    ${getItemsHtml(this.getPagedNewsItems('wishlist'), 'wishlist')}
                  </div>
                  ${this.paginationState['wishlist'].totalItems > 0 ? this.getPaginationHtml('wishlist') : ''}
                </div>
                <div class="tab-pane fade ${this.activeTab === 'my-items' ? 'show active' : ''}" id="my-items" role="tabpanel">
                  <div class="row g-4">
                    ${getItemsHtml(this.getPagedNewsItems('my-items'), 'my-items')}
                  </div>
                  ${this.paginationState['my-items'].totalItems > 0 ? this.getPaginationHtml('my-items') : ''}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    `;

    // Add modal HTML
    this.domElement.innerHTML += `
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">${this._isArabic ? 'تأكيد الحذف' : 'Confirm Delete'}</h5>
            <button type="button" class="btn-close close-modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            ${this._isArabic ? 'هل أنت متأكد أنك تريد حذف هذا العنصر؟' : 'Are you sure you want to delete this item?'}
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary close-modal">${this._isArabic ? 'إلغاء' : 'Cancel'}</button>
            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">${this._isArabic ? 'حذف' : 'Delete'}</button>
          </div>
        </div>
      </div>
    </div>
    <div class="modal fade" id="itemFormModal" tabindex="-1" aria-labelledby="itemFormModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content card-color">
          <div class="modal-header border-0 pb-0">
            <h5 class="modal-title fw-bold head-color" id="itemFormModalLabel">${translate('Sell Your Item', 'بيع العنصر')}</h5>
            <button type="button" class="btn-close close-modal-btn close-modal" aria-label="Close"></button>
          </div>
          <div class="modal-body px-4 py-3">
            <form id="itemForm">
              <input type="hidden" id="itemId">
              
              <!-- Title (English) -->
              <div class="mb-1">
                <label for="title" class="form-label fw-semibold text-color-gray">${translate('Title (English)', 'العنوان (إنجليزي)')}<span class="text-danger">*</span></label>
                <textarea class="form-control" id="title" rows="3" placeholder="${translate('Enter item title...', 'أدخل عنوان العنصر...')}" required></textarea>
              </div>
  
              <!-- Title (Arabic) -->
              <div class="mb-3">
                <label for="Title_AR" class="form-label fw-semibold text-color-gray">${translate('Title (Arabic)', 'العنوان (عربي)')}<span class="text-danger"> * </span></label>
                <textarea class="form-control" id="Title_AR" rows="3" placeholder="${translate('Enter item title...', 'أدخل عنوان العنصر...')}" required></textarea>
              </div>
  
              <!-- Description (English) -->
              <div class="mb-1">
                <label for="description_EN" class="form-label fw-semibold text-color-gray">${translate('Description (English)', 'الوصف (إنجليزي)')}<span class="text-danger"> * </span></label>
                <textarea class="form-control" id="description_EN" rows="4" required placeholder="${translate('Enter item description...', 'أدخل وصف العنصر...')}"></textarea>
              </div>
  
              <!-- Description (Arabic) -->
              <div class="mb-3">
                <label for="description_AR" class="form-label fw-semibold text-color-gray">${translate('Description (Arabic)', 'الوصف (عربي)')}<span class="text-danger"> * </span></label>
                <textarea class="form-control" id="description_AR" rows="4" required placeholder="${translate('Enter item description...', 'أدخل وصف العنصر...')}"></textarea>
              </div>
  
              <!-- Category and Price Row -->
              <div class="row mb-3">
                <div class="col-md-6 mb-3 mb-md-0">
                  <label for="category" class="form-label fw-semibold text-color-gray">${translate('Category', 'الفئة')}<span class="text-danger"> * </span></label>
                  <select class="form-select" id="category" required>
                    <option value="">${translate('Select', 'اختر')}</option>
                  </select>
                </div>
                <div class="col-md-6">
                  <label for="price" class="form-label fw-semibold text-color-gray">${translate('Price (QAR)', 'السعر (ريال قطري)')}<span class="text-danger"> * </span></label>
                  <div class="input-group">
                    <input type="number" class="form-control" id="price" placeholder="0.00" min="0" step="0.01" required>
                    <span class="input-group-text">QAR</span>
                  </div>
                </div>
              </div>
  
              <!-- Type and Status Row -->
              <div class="row mb-3">
                <div class="col-md-6">
                  <label class="form-label fw-semibold text-color-gray">${translate('Type', 'النوع')}<span class="text-danger"> * </span></label>
                  <div class="row g-2">
                    <div class="col-6">
                      <input class="btn-check" type="radio" name="item_type" id="item_type_new" value="new" required>
                      <label class="btn btn-outline-secondary w-100 custom-radio-btn" for="item_type_new">${translate('New', 'جديد')}</label>
                    </div>
                    <div class="col-6">
                      <input class="btn-check" type="radio" name="item_type" id="item_type_used" value="used">
                      <label class="btn btn-outline-secondary w-100 custom-radio-btn" for="item_type_used">${translate('Used', 'مستعمل')}</label>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-semibold text-color-gray">${translate('Status', 'الحالة')}<span class="text-danger"> * </span></label>
                  <div class="row g-2">
                    <div class="col-6">
                      <input class="btn-check" type="radio" name="item_status" id="item_status_active" value="active" required>
                      <label class="btn btn-outline-secondary w-100 custom-radio-btn" for="item_status_active">${translate('Active', 'متاح')}</label>
                    </div>
                    <div class="col-6">
                      <input class="btn-check" type="radio" name="item_status" id="item_status_sold" value="sold">
                      <label class="btn btn-outline-secondary w-100 custom-radio-btn" for="item_status_sold">${translate('Sold', 'تم البيع')}</label>
                    </div>
                  </div>
                </div>
              </div>
  
              <!-- Upload Item Images -->
              <div class="mb-4">
                <label class="form-label fw-semibold text-color-gray">${translate('Upload Item Images', 'تحميل صور العنصر')}${this._isEditMode ? '' : '<span class="text-danger">*</span>'}</label>
                <div class="upload-area border border-2 border-dashed rounded p-4 text-center">
                  <div id="uploadPlaceholder">
                    <div class="upload-icon mb-3">
                      <i class="bi bi-cloud-upload text-main" style="font-size: 2rem;"></i>
                    </div>
                    <p class="mb-2 text-color">
                      <span class="fw-semibold text-main">${translate('Upload files', 'تحميل الملفات')}</span>
                    </p>
                    <input type="file" class="form-control d-none" id="itemImages" multiple accept="image/*" ${this._isEditMode ? '' : 'required'}>
                    <label for="itemImages" class="btn btn-outline-main btn-sm upload-btn">
                      <i class="bi bi-plus-circle me-1"></i>${translate('Upload files', 'تحميل الملفات')}
                    </label>
                  </div>
                  <div id="currentAttachments" class="row g-2 mt-2" style="display: none;"></div>
                  <div id="uploadedImagesContainer" class="row g-2 mt-2" style="display: none;"></div>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-2">
                  <small class="text-color">${translate('You can upload multiple images (JPG, PNG, GIF)', 'يمكنك تحميل صور متعددة (JPG, PNG, GIF)')}</small>
                  <button type="button" id="addMoreImages" class="btn btn-sm btn-outline-main" style="display: none;">
                    <i class="bi bi-plus-circle me-1"></i>${translate('Add more', 'إضافة المزيد')}
                  </button>
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer border-0 pt-0 px-4 pb-4">
            <button type="button" class="btn btn-outline-secondary px-4 close-modal close-modal-btn">${translate('Cancel', 'إلغاء')}</button>
            <button type="button" class="btn btn-main text-white px-4" id="saveItemBtn">${translate('Save', 'حفظ')}</button>
          </div>
        </div>
      </div>
    </div>


    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
      <div class="modal-content bg-transparent border-0">
        <div class="modal-header border-0 position-absolute top-0 end-0 z-3">
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
            <i class="bi bi-x-lg icon-md text-white"></i>
          </button>
        </div>
        <div class="modal-body p-0">
          <!-- Modal Image Slider -->
          <div id="modalSlider" class="carousel slide" data-bs-ride="false">
            <div class="carousel-inner" id="modalCarouselInner">
              <!-- Images will be populated dynamically -->
            </div>
            <!-- Modal Slider Controls -->
            <button class="carousel-control-prev modal-control" type="button" data-bs-target="#modalSlider" data-bs-slide="prev">
              <span class="carousel-control-prev-icon" aria-hidden="true"></span>
              <span class="visually-hidden">${translate('Previous', 'السابق')}</span>
            </button>
            <button class="carousel-control-next modal-control" type="button" data-bs-target="#modalSlider" data-bs-slide="next">
              <span class="carousel-control-next-icon" aria-hidden="true"></span>
              <span class="visually-hidden">${translate('Next', 'التالي')}</span>
            </button>
            <!-- Modal Slide Indicators -->
            <div class="carousel-indicators modal-indicators" id="modalCarouselIndicators">
              <!-- Indicators will be populated dynamically -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
    `;
    this.domElement.innerHTML += `
    <style>
      #imageModal .modal-header {
        z-index: 1055;
      }
      #imageModal .btn-close {
        padding: 0.5rem;
        background: none;
        opacity: 1;
      }
      #imageModal .btn-close:hover {
        opacity: 0.7;
      }
      #imageModal .btn-close i.bi-x-lg {
        font-size: 1.5rem;
      }
    </style>
  `;


    // Attach delete item listeners
    this.domElement.querySelectorAll('.delete-item').forEach((button: HTMLElement) => {
      button.addEventListener('click', async (e) => {
        e.preventDefault();
        const itemId = button.getAttribute('data-id');
        if (itemId) {
          const modalElement = this.domElement.querySelector('#deleteConfirmModal') as HTMLDivElement;
          if (modalElement) {
            modalElement.classList.add('show');
            modalElement.style.display = 'block';
            modalElement.setAttribute('aria-hidden', 'false');
            modalElement.setAttribute('aria-modal', 'true');
            modalElement.setAttribute('role', 'dialog');
            document.body.classList.add('modal-open');

            let backdrop = document.querySelector('.modal-backdrop') as HTMLDivElement;
            if (!backdrop) {
              backdrop = document.createElement('div');
              backdrop.className = 'modal-backdrop fade show';
              document.body.appendChild(backdrop);
            }

            const confirmBtn = modalElement.querySelector('#confirmDeleteBtn') as HTMLButtonElement;
            if (confirmBtn) {
              const handler = async () => {
                confirmBtn.removeEventListener('click', handler);
                await this.deleteItem(itemId);
                this.closeModal(modalElement);
              };
              confirmBtn.addEventListener('click', handler);
            }
          }
        }
      });
    });

    // Close modal listeners
    const closeButtons = this.domElement.querySelectorAll('.close-modal');
    closeButtons.forEach((btn: HTMLElement) => {
      btn.addEventListener('click', () => {
        const modal = btn.closest('.modal') as HTMLDivElement;
        if (modal) {
          this.closeModal(modal);
        }
      });
    });

    document.addEventListener('click', (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (target.classList.contains('modal') && (target.id === 'itemFormModal' || target.id === 'deleteConfirmModal' || target.id === 'imageModal')) {
        this.closeModal(target as HTMLDivElement);
      }
    });
    // Image modal trigger
    this.domElement.querySelectorAll('.modal-trigger').forEach((trigger: HTMLElement) => {
      trigger.addEventListener('click', (e: Event) => {
        e.preventDefault();
        const itemId = trigger.getAttribute('data-item-id');
        if (!itemId) {
          console.error('No item ID found on modal trigger');
          return;
        }

        const modalElement = this.domElement.querySelector('#imageModal') as HTMLDivElement;
        if (!modalElement) {
          console.error('Image modal (#imageModal) not found in DOM');
          Swal.fire({
            toast: true,
            position: this._isArabic ? 'top-start' : 'top-end',
            icon: 'error',
            title: this._isArabic ? 'فشل في فتح معرض الصور' : 'Failed to open image gallery',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            customClass: { popup: 'swal2-bootstrap', title: 'h6' },
          });
          return;
        }

        this.populateImageModal(itemId);
        modalElement.classList.add('show');
        modalElement.style.display = 'block';
        modalElement.setAttribute('aria-hidden', 'false');
        modalElement.setAttribute('aria-modal', 'true');
        modalElement.setAttribute('role', 'dialog');
        document.body.classList.add('modal-open');

        let backdrop = document.querySelector('.modal-backdrop') as HTMLDivElement;
        if (!backdrop) {
          backdrop = document.createElement('div');
          backdrop.className = 'modal-backdrop fade show';
          document.body.appendChild(backdrop);
        }
      });
    });
    this.addEventListeners();
    this.loadCategories();
    this.loadModalCategories();
  }


  private addEventListeners(): void {
    const tabs = this.domElement.querySelectorAll('.marketplace-tabs button[data-bs-toggle="tab"]');
    tabs.forEach((tab: HTMLElement) => {
      tab.addEventListener('click', (e: Event) => {
        e.preventDefault();
        const targetId = tab.getAttribute('data-bs-target')?.substring(1);
        if (targetId && targetId !== this.activeTab) {
          this.activeTab = targetId;
          this.paginationState[targetId].currentPage = 1;
        }
      });
    });

    const paginationContainer = this.domElement.querySelector('#main-content');
    if (paginationContainer) {
      paginationContainer.addEventListener('click', (event: Event) => {
        const target = event.target as HTMLElement;
        const pageLink = target.closest('.page-link');
        if (pageLink) {
          event.preventDefault();
          const tabId = pageLink.getAttribute('data-tab-id');
          if (!tabId || tabId === 'my-items') return;

          const pageText: any = pageLink.textContent?.trim().toLowerCase();
          const { currentPage, totalItems, itemsPerPage } = this.paginationState[tabId];
          let newPage: number;

          if (pageText === 'next' || pageText === 'التالي') {
            newPage = currentPage + 1;
          } else if (pageText === 'previous' || pageText === 'السابق') {
            newPage = currentPage - 1;
          } else {
            newPage = parseInt(pageText);
          }

          if (!isNaN(newPage) && newPage > 0 && newPage <= Math.ceil(totalItems / itemsPerPage)) {
            this.paginationState[tabId].currentPage = newPage;
            this.render();
          }
        }
      });
    }

    const sortItems = this.domElement.querySelectorAll('.dropdown-menu [data-value]');
    sortItems.forEach((item: HTMLElement) => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        const order = item.getAttribute('data-value');
        if (order) {
          this.changeSortOrder(order);
        }
      });
    });

    this.domElement.querySelectorAll('.wishlist-toggle').forEach((button: HTMLElement) => {
      button.addEventListener('click', async (e) => {
        e.preventDefault();
        e.stopPropagation();
        const itemId = button.getAttribute('data-id');
        if (itemId) {
          await this.toggleWishlistStatus(itemId);
        }
      });
    });

    this.addFilterListeners();
    this.initializeDatePickers();

    this.domElement.querySelectorAll('.send-email-btn').forEach((btn: HTMLElement) => {
      btn.addEventListener('click', (e: Event) => {
        e.preventDefault();
        const itemId = btn.getAttribute('data-item-id');
        if (itemId) {
          this.sendEmail(`${this.context.pageContext.web.absoluteUrl}/lists/Marketplace%20List/DispForm.aspx?ID=${itemId}`);
        }
      });
    });

    this.domElement.querySelectorAll('.copy-link-btn').forEach((btn: HTMLElement) => {
      btn.addEventListener('click', (e: Event) => {
        e.preventDefault();
        const itemId = btn.getAttribute('data-item-id');
        if (itemId) {
          this.copyLink(`${this.context.pageContext.web.absoluteUrl}/lists/Marketplace%20List/DispForm.aspx?ID=${itemId}`);
        }
      });
    });
    this.domElement.querySelectorAll('.edit-item').forEach((button: HTMLElement) => {
      button.addEventListener('click', (e: Event) => {
        e.preventDefault();
        const itemId = button.getAttribute('data-id');
        if (itemId) {
          this.openEditModal(itemId);
        }
      });
    });
    // Form submission

    const saveItemBtn = this.domElement.querySelector('#saveItemBtn') as HTMLElement;
    if (saveItemBtn) {
      saveItemBtn.addEventListener('click', async (e) => {
        e.preventDefault();
        await this.handleFormSubmit();
      });
    }

    const itemImagesInput = this.domElement.querySelector('#itemImages') as HTMLInputElement;
    const uploadPlaceholder = this.domElement.querySelector('#uploadPlaceholder') as HTMLDivElement;
    const uploadedImagesContainer = this.domElement.querySelector('#uploadedImagesContainer') as HTMLDivElement;
    const addMoreImagesBtn = this.domElement.querySelector('#addMoreImages') as HTMLButtonElement;
    const currentAttachmentsDiv = this.domElement.querySelector('#currentAttachments') as HTMLDivElement;

    if (itemImagesInput && uploadPlaceholder && uploadedImagesContainer && addMoreImagesBtn) {
      itemImagesInput.addEventListener('change', (e: Event) => {
        const files = (e.target as HTMLInputElement).files;
        if (files && files.length > 0) {
          // Hide placeholder, show container and add more button
          uploadPlaceholder.style.display = 'none';
          uploadedImagesContainer.style.display = 'flex';
          addMoreImagesBtn.style.display = 'block';

          // Display selected images
          Array.from(files).forEach((file) => {
            const imageCol = document.createElement('div');
            imageCol.className = 'col-4 col-md-3 mb-2';
            imageCol.dataset.filename = file.name;

            const imagePreview = document.createElement('div');
            imagePreview.className = 'position-relative';

            const img = document.createElement('img');
            img.className = 'img-thumbnail w-100';
            img.style.height = '80px';
            img.style.objectFit = 'cover';

            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'btn btn-sm btn-danger position-absolute top-0 end-0';
            deleteBtn.innerHTML = '<i class="bi bi-x"></i>';
            deleteBtn.style.padding = '0.1rem 0.3rem';
            deleteBtn.style.padding = '0.7rem';

            deleteBtn.addEventListener('click', () => {
              Swal.fire({
                title: this._isArabic ? 'تأكيد الحذف' : 'Confirm Deletion',
                text: this._isArabic ? 'هل أنت متأكد أنك تريد حذف هذه الصورة؟' : 'Are you sure you want to delete this image?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: this._isArabic ? 'حذف' : 'Delete',
                cancelButtonText: this._isArabic ? 'إلغاء' : 'Cancel',
                customClass: { popup: 'swal2-bootstrap' },
              }).then((result) => {
                if (result.isConfirmed) {
                  imageCol.remove();
                  if (uploadedImagesContainer.children.length === 0 && (!currentAttachmentsDiv || currentAttachmentsDiv.children.length === 0)) {
                    uploadPlaceholder.style.display = 'block';
                    uploadedImagesContainer.style.display = 'none';
                    addMoreImagesBtn.style.display = 'none';
                  }
                  // Reset file input to allow re-uploading the same file
                  itemImagesInput.value = '';
                }
              });
            });

            const reader = new FileReader();
            reader.onload = (ev) => {
              if (ev.target?.result) {
                img.src = ev.target.result as string;
              }
            };
            reader.readAsDataURL(file);

            imagePreview.appendChild(img);
            imagePreview.appendChild(deleteBtn);
            imageCol.appendChild(imagePreview);
            uploadedImagesContainer.appendChild(imageCol);
          });
        }
      });

      addMoreImagesBtn.addEventListener('click', () => {
        itemImagesInput.click();
      });
    }


    this.domElement.querySelectorAll('[data-bs-dismiss="modal"]').forEach((btn: HTMLElement) => {
      btn.addEventListener('click', () => {
        const modal = btn.closest('.modal') as HTMLDivElement;
        if (modal) {
          this.closeModal(modal);
        }
      });
    });


    // Modal show event to reset form
    // In addEventListeners method, replace the existing itemFormModal show event listener
    const itemFormModal = this.domElement.querySelector('#itemFormModal') as HTMLElement;
    if (itemFormModal) {
      itemFormModal.addEventListener('show.bs.modal', (e: Event) => {
        const relatedTarget = (e as any).relatedTarget as HTMLElement;
        if (relatedTarget && relatedTarget.id === 'addItemBtn') {
          this._isEditMode = false;
          this.resetForm();
        }
      });
    }


    const clearWishlistBtn = this.domElement.querySelector('#clearWishlistBtn') as HTMLButtonElement;
    if (clearWishlistBtn) {
      clearWishlistBtn.addEventListener('click', () => {
        Swal.fire({
          title: this._isArabic ? 'تأكيد مسح قائمة الرغبات' : 'Confirm Clear Wishlist',
          text: this._isArabic ? 'هل أنت متأكد أنك تريد مسح جميع العناصر من قائمة الرغبات؟' : 'Are you sure you want to clear all items from your wishlist?',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonText: this._isArabic ? 'مسح' : 'Clear',
          cancelButtonText: this._isArabic ? 'إلغاء' : 'Cancel',
          customClass: { popup: 'swal2-bootstrap' },
        }).then((result) => {
          if (result.isConfirmed) {
            this.clearWishlist();
          }
        });
      });
    }
  }

  private initializeDatePickers(): void {
    const fromInput = this.domElement.querySelector(".form-control[aria-label='From Date']") as HTMLInputElement;
    const toInput = this.domElement.querySelector(".form-control[aria-label='To Date']") as HTMLInputElement;

    if (fromInput) {
      flatpickr(fromInput, { dateFormat: 'Y-m-d' });
    }
    if (toInput) {
      flatpickr(toInput, { dateFormat: 'Y-m-d' });
    }
  }
  private closeModal(modalElement: HTMLDivElement): void {
    modalElement.classList.remove('show');
    modalElement.style.display = 'none';
    modalElement.setAttribute('aria-hidden', 'true');
    modalElement.removeAttribute('aria-modal');
    modalElement.removeAttribute('role');
    document.body.classList.remove('modal-open');

    const backdrop = document.querySelector('.modal-backdrop') as HTMLDivElement;
    if (backdrop) {
      backdrop.remove();
    }
  }
  private addFilterListeners(): void {
    const fromDateInput = this.domElement.querySelector("input[aria-label='From Date']") as HTMLInputElement;
    const toDateInput = this.domElement.querySelector("input[aria-label='To Date']") as HTMLInputElement;
    const applyBtn = this.domElement.querySelector('.btn.fw-bold.text-white') as HTMLButtonElement;
    const resetBtn = this.domElement.querySelector('.btn.btn-outline-dark') as HTMLButtonElement;
    const positionInput = this.domElement.querySelector('#positionSearch') as HTMLInputElement;
    const categoryBtn = this.domElement.querySelector('#categoryDropdownBtn') as HTMLElement;
  
    // Track selected type
    let selectedType: string = '';
  
    const typeButtons = this.domElement.querySelectorAll('.btn-outline-secondary.fw-bold');
    typeButtons.forEach((btn: HTMLElement) => {
      btn.addEventListener('click', () => {
        typeButtons.forEach((b) => b.classList.remove('active'));
        btn.classList.add('active');
        selectedType = btn.getAttribute('data-value') || btn.textContent?.trim() || '';
      });
    });
  
    if (applyBtn) {
      applyBtn.addEventListener('click', () => {
        const fromDate = fromDateInput?.value || '';
        const toDate = toDateInput?.value || '';
        const position = positionInput?.value.trim() || '';
        const categorySpan = categoryBtn?.querySelector('span') as HTMLElement;
        const category = categorySpan?.textContent && !['Select', 'اختر'].includes(categorySpan.textContent.trim()) ? categorySpan.textContent.trim() : '';
  
        // Normalize type value to match list values
        const typeMap: { [key: string]: string } = {
          'New': 'New',
          'جديد': 'جديد',
          'Used': 'Used',
          'مستعمل': 'مستعمل'
        };
        const type = typeMap[selectedType] || '';
  
        // Apply filters to the active tab
        this.paginationState[this.activeTab].currentPage = 1; // Reset to first page
        this.fetchNewsData(this.activeTab, position, fromDate, toDate, category, type);
      });
    }
  
    if (resetBtn) {
      resetBtn.addEventListener('click', () => {
        // Clear inputs
        if (fromDateInput) fromDateInput.value = '';
        if (toDateInput) toDateInput.value = '';
        if (positionInput) positionInput.value = '';
        if (categoryBtn) {
          const span = categoryBtn.querySelector('span') as HTMLElement;
          if (span) span.textContent = this._isArabic ? 'اختر' : 'Select';
        }
        typeButtons.forEach((b) => b.classList.remove('active'));
        selectedType = '';
  
        // Reset data
        this.paginationState[this.activeTab].currentPage = 1;
        this.fetchNewsData(this.activeTab);
      });
    }
  }

  private async toggleWishlistStatus(itemId: string): Promise<void> {
    try {
      const wishlistIds = this.manageWishlist('get');
      const isWishlisted = wishlistIds.includes(itemId);
      const newStatus = !isWishlisted;

      // Update localStorage
      if (newStatus) {
        this.manageWishlist('add', itemId);
      } else {
        this.manageWishlist('remove', itemId);
      }

      // Update wishlistItems
      const item = this.newsItems.find((i) => i.Id.toString() === itemId);
      if (item) {
        if (newStatus) {
          if (!this.wishlistItems.some((i) => i.Id.toString() === itemId)) {
            this.wishlistItems.push(item);
          }
        } else {
          this.wishlistItems = this.wishlistItems.filter((i) => i.Id.toString() !== itemId);
        }
        this.paginationState['wishlist'].totalItems = this.wishlistItems.length;
      }

      // Update UI
      this.updateWishlistUI(itemId, newStatus);

      const message = this._isArabic
        ? (newStatus ? 'تمت الإضافة إلى المفضلة' : 'تمت الإزالة من المفضلة')
        : (newStatus ? 'Added to favorites' : 'Removed from favorites');

      Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'success',
        title: message,
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: {
          popup: 'swal2-bootstrap',
          title: 'h6',
        },
      });

      // Re-render to update wishlist tab if active
      if (this.activeTab === 'wishlist') {
        this.render();
      }
    } catch (error) {
      console.error('Error in toggleWishlistStatus:', error);
      Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'error',
        title: this._isArabic ? 'فشل في تحديث المفضلة' : 'Failed to update wishlist',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: {
          popup: 'swal2-bootstrap',
          title: 'h6',
        },
      });
    }
  }

  private updateWishlistUI(itemId: string, newStatus: boolean): void {
    const heartIcon = this.domElement.querySelector(`.wishlist-toggle[data-id="${itemId}"] i`) as HTMLElement;
    if (heartIcon) {
      heartIcon.className = newStatus ? 'bi bi-heart-fill text-danger' : 'bi bi-heart';
    }
  }

  private getFilterSidebarHtml(): string {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);

    return `
      <div class="col-lg-3 mt-3">
        <div class="card card-color mb-4">
          <div class="card-body">
            <h3 class="fw-bold head-color mb-3">${t('Filters', 'الفلترة')}</h3>
            <hr>
            <div class="mb-3">
              <label for="positionSearch" class="form-label">${t('Item Name', 'اسم العنصر')}</label>
              <div class="input-group">
                <input type="text" class="form-control" id="positionSearch" placeholder="${t('Search', 'ابحث')}">
                <button class="btn btn-outline-secondary" type="button">
                  <i class="bi bi-search"></i>
                </button>
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label head-color">${t('Category', 'الفئة')}</label>
              <div class="dropdown w-100" data-bs-auto-close="outside">
                <button class="btn btn-outline-secondary border btn-sm dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center" type="button" id="categoryDropdownBtn" data-bs-toggle="dropdown" aria-expanded="false">
                  <span>${t('Select', 'اختر')}</span>
                </button>
                <ul class="dropdown-menu w-100" id="categoryDropdownMenu"></ul>
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label head-color">${t('Type', 'النوع')}</label>
              <div class="d-flex gap-2">
                <button type="button" class="btn btn-outline-secondary w-100 fw-bold" data-value="${t('New', 'جديد')}">${t('New', 'جديد')}</button>
                <button type="button" class="btn btn-outline-secondary w-100 fw-bold" data-value="${t('Used', 'مستعمل')}">${t('Used', 'مستعمل')}</button>
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label head-color">${t('Create Date', 'تاريخ الإنشاء')}</label>
              <div class="input-group mb-2">
                <input type="text" class="form-control datepicker flatpickr-input" placeholder="${t('From', 'من')}" aria-label="From Date">
                <span class="input-group-text"><i class="bi bi-calendar"></i></span>
              </div>
              <div class="input-group">
                <input type="text" class="form-control datepicker flatpickr-input" placeholder="${t('To', 'إلى')}" aria-label="To Date">
                <span class="input-group-text"><i class="bi bi-calendar"></i></span>
              </div>
            </div>
            <div class="d-grid gap-2 mt-4">
              <button type="button" class="btn fw-bold text-white " >${t('Apply Filter', 'تطبيق الفلتر')}</button>
              <button type="button" class="btn btn-outline-dark fw-bold">${t('Reset', 'إعادة تعيين')}</button>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private getMarketplaceTabsHtml(): string {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);
    return `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <ul class="nav nav-tabs marketplace-tabs" id="marketplaceTabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link ${this.activeTab === 'all-items' ? 'active' : ''}" id="all-items-tab" data-bs-toggle="tab" data-bs-target="#all-items" type="button" role="tab">
              ${t('All Items', 'كل العناصر')}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link ${this.activeTab === 'wishlist' ? 'active' : ''}" id="wishlist-tab" data-bs-toggle="tab" data-bs-target="#wishlist" type="button" role="tab">
              ${t('Wishlist', 'قائمة الرغبات')}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link ${this.activeTab === 'my-items' ? 'active' : ''}" id="my-items-tab" data-bs-toggle="tab" data-bs-target="#my-items" type="button" role="tab">
              ${t('My Items', 'عناصري')}
            </button>
          </li>
        </ul>
        <div class="d-flex align-items-center gap-3 flex-wrap">
          <div class="dropdown ms-2" data-bs-auto-close="outside">
            <button class="btn dropdown-toggle head-color" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
              ${t('Sort by: Newest', 'الترتيب حسب: الأحدث')}
            </button>
            <ul class="dropdown-menu" aria-labelledby="sortDropdown">
              <li><a class="dropdown-item" href="#" data-value="newest">${t('Create Date (Newest)', 'تاريخ الإنشاء (الأحدث)')}</a></li>
              <li><a class="dropdown-item" href="#" data-value="oldest">${t('Create Date (Oldest)', 'تاريخ الإنشاء (الأقدم)')}</a></li>
              <li><a class="dropdown-item" href="#" data-value="price-low">${t('Price (Low to High)', 'السعر (منخفض إلى مرتفع)')}</a></li>
              <li><a class="dropdown-item" href="#" data-value="price-high">${t('Price (High to Low)', 'السعر (مرتفع إلى منخفض)')}</a></li>
              <li><a class="dropdown-item" href="#" data-value="name">${t('Name (A-Z)', 'الاسم (أ-ي)')}</a></li>
            </ul>
          </div>
          <button class="btn btn-main text-white px-4 py-2" id="addItemBtn" data-bs-toggle="modal" data-bs-target="#itemFormModal">
            <i class="bi bi-plus-circle me-2"></i>
            ${t('Sell Your Item', 'بيع العنصر الخاص بك')}
          </button>
          ${this.activeTab === 'wishlist' ? `
            <button class="btn btn-outline-danger px-4 py-2" id="clearWishlistBtn">
              <i class="bi bi-trash me-2"></i>
              ${t('Clear Wishlist', 'مسح قائمة الرغبات')}
            </button>
          ` : ''}
        </div>
      </div>
    `;
  }

  getMyItemsTabHtml(): string {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);
    return `
      <div class="text-center py-5">
        <i class="bi bi-bag display-1 text-color"></i>
        <h4 class="mt-3 head-color">${t('No Items Listed', 'لا توجد عناصر مدرجة')}</h4>
        <p class="text-color">${t("You haven't listed any items for sale yet.", 'لم تقم بإدراج أي عناصر للبيع حتى الآن.')}</p>
        <button class="btn btn-main text-white">
          <i class="bi bi-plus-circle me-1"></i>
          ${t('List Your First Item', 'سرد العنصر الأول')}
        </button>
      </div>
    `;
  }

  private getPagedNewsItems(tabId: string): MarketplaceItem[] {
    const { currentPage, itemsPerPage } = this.paginationState[tabId];
    const startIndex = (currentPage - 1) * itemsPerPage;
    const items =
      tabId === 'all-items' ? this.newsItems :
        tabId === 'wishlist' ? this.wishlistItems :
          tabId === 'my-items' ? this.myItems : [];
    return items.slice(startIndex, startIndex + itemsPerPage);
  }

  private getPaginationHtml(tabId: string): string {
    const { totalItems, itemsPerPage, currentPage } = this.paginationState[tabId];
    const totalPages = Math.ceil(totalItems / itemsPerPage) || 1;
    const pageNumbers = [];

    for (let i = 1; i <= totalPages; i++) {
      pageNumbers.push(`
        <li class="page-item ${currentPage === i ? 'active' : ''}">
          <a class="page-link" href="#" data-tab-id="${tabId}">${i}</a>
        </li>
      `);
    }

    return `
      <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center flex-wrap gap-1">
          <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link rounded-pill px-3" href="#" data-tab-id="${tabId}" aria-label="Previous">
              <i class="bi bi-arrow-left"></i> ${this._isArabic ? 'السابق' : 'Previous'}
            </a>
          </li>
          ${pageNumbers.join('')}
          <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link px-3" href="#" data-tab-id="${tabId}" aria-label="Next">
              ${this._isArabic ? 'التالي' : 'Next'} <i class="bi bi-arrow-right"></i>
            </a>
          </li>
        </ul>
      </nav>
    `;
  }

  private async loadCategories(): Promise<void> {
    try {
      const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Marketplace Categories')/items?$select=Title,Category_AR`;
      const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);

      if (!response.ok) {
        throw new Error(`Failed to fetch categories: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const dropdownMenu = this.domElement.querySelector('#categoryDropdownMenu') as HTMLElement;

      if (!dropdownMenu) {
        throw new Error('Category dropdown menu (#categoryDropdownMenu) not found in DOM');
      }

      // Clear existing items
      dropdownMenu.innerHTML = '';

      // Map categories to dropdown items
      const itemsHtml = (data.value)
        .map((item: { Title?: string; Category_AR?: string }) => {
          const displayText = this._isArabic ? item.Category_AR?.trim() : item.Title?.trim();
          if (!displayText) return '';
          return `<li><a class="dropdown-item" href="#" data-value="${displayText}">${displayText}</a></li>`;
        })
        .filter((html: string) => html)
        .join('');

      // Populate dropdown
      dropdownMenu.innerHTML = itemsHtml;

      // Attach event listeners
      dropdownMenu.querySelectorAll('a.dropdown-item').forEach((item: HTMLElement) => {
        item.addEventListener('click', (e: Event) => {
          e.preventDefault();
          const selectedValue = item.getAttribute('data-value');
          const span = this.domElement.querySelector('#categoryDropdownBtn span') as HTMLElement;
          if (span && selectedValue) {
            span.textContent = selectedValue;
          }
        });
      });

    } catch (error) {
      console.error('Error loading categories:', error);
      throw error; // Re-throw to ensure promise rejects properly
    }
  }

  private formatDateSimple(date: string): string {
    const parsedDate = new Date(date);
    const year = parsedDate.getFullYear();
    const month = (parsedDate.getMonth() + 1).toString().padStart(2, '0');
    const day = parsedDate.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  private changeSortOrder(order: string): void {
    const sortButton = this.domElement.querySelector('#sortDropdown');
    if (sortButton) {
      sortButton.textContent = this._isArabic
        ? (order === 'newest' ? 'الترتيب حسب: الأحدث' :
          order === 'oldest' ? 'الترتيب حسب: الأقدم' :
            order === 'price-low' ? 'الترتيب حسب: السعر (منخفض)' :
              order === 'price-high' ? 'الترتيب حسب: السعر (مرتفع)' :
                'الترتيب حسب: الاسم')
        : `Sort by: ${order.charAt(0).toUpperCase() + order.slice(1)}`;
    }

    this.currentSortOrder = order;
    this.paginationState[this.activeTab].currentPage = 1;
    if (this.activeTab !== 'my-items') {
      this.fetchNewsData(this.activeTab);
    }
  }

  private copyLink(url: string): void {
    navigator.clipboard.writeText(url).then(() => {
      Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'success',
        title: this._isArabic ? 'تم نسخ الرابط بنجاح' : 'Link copied successfully',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: {
          popup: 'swal2-bootstrap',
          title: 'h6',
        },
      })
    }).catch((error) => {
      console.error('Error copying link:', error);
    });
  }

  private sendEmail(url: string): void {
    const subject = this._isArabic ? 'تحقق من هذا!' : 'Check this out!';
    const body = this._isArabic ? `إليك رابط العنصر: ${url}` : `Here is the link to the item: ${url}`;
    window.location.href = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  }

  async getPageTitle(serverRelativeUrl: string): Promise<string> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/getfilebyserverrelativeurl('${serverRelativeUrl}')/ListItemAllFields?$select=Title`;

    try {
      const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
      if (!response.ok) {
        throw new Error(`Failed to fetch page title: ${response.statusText}`);
      }
      const data = await response.json();
      return data?.Title || '';
    } catch (error) {
      console.error('Error fetching page title:', error);
      return '';
    }
  }

  private async deleteItem(itemId: string): Promise<void> {
    try {
      const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Marketplace List')/items(${itemId})`;
      const response = await this.context.spHttpClient.post(url, SPHttpClient.configurations.v1, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'IF-MATCH': '*',
          'X-HTTP-Method': 'DELETE',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to delete item ${itemId}: ${response.status} ${errorData.error?.message || 'Unknown error'}`);
      }

      // Remove from localStorage wishlist
      this.manageWishlist('remove', itemId);

      // Update local state
      this.myItems = this.myItems.filter((item) => item.Id.toString() !== itemId);
      this.paginationState['my-items'].totalItems = this.myItems.length;
      this.paginationState['my-items'].currentPage = Math.min(
        this.paginationState['my-items'].currentPage,
        Math.max(1, Math.ceil(this.myItems.length / this.paginationState['my-items'].itemsPerPage))
      );

      this.newsItems = this.newsItems.filter((item) => item.Id.toString() !== itemId);
      this.paginationState['all-items'].totalItems = this.newsItems.length;
      this.paginationState['all-items'].currentPage = Math.min(
        this.paginationState['all-items'].currentPage,
        Math.max(1, Math.ceil(this.newsItems.length / this.paginationState['all-items'].itemsPerPage))
      );

      this.wishlistItems = this.wishlistItems.filter((item) => item.Id.toString() !== itemId);
      this.paginationState['wishlist'].totalItems = this.wishlistItems.length;
      this.paginationState['wishlist'].currentPage = Math.min(
        this.paginationState['wishlist'].currentPage,
        Math.max(1, Math.ceil(this.wishlistItems.length / this.paginationState['wishlist'].itemsPerPage))
      );

      await Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'success',
        title: this._isArabic ? 'تم حذف العنصر بنجاح' : 'Item deleted successfully',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: {
          popup: 'swal2-bootstrap',
          title: 'h6',
        },
      });

      this.render();
    } catch (error) {
      console.error('Error deleting item:', error);
      await Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'error',
        title: this._isArabic ? 'فشل في حذف العنصر. حاول مرة أخرى.' : 'Failed to delete item. Please try again.',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: {
          popup: 'swal2-bootstrap',
          title: 'h6',
        },
      });
    }
  }

  private async loadModalCategories(): Promise<void> {
    try {
      const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Marketplace Categories')/items?$select=Id,Title,Category_AR`;
      const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);

      if (!response.ok) {
        throw new Error(`Failed to fetch categories: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const categorySelect = this.domElement.querySelector('#category') as HTMLSelectElement;

      if (!categorySelect) {
        throw new Error('Category dropdown not found in modal');
      }

      // Clear existing options
      categorySelect.innerHTML = `<option value="">${this._isArabic ? 'اختر' : 'Select'}</option>`;

      // Store categories data for later use
      this.categoriesData = data.value || [];

      this.categoriesData.forEach((item: { Id: number; Title?: string; Category_AR?: string }) => {
        const displayText = this._isArabic ? item.Category_AR?.trim() : item.Title?.trim();
        if (displayText) {
          const option = document.createElement('option');
          option.value = item.Id.toString(); // Use ID as value
          option.text = displayText;
          option.setAttribute('data-title-en', item.Title || '');
          option.setAttribute('data-title-ar', item.Category_AR || '');
          categorySelect.appendChild(option);
        }
      });
    } catch (error) {
      console.error('Error loading modal categories:', error);
    }
  }
  private attachExistingAttachmentListeners(): void {
    const currentAttachmentsDiv = this.domElement.querySelector('#currentAttachments') as HTMLDivElement;
    const uploadPlaceholder = this.domElement.querySelector('#uploadPlaceholder') as HTMLDivElement;
    const uploadedImagesContainer = this.domElement.querySelector('#uploadedImagesContainer') as HTMLDivElement;
    const addMoreImagesBtn = this.domElement.querySelector('#addMoreImages') as HTMLButtonElement;

    if (currentAttachmentsDiv) {
      currentAttachmentsDiv.querySelectorAll('.existing-attachment-delete').forEach((btn: HTMLElement) => {
        btn.addEventListener('click', async (e: Event) => {
          e.preventDefault();
          const filename = btn.getAttribute('data-filename');

          if (filename) {
            Swal.fire({
              title: this._isArabic ? 'تأكيد الحذف' : 'Confirm Deletion',
              text: this._isArabic ? 'هل أنت متأكد أنك تريد حذف هذه الصورة؟' : 'Are you sure you want to delete this image?',
              icon: 'warning',
              showCancelButton: true,
              confirmButtonText: this._isArabic ? 'حذف' : 'Delete',
              cancelButtonText: this._isArabic ? 'إلغاء' : 'Cancel',
              customClass: { popup: 'swal2-bootstrap' },
            }).then((result) => {
              if (result.isConfirmed) {
                // Add filename to deleted list
                this.deletedAttachmentFilenames.push(filename);

                // Remove from UI
                const imageCol = btn.closest('.col-4') as HTMLDivElement;
                if (imageCol) {
                  imageCol.remove();
                  if (currentAttachmentsDiv.children.length === 0 && uploadedImagesContainer.children.length === 0) {
                    uploadPlaceholder.style.display = 'block';
                    currentAttachmentsDiv.style.display = 'none';
                    uploadedImagesContainer.style.display = 'none';
                    addMoreImagesBtn.style.display = 'none';
                  }
                }

                Swal.fire({
                  toast: true,
                  position: this._isArabic ? 'top-start' : 'top-end',
                  icon: 'success',
                  title: this._isArabic ? 'تمت إضافة الصورة للحذف عند الحفظ' : 'Image marked for deletion on save',
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  customClass: { popup: 'swal2-bootstrap', title: 'h6' },
                });
              }
            });
          }
        });
      });
    }
  }
  private openEditModal(itemId: string): void {
    const item = this.myItems.find((i) => i.Id.toString() === itemId);
    if (!item) {
      console.error(`Item with ID ${itemId} not found`);
      Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'error',
        title: this._isArabic ? 'العنصر غير موجود' : 'Item not found',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: { popup: 'swal2-bootstrap', title: 'h6' },
      });
      return;
    }

    this._isEditMode = true;
    this.deletedAttachmentFilenames = []; // Reset deleted filenames

    const modalElement = this.domElement.querySelector('#itemFormModal') as HTMLDivElement;
    if (!modalElement) {
      console.error('Modal element #itemFormModal not found');
      Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'error',
        title: this._isArabic ? 'فشل في فتح النافذة' : 'Failed to open modal',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: { popup: 'swal2-bootstrap', title: 'h6' },
      });
      return;
    }

    const form = modalElement.querySelector('#itemForm') as HTMLFormElement;
    if (form) {
      (form.querySelector('#itemId') as HTMLInputElement).value = itemId;
      (form.querySelector('#title') as HTMLTextAreaElement).value = item.Title || '';
      (form.querySelector('#Title_AR') as HTMLTextAreaElement).value = item.Title_AR || '';
      (form.querySelector('#description_EN') as HTMLTextAreaElement).value = item.Description_EN || '';
      (form.querySelector('#description_AR') as HTMLTextAreaElement).value = item.Description_AR || '';
      (form.querySelector('#price') as HTMLInputElement).value = item.Price?.toString() || '';

      // Set type based on English value
      if (item.Type_EN) {
        const typeValue = item.Type_EN.toLowerCase() === 'new' ? 'new' : 'used';
        (form.querySelector(`#item_type_${typeValue}`) as HTMLInputElement).checked = true;
      }

      // Set status based on English value
      if (item.Status_EN) {
        const statusValue = item.Status_EN.toLowerCase() === 'active' ? 'active' : 'sold';
        (form.querySelector(`#item_status_${statusValue}`) as HTMLInputElement).checked = true;
      }

      // Set category based on current data
      const categorySelect = form.querySelector('#category') as HTMLSelectElement;
      if (categorySelect && this.categoriesData.length > 0) {
        // Find category by matching title
        const categoryTitle = this._isArabic ? item.Category_AR?.Category_AR : item.Category_EN?.Title;
        const matchingCategory = this.categoriesData.find(cat =>
          (this._isArabic ? cat.Category_AR : cat.Title) === categoryTitle
        );
        if (matchingCategory) {
          categorySelect.value = matchingCategory.Id.toString();
        }
      }

      // Make image input not required in edit mode
      const itemImagesInput = form.querySelector('#itemImages') as HTMLInputElement;
      if (itemImagesInput) {
        itemImagesInput.removeAttribute('required');
      }
    }

    // Load current attachments
    const currentAttachmentsDiv = form.querySelector('#currentAttachments') as HTMLDivElement;
    const uploadPlaceholder = this.domElement.querySelector('#uploadPlaceholder') as HTMLDivElement;
    const uploadedImagesContainer = this.domElement.querySelector('#uploadedImagesContainer') as HTMLDivElement;
    const addMoreImagesBtn = this.domElement.querySelector('#addMoreImages') as HTMLButtonElement;

    if (currentAttachmentsDiv) {
      currentAttachmentsDiv.innerHTML = '';
      const imageAttachments = item.AttachmentFiles?.filter((att) =>
        /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(att.FileName)
      ) || [];
      if (imageAttachments.length > 0) {
        currentAttachmentsDiv.style.display = 'flex';
        uploadPlaceholder.style.display = 'none';
        uploadedImagesContainer.style.display = 'none';
        addMoreImagesBtn.style.display = 'block';
        imageAttachments.forEach((attachment) => {
          const attachmentHtml = `
            <div class="col-4 col-md-3 mb-2" data-filename="${attachment.FileName}">
              <div class="position-relative">
                <img src="${attachment.ServerRelativeUrl}" class="img-thumbnail w-100" style="height: 80px; object-fit: cover;" alt="${attachment.FileName}">
                <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 existing-attachment-delete" data-filename="${attachment.FileName}" style="padding: 0.1rem 0.3rem; font-size: 0.7rem;">
                  <i class="bi bi-x"></i>
                </button>
              </div>
            </div>
          `;
          currentAttachmentsDiv.innerHTML += attachmentHtml;
        });
        // Attach deletion listeners
        this.attachExistingAttachmentListeners();
      } else {
        currentAttachmentsDiv.style.display = 'none';
        uploadPlaceholder.style.display = 'block';
        uploadedImagesContainer.style.display = 'none';
        addMoreImagesBtn.style.display = 'none';
      }
    }

    try {
      modalElement.classList.add('show');
      modalElement.style.display = 'block';
      modalElement.setAttribute('aria-hidden', 'false');
      modalElement.setAttribute('aria-modal', 'true');
      modalElement.setAttribute('role', 'dialog');
      document.body.classList.add('modal-open');

      let backdrop = document.querySelector('.modal-backdrop') as HTMLDivElement;
      if (!backdrop) {
        backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        document.body.appendChild(backdrop);
      }
    } catch (error) {
      console.error('Error opening modal:', error);
      Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'error',
        title: this._isArabic ? 'فشل في فتح النافذة' : 'Failed to open modal',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: { popup: 'swal2-bootstrap', title: 'h6' },
      });
    }
  }

  private resetForm(): void {
    const form = this.domElement.querySelector('#itemForm') as HTMLFormElement;
    if (form) {
      form.reset();
      (form.querySelector('#itemId') as HTMLInputElement).value = '';
      const itemImagesInput: any = form.querySelector('#itemImages');
      const uploadPlaceholder = this.domElement.querySelector('#uploadPlaceholder') as HTMLInputElement;
      const uploadedImagesContainer = this.domElement.querySelector('#uploadedImagesContainer') as HTMLDivElement;
      const addMoreImagesBtn = this.domElement.querySelector('#addMoreImages') as HTMLButtonElement;
      const currentAttachmentsDiv = this.domElement.querySelector('#currentAttachments') as HTMLDivElement;

      if (itemImagesInput) {
        itemImagesInput.value = '';
      }
      // Reset radio buttons to default values
      (form.querySelector('#item_type_new') as HTMLInputElement).checked = true;
      (form.querySelector('#item_status_active') as HTMLInputElement).checked = true;

      // Clear image upload UI
      if (uploadPlaceholder) {
        uploadPlaceholder.style.display = 'block';
      }
      if (uploadedImagesContainer) {
        uploadedImagesContainer.innerHTML = '';
        uploadedImagesContainer.style.display = 'none';
      }
      if (addMoreImagesBtn) {
        addMoreImagesBtn.style.display = 'none';
      }
      if (currentAttachmentsDiv) {
        currentAttachmentsDiv.innerHTML = '';
        currentAttachmentsDiv.style.display = 'none';
      }
      // Reset deleted filenames
      this.deletedAttachmentFilenames = [];
    }
  }

  private async handleFormSubmit(): Promise<void> {
    const form = this.domElement.querySelector('#itemForm') as HTMLFormElement;
    if (!form || !form.checkValidity()) {
      form?.reportValidity();
      Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'error',
        title: this._isArabic ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: { popup: 'swal2-bootstrap', title: 'h6' },
      });
      return;
    }

    const itemId = (form.querySelector('#itemId') as HTMLInputElement).value;
    const files = (form.querySelector('#itemImages') as HTMLInputElement).files;
    const currentAttachmentsDiv = form.querySelector('#currentAttachments') as HTMLDivElement;

    // Count images
    const existingImages = currentAttachmentsDiv ? currentAttachmentsDiv.querySelectorAll('.col-4').length : 0;
    const newImages = files?.length || 0;
    if (existingImages + newImages > 5) {
      Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'error',
        title: this._isArabic ? 'يمكنك تحميل 5 صور كحد أقصى' : 'You can upload a maximum of 5 images',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: { popup: 'swal2-bootstrap', title: 'h6' },
      });
      return;
    }

    // if (!this._isEditMode && newImages === 0) {
    //   Swal.fire({
    //     toast: true,
    //     position: this._isArabic ? 'top-start' : 'top-end',
    //     icon: 'error',
    //     title: this._isArabic ? 'يرجى تحميل صورة واحدة على الأقل' : 'Please upload at least one image',
    //     showConfirmButton: false,
    //     timer: 3000,
    //     timerProgressBar: true,
    //     customClass: { popup: 'swal2-bootstrap', title: 'h6' },
    //   });
    //   return;
    // }

    // Get selected type and status values
    const selectedType = (form.querySelector('input[name="item_type"]:checked') as HTMLInputElement)?.value || 'new';
    const selectedStatus = (form.querySelector('input[name="item_status"]:checked') as HTMLInputElement)?.value || 'active';
    const selectedCategoryId = (form.querySelector('#category') as HTMLSelectElement).value;

    // Map values to both languages
    const typeMapping = {
      'new': { en: 'New', ar: 'جديد' },
      'used': { en: 'Used', ar: 'مستعمل' }
    };

    const statusMapping = {
      'active': { en: 'Active', ar: 'متاح' },
      'sold': { en: 'Sold', ar: 'تم البيع' }
    };

    // Find category data by ID
    const selectedCategory = this.categoriesData.find(cat => cat.Id.toString() === selectedCategoryId);

    const item: Partial<MarketplaceItem> = {
      Title: (form.querySelector('#title') as HTMLTextAreaElement).value,
      Title_AR: (form.querySelector('#Title_AR') as HTMLTextAreaElement).value,
      Description_EN: (form.querySelector('#description_EN') as HTMLTextAreaElement).value,
      Description_AR: (form.querySelector('#description_AR') as HTMLTextAreaElement).value,
      Price: parseFloat((form.querySelector('#price') as HTMLInputElement).value),
      Type_EN: typeMapping[selectedType as keyof typeof typeMapping].en,
      Type_AR: typeMapping[selectedType as keyof typeof typeMapping].ar,
      Status_EN: statusMapping[selectedStatus as keyof typeof statusMapping].en,
      Status_Ar: statusMapping[selectedStatus as keyof typeof statusMapping].ar,
      Category_EN: { Title: selectedCategory?.Title || '' },
      Category_AR: { Category_AR: selectedCategory?.Category_AR || '' },
      // AddToWishList: false,
    };

    try {
      Swal.fire({
        title: this._isArabic ? 'جارٍ الحفظ...' : 'Saving...',
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });

      if (itemId) {
        // Update existing item
        await this.updateItem(itemId, item);

        // Delete attachments marked for removal
        for (const filename of this.deletedAttachmentFilenames) {
          try {
            await this.deleteAttachment(itemId, filename);
          } catch (error) {
            console.error(`Error deleting attachment ${filename}:`, error);
            Swal.fire({
              toast: true,
              position: this._isArabic ? 'top-start' : 'top-end',
              icon: 'error',
              title: this._isArabic ? `فشل في حذف الملف ${filename}` : `Failed to delete file ${filename}`,
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              customClass: { popup: 'swal2-bootstrap', title: 'h6' },
            });
          }
        }

        // Upload new files
        if (files && files.length > 0) {
          await this.uploadAttachments(parseInt(itemId), files);
        }
      } else {
        // Add new item
        const newItemId = await this.addItem(item);
        if (files && files.length > 0) {
          await this.uploadAttachments(newItemId, files);
        }
      }

      // Reset deleted filenames
      this.deletedAttachmentFilenames = [];

      Swal.close();

      // Close modal
      const modalElement = this.domElement.querySelector('#itemFormModal') as HTMLDivElement;
      if (modalElement) {
        this.closeModal(modalElement);
      }

      // Refresh data
      await Promise.all([
        this.fetchNewsData('all-items'),
        this.fetchNewsData('wishlist'),
        this.fetchNewsData('my-items'),
      ]);

      Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'success',
        title: this._isArabic ? 'تم حفظ العنصر بنجاح' : 'Item saved successfully',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: { popup: 'swal2-bootstrap', title: 'h6' },
      });
    } catch (error) {
      console.error('Error saving item:', error);
      Swal.close();
      Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'error',
        title: this._isArabic ? 'فشل في حفظ العنصر' : 'Failed to save item',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: { popup: 'swal2-bootstrap', title: 'h6' },
      });
    }
  }
  private async addItem(item: Partial<MarketplaceItem>): Promise<number> {
    try {
      const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Marketplace List')/items`;
      const body = JSON.stringify({

        Title: item.Title,
        Title_AR: item.Title_AR,
        Description_EN: item.Description_EN,
        Description_AR: item.Description_AR,
        Price: item.Price,
        Type_EN: item.Type_EN,
        Type_AR: item.Type_AR,
        Category_ENId: await this.getCategoryIdByTitle(item.Category_EN?.Title, 'EN'),
        Category_ARId: await this.getCategoryIdByTitle(item.Category_AR?.Category_AR, 'AR'),
        // AddToWishList: item.AddToWishList,
        Status_EN: item.Status_EN,
        Status_Ar: item.Status_Ar
      });

      const response = await this.context.spHttpClient.post(url, SPHttpClient.configurations.v1, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'X-RequestDigest': await this.getRequestDigest(),
        },
        body,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to add item: ${response.status} ${errorData.error?.message || 'Unknown error'}`);
      }

      const data = await response.json();
      return data.Id;
    } catch (error) {
      console.error('Error adding item:', error);
      throw error;
    }
  }

  private async updateItem(itemId: string, item: Partial<MarketplaceItem>): Promise<void> {
    try {
      const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Marketplace List')/items(${itemId})`;
      const body = JSON.stringify({

        Title: item.Title,
        Title_AR: item.Title_AR,
        Description_EN: item.Description_EN,
        Description_AR: item.Description_AR,
        Price: item.Price,
        Type_EN: item.Type_EN,
        Type_AR: item.Type_AR,
        Status_EN: item.Status_EN,
        Status_Ar: item.Status_Ar,
        Category_ENId: await this.getCategoryIdByTitle(item.Category_EN?.Title, 'EN'),
        Category_ARId: await this.getCategoryIdByTitle(item.Category_AR?.Category_AR, 'AR'),
      });

      const response = await this.context.spHttpClient.post(url, SPHttpClient.configurations.v1, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'IF-MATCH': '*',
          'X-HTTP-Method': 'MERGE',
          'X-RequestDigest': await this.getRequestDigest(),
        },
        body,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to update item: ${response.status} ${errorData.error?.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error updating item:', error);
      throw error;
    }
  }

  private async uploadAttachments(itemId: number, files: FileList): Promise<void> {
    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Marketplace List')/items(${itemId})/AttachmentFiles/add(FileName='${file.name}')`;
        const reader = new FileReader();

        const fileContent = await new Promise<ArrayBuffer>((resolve) => {
          reader.onload = () => resolve(reader.result as ArrayBuffer);
          reader.readAsArrayBuffer(file);
        });

        const response = await this.context.spHttpClient.post(url, SPHttpClient.configurations.v1, {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/octet-stream',
            'X-RequestDigest': await this.getRequestDigest(),
          },
          body: fileContent,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`Failed to upload attachment ${file.name}: ${errorData.error?.message || 'Unknown error'}`);
        }
      }
    } catch (error) {
      console.error('Error uploading attachments:', error);
      throw error;
    }
  }

  private async getCategoryIdByTitle(categoryTitle: string | undefined, lang: 'EN' | 'AR'): Promise<number | undefined> {
    if (!categoryTitle) return undefined;
    try {
      const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Marketplace Categories')/items?$filter=${lang === 'EN' ? 'Title' : 'Category_AR'} eq '${encodeURIComponent(categoryTitle)}'&$select=Id`;
      const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
      if (!response.ok) {
        throw new Error(`Failed to fetch category ID: ${response.statusText}`);
      }
      const data = await response.json();
      return data.value[0]?.Id;
    } catch (error) {
      console.error(`Error fetching category ID for ${categoryTitle}:`, error);
      throw error;
    }
  }

  private async getRequestDigest(): Promise<string> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/contextinfo`;
    const response = await this.context.spHttpClient.post(url, SPHttpClient.configurations.v1, {
      headers: { 'Accept': 'application/json' },
    });
    const data = await response.json();
    return data.FormDigestValue;
  }
  async deleteAttachment(itemId: string, fileName: string): Promise<void> {
    try {
      const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Marketplace List')/items(${itemId})/AttachmentFiles/getByFileName('${encodeURIComponent(fileName)}')`;
      const response = await this.context.spHttpClient.post(url, SPHttpClient.configurations.v1, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'IF-MATCH': '*',
          'X-HTTP-Method': 'DELETE',
          'X-RequestDigest': await this.getRequestDigest(),
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to delete attachment ${fileName}: ${response.status} ${errorData.error?.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error(`Error deleting attachment ${fileName}:`, error);
      throw error;
    }
  }
  private populateImageModal(itemId: string): void {
    const item = this.newsItems.find((i) => i.Id.toString() === itemId) ||
      this.wishlistItems.find((i) => i.Id.toString() === itemId) ||
      this.myItems.find((i) => i.Id.toString() === itemId);
    if (!item) {
      console.error(`Item with ID ${itemId} not found`);
      Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'error',
        title: this._isArabic ? 'العنصر غير موجود' : 'Item not found',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: { popup: 'swal2-bootstrap', title: 'h6' },
      });
      return;
    }

    const imageModal = this.domElement.querySelector('#imageModal') as HTMLDivElement;
    if (!imageModal) {
      console.error('Image modal (#imageModal) not found in DOM');
      Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'error',
        title: this._isArabic ? 'فشل في فتح معرض الصور' : 'Failed to open image gallery',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: { popup: 'swal2-bootstrap', title: 'h6' },
      });
      return;
    }

    const carouselInner = this.domElement.querySelector('#modalCarouselInner') as HTMLDivElement;
    const carouselIndicators = this.domElement.querySelector('#modalCarouselIndicators') as HTMLDivElement;
    const prevControl = this.domElement.querySelector('.carousel-control-prev.modal-control') as HTMLButtonElement;
    const nextControl = this.domElement.querySelector('.carousel-control-next.modal-control') as HTMLButtonElement;

    if (!carouselInner || !carouselIndicators || !prevControl || !nextControl) {
      console.error('Modal carousel elements not found');
      Swal.fire({
        toast: true,
        position: this._isArabic ? 'top-start' : 'top-end',
        icon: 'error',
        title: this._isArabic ? 'فشل في تحميل معرض الصور' : 'Failed to load image gallery',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: { popup: 'swal2-bootstrap', title: 'h6' },
      });
      return;
    }

    const imageAttachments = (item.AttachmentFiles || []).filter((att) =>
      /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(att.FileName)
    );

    // Clear existing content
    carouselInner.innerHTML = '';
    carouselIndicators.innerHTML = '';

    if (imageAttachments.length === 0) {
      // Handle no images
      carouselInner.innerHTML = `
        <div class="carousel-item active">
          <img src="${require('./assets/img.jpg')}" class="d-block w-100 modal-image" alt="${this._isArabic ? 'لا توجد صورة متاحة' : 'No Image Available'}">
        </div>
      `;
      prevControl.style.display = 'none';
      nextControl.style.display = 'none';
      carouselIndicators.style.display = 'none';
      return;
    }

    // Populate carousel items
    imageAttachments.forEach((att, idx) => {
      const isActive = idx === 0 ? 'active' : '';
      const carouselItem = `
        <div class="carousel-item ${isActive}">
          <img src="${att.ServerRelativeUrl}" class="d-block w-100 modal-image" alt="${this._isArabic ? 'صورة المنتج' : 'Product Image'} ${idx + 1}">
        </div>
      `;
      carouselInner.innerHTML += carouselItem;

      // Populate indicators
      const indicator = `
        <button type="button" data-bs-target="#modalSlider" data-bs-slide-to="${idx}" class="${isActive}" aria-label="${this._isArabic ? 'الصورة' : 'Slide'} ${idx + 1}" ${isActive ? 'aria-current="true"' : ''}></button>
      `;
      carouselIndicators.innerHTML += indicator;
    });

    // Show/hide controls based on number of images
    if (imageAttachments.length === 1) {
      prevControl.style.display = 'none';
      nextControl.style.display = 'none';
      carouselIndicators.style.display = 'none';
    } else {
      prevControl.style.display = 'block';
      nextControl.style.display = 'block';
      carouselIndicators.style.display = 'flex';
    }
  }
  private manageWishlist(action: 'get' | 'add' | 'remove' | 'clear', itemId?: string): string[] {
    const key = 'marketplace_wishlist';
    let wishlist: string[] = JSON.parse(localStorage.getItem(key) || '[]');

    switch (action) {
      case 'get':
        return wishlist;
      case 'add':
        if (itemId && !wishlist.includes(itemId)) {
          wishlist.push(itemId);
          localStorage.setItem(key, JSON.stringify(wishlist));
        }
        return wishlist;
      case 'remove':
        if (itemId) {
          wishlist = wishlist.filter((id) => id !== itemId);
          localStorage.setItem(key, JSON.stringify(wishlist));
        }
        return wishlist;
      case 'clear':
        wishlist = [];
        localStorage.setItem(key, JSON.stringify(wishlist));
        return wishlist;
      default:
        return wishlist;
    }
  }
  private clearWishlist(): void {
    this.manageWishlist('clear');
    this.wishlistItems = [];
    this.paginationState['wishlist'].totalItems = 0;
    this.paginationState['wishlist'].currentPage = 1;
    this.render();
    Swal.fire({
      toast: true,
      position: this._isArabic ? 'top-start' : 'top-end',
      icon: 'success',
      title: this._isArabic ? 'تم مسح قائمة الرغبات' : 'Wishlist cleared',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      customClass: { popup: 'swal2-bootstrap', title: 'h6' },
    });
  }
}



