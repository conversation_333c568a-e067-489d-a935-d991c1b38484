import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';
import { Version } from '@microsoft/sp-core-library';
import {
  type IPropertyPaneConfiguration,
  PropertyPaneTextField,
  PropertyPaneDropdown,
  IPropertyPaneDropdownOption
} from '@microsoft/sp-property-pane';

export interface ISummaryControlWebPartProps {
  description: string;
  selectedPageId: string;
  listName: string;
}

export interface ISummaryItem {
  Id: number;
  Title: string;
  Title_AR?: string;
  Summary_EN?: string;
  Summary_AR?: string;
  PageURL_EN?: string;
  PageURL_AR?: string;
}

export default class SummaryControlWebPart extends BaseClientSideWebPart<ISummaryControlWebPartProps> {
  private _summaryItems: ISummaryItem[] = [];
  private _allSummaryItems: ISummaryItem[] = [];
  private _pageOptions: IPropertyPaneDropdownOption[] = [];
  private _isArabic: boolean = false;
  private _isLoading: boolean = false;

  protected async onInit(): Promise<void> {
    await super.onInit();
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;

    // تحديد اسم الليست الافتراضي إذا لم يكن محدد
    if (!this.properties.listName) {
      this.properties.listName = 'Page%20Summary'; // اسم الليست الافتراضي
    }

    await this._loadAllSummaryItems();
    await this._loadSelectedSummary();
  }

  private getListEndpoint(listTitle: string, selectFields?: string, expandFields?: string, filterQuery?: string, orderQuery?: string, topQuery?: string, forceMainSite?: boolean): string {
    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    const siteUrl = this.context.pageContext.site.absoluteUrl;
    const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    // للملخصات: نقرأ من main site والـ subsites
    const baseUrl = forceMainSite
      ? `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
      : (isSubSite
        ? `${currentWebUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
        : `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`);

    // بناء query parameters
    const queryParams: string[] = [];

    if (selectFields) {
      queryParams.push(`$select=${selectFields}`);
    }

    if (expandFields) {
      queryParams.push(`$expand=${expandFields}`);
    }

    if (filterQuery) {
      queryParams.push(filterQuery);
    }

    if (orderQuery) {
      queryParams.push(orderQuery);
    }

    if (topQuery) {
      queryParams.push(topQuery);
    }


    return queryParams.length > 0
      ? `${baseUrl}?${queryParams.join('&')}`
      : baseUrl;
  }

  private async _loadAllSummaryItems(): Promise<void> {
    try {

      const listName = this.properties.listName || 'Page%20Summary';
      const selectFields = '*';
      const orderQuery = '$orderby=Title asc';

      const url = this.getListEndpoint(listName, selectFields, undefined, undefined, orderQuery);


      const response: SPHttpClientResponse = await this.context.spHttpClient.get(
        url,
        SPHttpClient.configurations.v1
      );

      if (response.ok) {
        const data = await response.json();
        this._allSummaryItems = data.value || [];

        // تحديث خيارات الـ Property Pane
        this._updatePageOptions();
      } else {
        console.error('Summary Control - Failed to load items:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Summary Control - Error loading items:', error);
    }
  }

  private async _loadSelectedSummary(): Promise<void> {
    try {
      this._isLoading = true;
      this.render();

      if (!this.properties.selectedPageId) {
        this._summaryItems = [];
        return;
      }


      const listName = this.properties.listName || 'Page%20Summary';
      const selectFields = 'Id,Title,Title_AR,Summary_EN,Summary_AR,PageURL_EN,PageURL_AR';
      const filterQuery = `$filter=Id eq ${this.properties.selectedPageId}`;

      const url = this.getListEndpoint(listName, selectFields, undefined, filterQuery);


      const response: SPHttpClientResponse = await this.context.spHttpClient.get(
        url,
        SPHttpClient.configurations.v1
      );

      if (response.ok) {
        const data = await response.json();
        this._summaryItems = data.value || [];
      } else {
        console.error('Summary Control - Failed to load selected item:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Summary Control - Error loading selected item:', error);
    } finally {
      this._isLoading = false;
      this.render();
    }
  }

  private _updatePageOptions(): void {
    this._pageOptions = [
      { key: '', text: this._isArabic ? 'اختر صفحة...' : 'Select a page...' }
    ];

    this._allSummaryItems.forEach(item => {
      const title = this._isArabic ? (item.Title_AR || item.Title) : item.Title;
      this._pageOptions.push({
        key: item.Id.toString(),
        text: title
      });
    });

    // تحديث الـ Property Pane
    this.context.propertyPane.refresh();
  }

  public render(): void {
    const loadingText = this._isArabic ? 'جاري التحميل...' : 'Loading...';
    const noSelectionText = this._isArabic ? 'لم يتم اختيار صفحة' : 'No page selected';
    const configureText = this._isArabic ? 'يرجى تكوين الويب بارت من إعدادات الصفحة' : 'Please configure the web part from page settings';

    this.domElement.innerHTML = `
      <div class="summary-control-webpart">
        ${this._isLoading ? `
          <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">${loadingText}</span>
            </div>
            <p class="mt-3">${loadingText}</p>
          </div>
        ` : this._renderSummaryContent(noSelectionText, configureText)}
      </div>
    `;
  }

  private _renderSummaryContent(noSelectionText: string, configureText: string): string {
    if (!this.properties.selectedPageId) {
      return `
        <div class="text-center py-5">
          <i class="bi bi-gear display-1  text-color "></i>
          <h4 class="mt-3  text-color ">${noSelectionText}</h4>
          <p class=" text-color ">${configureText}</p>
        </div>
      `;
    }

    if (!this._summaryItems || this._summaryItems.length === 0) {
      return `
        <div class="text-center py-5">
          <i class="bi bi-exclamation-circle display-1 text-warning"></i>
          <h4 class="mt-3 text-warning">${this._isArabic ? 'لم يتم العثور على المحتوى' : 'Content not found'}</h4>
          <p class=" text-color ">${this._isArabic ? 'الصفحة المحددة غير موجودة' : 'The selected page does not exist'}</p>
        </div>
      `;
    }

    const summaryItem: any = this._summaryItems[0];

    // التعامل مع العنوان - جميع الحقول optional
    let title = '';
    if (this._isArabic) {
      title = summaryItem.Title_AR || summaryItem.Title || 'عنوان غير متوفر';
    } else {
      title = summaryItem.Title || summaryItem.Title_AR || 'Title not available';
    }

    let summary = '';
    if (this._isArabic) {
      summary = summaryItem.Summary_AR || summaryItem.Summary_EN || '';
    } else {
      summary = summaryItem.Summary_EN || summaryItem.Summary_AR || '';
    }

    if (summary) {
      summary = this._cleanHtmlContent(summary);
    }

    let pageUrl = '';
    let pageUrlDescription = '';

    if (this._isArabic) {
      // جرب العربي أولاً
      if (summaryItem.PageURL_AR) {
        pageUrl = summaryItem.PageURL_AR.Url || summaryItem.PageURL_AR || '';
        pageUrlDescription = summaryItem.PageURL_AR.Description || 'عرض المزيد';
      } else if (summaryItem.PageURL_EN) {
        pageUrl = summaryItem.PageURL_EN.Url || summaryItem.PageURL_EN || '';
        pageUrlDescription = summaryItem.PageURL_EN.Description || 'عرض المزيد';
      } else {
        pageUrlDescription = 'عرض المزيد';
      }
    } else {
      // جرب الإنجليزي أولاً
      if (summaryItem.PageURL_EN) {
        pageUrl = summaryItem.PageURL_EN.Url || summaryItem.PageURL_EN || '';
        pageUrlDescription = summaryItem.PageURL_EN.Description || 'Read More';
      } else if (summaryItem.PageURL_AR) {
        pageUrl = summaryItem.PageURL_AR.Url || summaryItem.PageURL_AR || '';
        pageUrlDescription = summaryItem.PageURL_AR.Description || 'Read More';
      } else {
        pageUrlDescription = 'Read More';
      }
    }

    return `
      <div class="card card-color pt-2 px-4 section-card bg-remover my-3 admin-section bg-remover">
        <h2 class="fw-bold head-color mb-4 tittle">${title ? title : (this._isArabic ? 'لا يوجد عنوان متاح' : 'No title available')}</h2>

        <div class="text-color">
          ${summary ? summary : `<p class=" text-color  fst-italic">${this._isArabic ? 'لا يوجد ملخص متاح' : 'No summary available'}</p>`}
        </div>
        <div class="text-end mt-3 card-footer">
          ${pageUrl && pageUrl.trim() !== '' ? `
            <a href="${pageUrl}" class="text-decoration-none fw-bold btn-main-link" target="_blank">
              ${pageUrlDescription} →
            </a>
          ` : `
            <small class=" text-color  fst-italic">
              ${this._isArabic ? 'لا يوجد رابط متاح' : 'No link available'}
            </small>
          `}
        </div>
      </div>
     
    `;
  }

  protected get dataVersion(): Version {
    return Version.parse('1.0');
  }

  protected onPropertyPaneFieldChanged(propertyPath: string, oldValue: any, newValue: any): void {
    if (propertyPath === 'selectedPageId') {
      this._loadSelectedSummary();
    } else if (propertyPath === 'listName') {
      this._loadAllSummaryItems().then(() => {
        this._loadSelectedSummary();
      });
    }
  }

  protected getPropertyPaneConfiguration(): IPropertyPaneConfiguration {
    return {
      pages: [
        {
          header: {
            description: this._isArabic ? 'إعدادات ملخص الصفحة' : 'Page Summary Settings'
          },
          groups: [
            {
              groupName: this._isArabic ? 'الإعدادات الأساسية' : 'Basic Settings',
              groupFields: [
                PropertyPaneTextField('listName', {
                  label: this._isArabic ? 'اسم القائمة' : 'List Name',
                  description: this._isArabic ? 'اسم قائمة SharePoint التي تحتوي على ملخصات الصفحات' : 'Name of the SharePoint list containing Page%20Summary',
                  value: this.properties.listName || 'Page%20Summary'
                }),
                PropertyPaneDropdown('selectedPageId', {
                  label: this._isArabic ? 'اختر الصفحة' : 'Select Page',
                  options: this._pageOptions,
                  selectedKey: this.properties.selectedPageId
                })
              ]
            },
            {
              groupName: this._isArabic ? 'معلومات' : 'Information',
              groupFields: [
                PropertyPaneTextField('description', {
                  label: this._isArabic ? 'الوصف' : 'Description',
                  description: this._isArabic ? 'وصف الويب بارت' : 'Description of the web part',
                  multiline: true,
                  rows: 3
                })
              ]
            }
          ]
        }
      ]
    };
  }

  // Helper method لتنظيف محتوى HTML
  private _cleanHtmlContent(htmlContent: string): string {
    if (!htmlContent) return '';

    try {
      // إزالة الـ HTML tags الخارجية والـ inline styles المعقدة
      let cleaned = htmlContent
        .replace(/<div[^>]*class="ExternalClass[^"]*"[^>]*>/gi, '') // إزالة External class divs
        .replace(/<div[^>]*style="[^"]*"[^>]*>/gi, '<div>') // تبسيط الـ style attributes
        .replace(/style="[^"]*"/gi, '') // إزالة جميع الـ inline styles
        .replace(/&#58;/g, ':') // تحويل HTML entities
        .replace(/&nbsp;/g, ' ') // تحويل non-breaking spaces
        .replace(/<\/div><\/div>$/gi, '</div>') // إزالة الـ closing divs الزائدة
        .replace(/\s+/g, ' ') // تنظيف المسافات الزائدة
        .trim();

      // إذا كان المحتوى لا يزال يحتوي على HTML tags صالحة، اتركه كما هو
      // وإلا قم بإزالة جميع الـ HTML tags
      if (cleaned.includes('<') && cleaned.includes('>')) {
        // تنظيف أساسي للـ HTML
        cleaned = cleaned
          .replace(/<br\s*\/?>/gi, '<br>') // تنظيف br tags
          .replace(/<p\s*>/gi, '<p>') // تنظيف p tags
          .replace(/<span[^>]*>/gi, '<span>'); // تنظيف span tags
      }

      return cleaned;
    } catch (error) {
      console.warn('Error cleaning HTML content:', error);
      // في حالة الخطأ، قم بإزالة جميع الـ HTML tags
      return htmlContent.replace(/<[^>]*>/g, '').trim();
    }
  }
}