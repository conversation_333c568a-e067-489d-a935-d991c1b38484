{"$schema": "https://developer.microsoft.com/json-schemas/spfx-build/config.2.0.schema.json", "version": "2.0", "bundles": {"home-slider-web-part": {"components": [{"entrypoint": "./lib/webparts/homeSlider/HomeSliderWebPart.js", "manifest": "./src/webparts/homeSlider/HomeSliderWebPart.manifest.json"}]}, "news-web-part": {"components": [{"entrypoint": "./lib/webparts/news/NewsWebPart.js", "manifest": "./src/webparts/news/NewsWebPart.manifest.json"}]}, "news-details-web-part": {"components": [{"entrypoint": "./lib/webparts/newsDetails/NewsDetailsWebPart.js", "manifest": "./src/webparts/newsDetails/NewsDetailsWebPart.manifest.json"}]}, "news-list-web-part": {"components": [{"entrypoint": "./lib/webparts/newsList/NewsListWebPart.js", "manifest": "./src/webparts/newsList/NewsListWebPart.manifest.json"}]}, "global-customizer-application-customizer": {"components": [{"entrypoint": "./lib/extensions/globalCustomizer/GlobalCustomizerApplicationCustomizer.js", "manifest": "./src/extensions/globalCustomizer/GlobalCustomizerApplicationCustomizer.manifest.json"}]}, "our-people-web-part": {"components": [{"entrypoint": "./lib/webparts/ourPeople/OurPeopleWebPart.js", "manifest": "./src/webparts/ourPeople/OurPeopleWebPart.manifest.json"}]}, "quick-access-web-part": {"components": [{"entrypoint": "./lib/webparts/quickAccess/QuickAccessWebPart.js", "manifest": "./src/webparts/quickAccess/QuickAccessWebPart.manifest.json"}]}, "employee-web-part": {"components": [{"entrypoint": "./lib/webparts/employee/EmployeeWebPart.js", "manifest": "./src/webparts/employee/EmployeeWebPart.manifest.json"}]}, "our-stories-web-part": {"components": [{"entrypoint": "./lib/webparts/ourStories/OurStoriesWebPart.js", "manifest": "./src/webparts/ourStories/OurStoriesWebPart.manifest.json"}]}, "stories-web-part": {"components": [{"entrypoint": "./lib/webparts/stories/StoriesWebPart.js", "manifest": "./src/webparts/stories/StoriesWebPart.manifest.json"}]}, "setting-site-web-part": {"components": [{"entrypoint": "./lib/webparts/settingSite/SettingSiteWebPart.js", "manifest": "./src/webparts/settingSite/SettingSiteWebPart.manifest.json"}]}, "header-web-part": {"components": [{"entrypoint": "./lib/webparts/header/HeaderWebPart.js", "manifest": "./src/webparts/header/HeaderWebPart.manifest.json"}]}, "home-collection-web-part": {"components": [{"entrypoint": "./lib/webparts/homeCollection/HomeCollectionWebPart.js", "manifest": "./src/webparts/homeCollection/HomeCollectionWebPart.manifest.json"}]}, "inspire-reflect-web-part": {"components": [{"entrypoint": "./lib/webparts/inspireReflect/InspireReflectWebPart.js", "manifest": "./src/webparts/inspireReflect/InspireReflectWebPart.manifest.json"}]}, "bread-cramp-web-part": {"components": [{"entrypoint": "./lib/webparts/breadCramp/BreadCrampWebPart.js", "manifest": "./src/webparts/breadCramp/BreadCrampWebPart.manifest.json"}]}, "internal-vacancies-web-part": {"components": [{"entrypoint": "./lib/webparts/internalVacancies/InternalVacanciesWebPart.js", "manifest": "./src/webparts/internalVacancies/InternalVacanciesWebPart.manifest.json"}]}, "vacancay-details-web-part": {"components": [{"entrypoint": "./lib/webparts/vacancayDetails/VacancayDetailsWebPart.js", "manifest": "./src/webparts/vacancayDetails/VacancayDetailsWebPart.manifest.json"}]}, "footer-web-part": {"components": [{"entrypoint": "./lib/webparts/footer/FooterWebPart.js", "manifest": "./src/webparts/footer/FooterWebPart.manifest.json"}]}, "events-web-part": {"components": [{"entrypoint": "./lib/webparts/events/EventsWebPart.js", "manifest": "./src/webparts/events/EventsWebPart.manifest.json"}]}, "all-testimonials-web-part": {"components": [{"entrypoint": "./lib/webparts/allTestimonials/AllTestimonialsWebPart.js", "manifest": "./src/webparts/allTestimonials/AllTestimonialsWebPart.manifest.json"}]}, "testimonials-web-part": {"components": [{"entrypoint": "./lib/webparts/testimonials/TestimonialsWebPart.js", "manifest": "./src/webparts/testimonials/TestimonialsWebPart.manifest.json"}]}, "market-place-web-part": {"components": [{"entrypoint": "./lib/webparts/marketPlace/MarketPlaceWebPart.js", "manifest": "./src/webparts/marketPlace/MarketPlaceWebPart.manifest.json"}]}, "announcement-web-part": {"components": [{"entrypoint": "./lib/webparts/announcement/AnnouncementWebPart.js", "manifest": "./src/webparts/announcement/AnnouncementWebPart.manifest.json"}]}, "department-employees-web-part": {"components": [{"entrypoint": "./lib/webparts/departmentEmployees/DepartmentEmployeesWebPart.js", "manifest": "./src/webparts/departmentEmployees/DepartmentEmployeesWebPart.manifest.json"}]}, "department-director-web-part": {"components": [{"entrypoint": "./lib/webparts/departmentDirector/DepartmentDirectorWebPart.js", "manifest": "./src/webparts/departmentDirector/DepartmentDirectorWebPart.manifest.json"}]}, "department-mandate-web-part": {"components": [{"entrypoint": "./lib/webparts/departmentMandate/DepartmentMandateWebPart.js", "manifest": "./src/webparts/departmentMandate/DepartmentMandateWebPart.manifest.json"}]}, "fa-qs-web-part": {"components": [{"entrypoint": "./lib/webparts/faQs/FaQsWebPart.js", "manifest": "./src/webparts/faQs/FaQsWebPart.manifest.json"}]}, "newsletter-web-part": {"components": [{"entrypoint": "./lib/webparts/newsletter/NewsletterWebPart.js", "manifest": "./src/webparts/newsletter/NewsletterWebPart.manifest.json"}]}, "all-fa-qs-web-part": {"components": [{"entrypoint": "./lib/webparts/allFaQs/AllFaQsWebPart.js", "manifest": "./src/webparts/allFaQs/AllFaQsWebPart.manifest.json"}]}, "all-newsletters-web-part": {"components": [{"entrypoint": "./lib/webparts/allNewsletters/AllNewslettersWebPart.js", "manifest": "./src/webparts/allNewsletters/AllNewslettersWebPart.manifest.json"}]}, "articles-web-part": {"components": [{"entrypoint": "./lib/webparts/articles/ArticlesWebPart.js", "manifest": "./src/webparts/articles/ArticlesWebPart.manifest.json"}]}, "emplolyees-web-part": {"components": [{"entrypoint": "./lib/webparts/emplolyees/EmplolyeesWebPart.js", "manifest": "./src/webparts/emplolyees/EmplolyeesWebPart.manifest.json"}]}, "all-emplolyees-web-part": {"components": [{"entrypoint": "./lib/webparts/allEmplolyees/AllEmplolyeesWebPart.js", "manifest": "./src/webparts/allEmplolyees/AllEmplolyeesWebPart.manifest.json"}]}, "summary-control-web-part": {"components": [{"entrypoint": "./lib/webparts/summaryControl/SummaryControlWebPart.js", "manifest": "./src/webparts/summaryControl/SummaryControlWebPart.manifest.json"}]}, "department-news-details-web-part": {"components": [{"entrypoint": "./lib/webparts/departmentNewsDetails/DepartmentNewsDetailsWebPart.js", "manifest": "./src/webparts/departmentNewsDetails/DepartmentNewsDetailsWebPart.manifest.json"}]}, "birthday-web-part": {"components": [{"entrypoint": "./lib/webparts/birthday/BirthdayWebPart.js", "manifest": "./src/webparts/birthday/BirthdayWebPart.manifest.json"}]}, "work-web-part": {"components": [{"entrypoint": "./lib/webparts/work/WorkWebPart.js", "manifest": "./src/webparts/work/WorkWebPart.manifest.json"}]}, "our-lens-web-part": {"components": [{"entrypoint": "./lib/webparts/ourLens/OurLensWebPart.js", "manifest": "./src/webparts/ourLens/OurLensWebPart.manifest.json"}]}, "our-stories-list-web-part": {"components": [{"entrypoint": "./lib/webparts/ourStoriesList/OurStoriesListWebPart.js", "manifest": "./src/webparts/ourStoriesList/OurStoriesListWebPart.manifest.json"}]}, "news-ticker-web-part": {"components": [{"entrypoint": "./lib/webparts/newsTicker/NewsTickerWebPart.js", "manifest": "./src/webparts/newsTicker/NewsTickerWebPart.manifest.json"}]}, "mazaya-web-part": {"components": [{"entrypoint": "./lib/webparts/mazaya/MazayaWebPart.js", "manifest": "./src/webparts/mazaya/MazayaWebPart.manifest.json"}]}, "mazaya-details-web-part": {"components": [{"entrypoint": "./lib/webparts/mazayaDetails/MazayaDetailsWebPart.js", "manifest": "./src/webparts/mazayaDetails/MazayaDetailsWebPart.manifest.json"}]}, "featured-deals-web-part": {"components": [{"entrypoint": "./lib/webparts/featuredDeals/FeaturedDealsWebPart.js", "manifest": "./src/webparts/featuredDeals/FeaturedDealsWebPart.manifest.json"}]}, "category-details-web-part": {"components": [{"entrypoint": "./lib/webparts/categoryDetails/CategoryDetailsWebPart.js", "manifest": "./src/webparts/categoryDetails/CategoryDetailsWebPart.manifest.json"}]}}, "externals": {}, "localizedResources": {"HomeSliderWebPartStrings": "lib/webparts/homeSlider/loc/{locale}.js", "NewsWebPartStrings": "lib/webparts/news/loc/{locale}.js", "NewsDetailsWebPartStrings": "lib/webparts/newsDetails/loc/{locale}.js", "NewsListWebPartStrings": "lib/webparts/newsList/loc/{locale}.js", "GlobalCustomizerApplicationCustomizerStrings": "lib/extensions/globalCustomizer/loc/{locale}.js", "OurPeopleWebPartStrings": "lib/webparts/ourPeople/loc/{locale}.js", "QuickAccessWebPartStrings": "lib/webparts/quickAccess/loc/{locale}.js", "EmployeeWebPartStrings": "lib/webparts/employee/loc/{locale}.js", "OurStoriesWebPartStrings": "lib/webparts/ourStories/loc/{locale}.js", "StoriesWebPartStrings": "lib/webparts/stories/loc/{locale}.js", "SettingSiteWebPartStrings": "lib/webparts/settingSite/loc/{locale}.js", "HeaderWebPartStrings": "lib/webparts/header/loc/{locale}.js", "HomeCollectionWebPartStrings": "lib/webparts/homeCollection/loc/{locale}.js", "InspireReflectWebPartStrings": "lib/webparts/inspireReflect/loc/{locale}.js", "BreadCrampWebPartStrings": "lib/webparts/breadCramp/loc/{locale}.js", "InternalVacanciesWebPartStrings": "lib/webparts/internalVacancies/loc/{locale}.js", "VacancayDetailsWebPartStrings": "lib/webparts/vacancayDetails/loc/{locale}.js", "FooterWebPartStrings": "lib/webparts/footer/loc/{locale}.js", "EventsWebPartStrings": "lib/webparts/events/loc/{locale}.js", "AllTestimonialsWebPartStrings": "lib/webparts/allTestimonials/loc/{locale}.js", "TestimonialsWebPartStrings": "lib/webparts/testimonials/loc/{locale}.js", "MarketPlaceWebPartStrings": "lib/webparts/marketPlace/loc/{locale}.js", "AnnouncementWebPartStrings": "lib/webparts/announcement/loc/{locale}.js", "DepartmentEmployeesWebPartStrings": "lib/webparts/departmentEmployees/loc/{locale}.js", "DepartmentDirectorWebPartStrings": "lib/webparts/departmentDirector/loc/{locale}.js", "DepartmentMandateWebPartStrings": "lib/webparts/departmentMandate/loc/{locale}.js", "FaQsWebPartStrings": "lib/webparts/faQs/loc/{locale}.js", "NewsletterWebPartStrings": "lib/webparts/newsletter/loc/{locale}.js", "AllFaQsWebPartStrings": "lib/webparts/allFaQs/loc/{locale}.js", "AllNewslettersWebPartStrings": "lib/webparts/allNewsletters/loc/{locale}.js", "ArticlesWebPartStrings": "lib/webparts/articles/loc/{locale}.js", "EmplolyeesWebPartStrings": "lib/webparts/emplolyees/loc/{locale}.js", "AllEmplolyeesWebPartStrings": "lib/webparts/allEmplolyees/loc/{locale}.js", "SummaryControlWebPartStrings": "lib/webparts/summaryControl/loc/{locale}.js", "DepartmentNewsDetailsWebPartStrings": "lib/webparts/departmentNewsDetails/loc/{locale}.js", "BirthdayWebPartStrings": "lib/webparts/birthday/loc/{locale}.js", "WorkWebPartStrings": "lib/webparts/work/loc/{locale}.js", "OurLensWebPartStrings": "lib/webparts/ourLens/loc/{locale}.js", "OurStoriesListWebPartStrings": "lib/webparts/ourStoriesList/loc/{locale}.js", "NewsTickerWebPartStrings": "lib/webparts/newsTicker/loc/{locale}.js", "MazayaWebPartStrings": "lib/webparts/mazaya/loc/{locale}.js", "MazayaDetailsWebPartStrings": "lib/webparts/mazayaDetails/loc/{locale}.js", "FeaturedDealsWebPartStrings": "lib/webparts/featuredDeals/loc/{locale}.js", "CategoryDetailsWebPartStrings": "lib/webparts/categoryDetails/loc/{locale}.js"}}