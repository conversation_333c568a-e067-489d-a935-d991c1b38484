import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient } from '@microsoft/sp-http';

interface AnnouncementItem {
  Id: number;
  Title: string;
  Title_AR: string;
  Begins: string;
  Expires: string;
  Description_EN: string;
  Description_AR: string;
  Active: boolean;
  Category: {
    Id: number;
    Title: string;
    Title_AR: string;
  };
  Link_EN: { Url: string };
  Link_AR: { Url: string };
}

interface CategoryItem {
  Id: number;
  Title: string;
  Title_AR: string;
  Icon: string;
  AttachmentFiles: { FileName: string; ServerRelativeUrl: string }[];
}

export default class AnnouncementWebPart extends BaseClientSideWebPart<{}> {
  private announcements: AnnouncementItem[] = [];
  private categories: CategoryItem[] = [];
  private _isArabic: boolean = false;

  private selectedCategory: string = 'all';
  private sortBy: string = 'latest';
  // private searchTerm: string = '';

  protected async onInit(): Promise<void> {
    this._isArabic = window.location.pathname.toLowerCase().includes('/ar/');
    await this.fetchCategories();
    await this.fetchAnnouncements();
  }

  private async fetchCategories(): Promise<void> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Announcement Categories')/items?$select=Id,Title,Title_AR,Icon,AttachmentFiles/FileName,AttachmentFiles/ServerRelativeUrl&$expand=AttachmentFiles`;
    const res = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
    const data = await res.json();
    this.categories = data.value || [];
  }

  private async fetchAnnouncements(): Promise<void> {
    const now = new Date().toISOString();

    let filter = `Active eq 1 and Expires ge datetime'${now}'`;
    if (this.selectedCategory !== 'all') {
      filter += ` and Category/Id eq ${this.selectedCategory}`;
    }

    // if (this.searchTerm.trim()) {
    //   const term = this.searchTerm.replace(/'/g, "''"); // escape single quotes
    //   filter += ` and (substringof('${term}', Title) or substringof('${term}', Title_AR) or substringof('${term}', Description_EN) or substringof('${term}', Description_AR))`;
    // }

    let order = '$orderby=Begins desc';
    if (this.sortBy === 'oldest') order = '$orderby=Begins asc';
    if (this.sortBy === 'category') order = '$orderby=Category/Title asc';

    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Announcement')/items?$select=*,Category/Id,Category/Title,Category/Title_AR&$expand=Category&$filter=${filter}&${order}`;

    const res = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
    const data = await res.json();
    this.announcements = data.value || [];
    this.render();
  }

  public render(): void {
    const isArabic = this._isArabic;
    const t = (en: string, ar: string) => (isArabic ? ar : en);



    const announcementsHtml = this.announcements.length === 0
      ? `<div class="text-center p-4">${t('No announcements found.', 'لا توجد إعلانات.')}</div>`
      : this.announcements.map(item => {
        const title = isArabic ? item.Title_AR || item.Title : item.Title;
        const desc = isArabic ? item.Description_AR : item.Description_EN;
        const link = isArabic ? item.Link_AR?.Url : item.Link_EN?.Url;
        const category = this.categories.find(c => c.Id === item.Category?.Id);
        const iconUrl = category?.AttachmentFiles?.[0]?.ServerRelativeUrl || '';
        const dateFormatted = this.formatDate(item.Begins);

        return `
        <div class="announcement-item d-flex  py-3 px-0 border-bottom">
          <div class="announcement-icon me-3">
            <div class="rounded-circle d-flex align-items-center justify-content-center">
              ${iconUrl ? `<img src="${iconUrl}" alt="${item.Category?.Title}" />` : ''}
            </div>
          </div>
          <div class="announcement-content flex-grow-1">
            <div class="d-flex justify-content-between align-items-start">
              <div class="flex-grow-1">
                <h6 class="announcement-title fw-bold mb-1 head-color" style="font-size: 1rem;">${title}</h6>
                <p class="announcement-description text-color mb-1">${desc}</p>
                ${link ? `<a href="${link}" class="main-color text-decoration-none fw-bold" target="_blank">${t('Read More', 'اقرأ المزيد')}</a>` : ''}
              </div>
              <div class="text-end ms-3">
                <small class="text-color d-block" style="font-size: 0.8rem;">${dateFormatted}</small>
              </div>
            </div>
          </div>
        </div>
      `;
      }).join('');

    this.domElement.innerHTML = `
    <div>
      <div class="d-flex flex-wrap justify-content-end gap-3 align-items-end my-3">

        <!-- Filter Dropdown -->
        <div class="dropdown d-flex filter-dropdown">
          <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
            ${t('Filter by:', 'تصفية حسب') + ' ' + this.getCategoryLabel(this.selectedCategory)}
          </button>
          <ul class="dropdown-menu" aria-labelledby="filterDropdown">
            <li><a class="dropdown-item" href="#" data-cat="all">${t('All', 'الكل')}</a></li>
            ${this.categories.map(cat => `
              <li><a class="dropdown-item" href="#" data-cat="${cat.Id}">${isArabic ? cat.Title_AR : cat.Title}</a></li>
            `).join('')}
          </ul>
        </div>


        <!-- Sort Dropdown (Bootstrap style) -->
        <div class="dropdown ms-2 d-flex sort-dropdown">

          <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
            ${t('Sort by:', 'ترتيب حسب') + ' ' + this.getSortLabel(this.sortBy)}
          </button>
          <ul class="dropdown-menu" aria-labelledby="sortDropdown">
            <li><a class="dropdown-item" href="#" data-sort="latest">${t('Newest First', 'الأحدث')}</a></li>
            <li><a class="dropdown-item" href="#" data-sort="oldest">${t('Oldest First', 'الأقدم')}</a></li>
            <li><a class="dropdown-item" href="#" data-sort="category">${t('By Category', 'حسب الفئة')}</a></li>
          </ul>
        </div>

        

      </div>

      <!-- Announcement List -->
      <div class="card card-color p-3 mt-3">
        <div class="card-body p-0">
          ${announcementsHtml}
        </div>
      </div>
    </div>
  `;

    this.setupFilterEvents();
  }


  private setupFilterEvents(): void {
    const categoryItems = this.domElement.querySelectorAll('[data-cat]');
    const sortItems = this.domElement.querySelectorAll('.dropdown-item[data-sort]');

    categoryItems.forEach(item => {
      item.addEventListener('click', async (e) => {
        e.preventDefault();
        const newCategory = (item as HTMLElement).dataset.cat;
        if (newCategory && newCategory !== this.selectedCategory) {
          this.selectedCategory = newCategory;
          await this.fetchAnnouncements();
        }
      });
    });

    sortItems.forEach(item => {
      item.addEventListener('click', async (e) => {
        e.preventDefault();
        const newSort = (item as HTMLElement).dataset.sort;
        if (newSort && newSort !== this.sortBy) {
          this.sortBy = newSort;
          await this.fetchAnnouncements();
        }
      });
    });
  }



  private getSortLabel(value: string): string {
    const isArabic = this._isArabic;
    if (value === 'latest') return isArabic ? 'الأحدث' : 'Newest First';
    if (value === 'oldest') return isArabic ? 'الأقدم' : 'Oldest First';
    if (value === 'category') return isArabic ? 'حسب الفئة' : 'By Category';
    return value;
  }

  private formatDate(dateStr: string): string {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US');

  }

  private getCategoryLabel(value: string): string {
    if (value === 'all') return this._isArabic ? 'الكل' : 'All';
    const cat = this.categories.find(c => String(c.Id) === value);
    if (!cat) return '';
    return this._isArabic ? cat.Title_AR : cat.Title;
  }

}
