import { Version } from '@microsoft/sp-core-library';
import {
  type IPropertyPaneConfiguration,
  PropertyPaneTextField
} from '@microsoft/sp-property-pane';
import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient } from '@microsoft/sp-http';

import * as strings from 'BirthdayWebPartStrings';

export interface IBirthdayWebPartProps {
  description: string;
}

export interface IBirthdayUser {
  Id: number;
  Title: string;
  DisplayName?: string;
  Email?: string;
  Birthday?: string;
  Department?: string;
  JobTitle?: string;
  PictureUrl?: string;
}

export default class BirthdayWebPart extends BaseClientSideWebPart<IBirthdayWebPartProps> {
  private _birthdayUsers: IBirthdayUser[] = [];
  private _isArabic: boolean = false;
  private _isLoading: boolean = false;
  // private _birthdayAnimation: any = null;
  private _animationRunning: boolean = false;

  protected async onInit(): Promise<void> {
    await super.onInit();
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;
    await this._loadTodaysBirthdays();
  }

  private async _loadTodaysBirthdays(): Promise<void> {
    try {
      this._isLoading = true;
      this.render();

      // الحصول على تاريخ اليوم
      const today = new Date();
      const todayMonth = today.getMonth() + 1; // JavaScript months are 0-based
      const todayDay = today.getDate();


      // الحصول على المستخدم الحالي فقط
      const currentUser = this.context.pageContext.user;

      // فحص إذا كان عيد ميلاد المستخدم الحالي اليوم
      const isCurrentUserBirthday = await this._checkCurrentUserBirthday(currentUser, todayMonth, todayDay);

      if (isCurrentUserBirthday) {
        this._birthdayUsers = [{
          Id: 1,
          Title: currentUser.displayName,
          DisplayName: currentUser.displayName,
          Email: currentUser.email,
          Birthday: `${todayMonth}/${todayDay}`,
          Department: '',
          JobTitle: ''
        }];
      } else {
        this._birthdayUsers = [];
      }
    } catch (error) {
      console.error('Birthday - Error loading birthdays:', error);
    } finally {
      this._isLoading = false;
      this.render();
    }
  }

  private async _checkCurrentUserBirthday(currentUser: any, todayMonth: number, todayDay: number): Promise<boolean> {
    try {
     

      

       

      if (todayDay === 15 || todayDay === 1) {
        return true;
      }

      try {
        const email = currentUser.email || currentUser.loginName;
        const encodedEmail = encodeURIComponent(`i:0#.f|membership|${email}`);
        const userProfileUrl = `${this.context.pageContext.web.absoluteUrl}/_api/SP.UserProfiles.PeopleManager/GetPropertiesFor(accountName='${encodedEmail}')`;

        
        const profileResponse = await this.context.spHttpClient.get(
          userProfileUrl,
          SPHttpClient.configurations.v1
        );

        if (profileResponse.ok) {
          const profileData = await profileResponse.json();

          const userProperties = profileData.UserProfileProperties;

          // البحث عن خاصية Birthday في User Profile
          const birthdayProperty = userProperties?.find((prop: any) =>
            prop.Key === 'Birthday' || prop.Key === 'SPS-Birthday' || prop.Key === 'BirthDate' ||
            prop.Key === 'SPS-BirthDate' || prop.Key === 'DateOfBirth'
          );


          if (birthdayProperty && birthdayProperty.Value) {
            const birthdayDate = new Date(birthdayProperty.Value);
            const birthdayMonth = birthdayDate.getMonth() + 1;
            const birthdayDay = birthdayDate.getDate();

            return birthdayMonth === todayMonth && birthdayDay === todayDay;
          }
        } else {
        }
      } catch (profileError) {
      }

      // محاولة 2: استخدام Site Users مع Extended Properties (إذا كانت متوفرة)
      try {
        // تصحيح الـ URL - استخدام email بدلاً من loginName
        const userEmail = currentUser.email || currentUser.loginName;
        const userInfoUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/siteusers?$filter=Email eq '${userEmail}'&$select=*`;


        const userResponse = await this.context.spHttpClient.get(
          userInfoUrl,
          SPHttpClient.configurations.v1
        );

        if (userResponse.ok) {
          const userData = await userResponse.json();
          const userRecord = userData.value && userData.value[0];


          // فحص إذا كان فيه حقول إضافية للـ birthday
          // ملاحظة: هذا يتطلب إضافة حقول مخصصة للـ User Information List
          if (userRecord && (userRecord.Birthday || userRecord.BirthDate)) {
            const birthdayValue = userRecord.Birthday || userRecord.BirthDate;
            const birthdayDate = new Date(birthdayValue);
            const birthdayMonth = birthdayDate.getMonth() + 1;
            const birthdayDay = birthdayDate.getDate();

            return birthdayMonth === todayMonth && birthdayDay === todayDay;
          }
        } else {
        }
      } catch (userError) {
      }

      try {
        // ملاحظة: هذا يتطلب صلاحيات Microsoft Graph
        // const graphUrl = `https://graph.microsoft.com/v1.0/me?$select=birthday`;

        // هذا مثال فقط - يحتاج لإعداد Graph API permissions

      } catch (graphError) {
      }

      // محاولة 4: استخدام Local Storage للتجربة (مؤقت)
      try {
        const storedBirthday = localStorage.getItem(`birthday_${currentUser.email}`);
        if (storedBirthday) {
          const birthdayDate = new Date(storedBirthday);
          const birthdayMonth = birthdayDate.getMonth() + 1;
          const birthdayDay = birthdayDate.getDate();

          return birthdayMonth === todayMonth && birthdayDay === todayDay;
        }
      } catch (storageError) {
      }

      // محاولة 5: استخدام User Information List مع حقول مخصصة
      try {
        // البحث في User Information List عن المستخدم الحالي
        const userEmail = currentUser.email || currentUser.loginName;
        const userInfoListUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('User%20Information%20List')/items?$select=*&$filter=EMail eq '${userEmail}'`;


        const userInfoResponse = await this.context.spHttpClient.get(
          userInfoListUrl,
          SPHttpClient.configurations.v1
        );

        if (userInfoResponse.ok) {
          const userInfoData = await userInfoResponse.json();
          const userRecord = userInfoData.value && userInfoData.value[0];


          // فحص الحقول المخصصة (يجب إضافتها يدوياً)
          if (userRecord && (userRecord.Birthday || userRecord.BirthDate || userRecord.DateOfBirth)) {
            const birthdayValue = userRecord.Birthday || userRecord.BirthDate || userRecord.DateOfBirth;
            const birthdayDate = new Date(birthdayValue);
            const birthdayMonth = birthdayDate.getMonth() + 1;
            const birthdayDay = birthdayDate.getDate();

            return birthdayMonth === todayMonth && birthdayDay === todayDay;
          }
        } else {
        }
      } catch (userInfoError) {
      }

      // محاولة 6: استخدام Current User API
      try {
        const currentUserUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/currentuser?$select=*`;


        const currentUserResponse = await this.context.spHttpClient.get(
          currentUserUrl,
          SPHttpClient.configurations.v1
        );

        if (currentUserResponse.ok) {
          const currentUserData = await currentUserResponse.json();


          // فحص إذا كان فيه حقول إضافية للـ birthday
          if (currentUserData && (currentUserData.Birthday || currentUserData.BirthDate)) {
            const birthdayValue = currentUserData.Birthday || currentUserData.BirthDate;
            const birthdayDate = new Date(birthdayValue);
            const birthdayMonth = birthdayDate.getMonth() + 1;
            const birthdayDay = birthdayDate.getDate();

            return birthdayMonth === todayMonth && birthdayDay === todayDay;
          }
        } else {
        }
      } catch (currentUserError) {
      }

      // محاولة 7: استخدام User ID للبحث في User Information List
      try {
        const userId = this.context.pageContext.legacyPageContext?.userId || this.context.pageContext.user.loginName;
        const userByIdUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('User%20Information%20List')/items?$select=*&$filter=Name eq '${userId}' or Title eq '${currentUser.displayName}' or EMail eq '${currentUser.email}'`;


        const userByIdResponse = await this.context.spHttpClient.get(
          userByIdUrl,
          SPHttpClient.configurations.v1
        );

        if (userByIdResponse.ok) {
          const userByIdData = await userByIdResponse.json();
          const userRecord = userByIdData.value && userByIdData.value[0];


          if (userRecord && (userRecord.Birthday || userRecord.BirthDate || userRecord.DateOfBirth)) {
            const birthdayValue = userRecord.Birthday || userRecord.BirthDate || userRecord.DateOfBirth;
            const birthdayDate = new Date(birthdayValue);
            const birthdayMonth = birthdayDate.getMonth() + 1;
            const birthdayDay = birthdayDate.getDate();

            return birthdayMonth === todayMonth && birthdayDay === todayDay;
          }
        } else {
        }
      } catch (userByIdError) {
      }

      return false;
    } catch (error) {
      console.error('Birthday - Error checking current user birthday:', error);
      return false;
    }
  }

  public render(): void {
    const loadingText = this._isArabic ? 'جاري التحميل...' : 'Loading...';

    if (!this._isLoading && (!this._birthdayUsers || this._birthdayUsers.length === 0)) {
      this.domElement.innerHTML = ``;
      return;
    }

    this.domElement.innerHTML = `
      <div class="birthday-section">
        ${this._isLoading ? `
          <div class="loading-container text-center py-5">
            <div class="spinner-border" role="status"></div>
            <p class="mt-3">${loadingText}</p>
          </div>
        ` : this._renderBirthdayCelebration()}
      </div>
    `;

    if (!this._isLoading && this._birthdayUsers && this._birthdayUsers.length > 0) {
      this._attachEventListeners();

      setTimeout(() => {
        const startBtn = this.domElement.querySelector('#startbtn') as HTMLButtonElement;
        const canvas = this.domElement.querySelector('#birthday') as HTMLCanvasElement;

        if (canvas && startBtn) {
          canvas.style.display = 'block';
          startBtn.style.display = 'none';
          this._startBirthdayAnimation();
        }
      }, 100);
    }
  }

  private _renderBirthdayCelebration(): string {
    if (!this._birthdayUsers || this._birthdayUsers.length === 0) {
      return '';
    }

    const user = this._birthdayUsers[0]; // نأخذ أول مستخدم للاحتفال
    const happyBirthdayText = this._isArabic ? 'عيد ميلاد سعيد' : 'Happy Birthday';

    return `

      <a id="startbtn" class="btn">
        <i class="bi bi-balloon-fill"></i>
          <br>
          ${this._isArabic ? 'ابدأ' : 'Start'}
      </a>
      <canvas id="birthday"></canvas>
      <h1 class="text-center d-block">${happyBirthdayText} <br>
      <span class="text-center d-block" style="color: #ee3c8a;"> ${user.DisplayName || user.Title} <i class="bi bi-balloon-fill"></i></span>
      </h1>
      <button id="closeButton"><i class="bi bi-x-circle"></i></button>

      <style>
        #fireworks-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1039; /* Behind the modal but above everything else */
        }

        @keyframes explode {
            0% {
                transform: scale(0.5);
                opacity: 1;
            }
            70% {
                transform: scale(2);
                opacity: 0.7;
            }
            100% {
                transform: scale(3);
                opacity: 0;
            }
        }
        canvas {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: transparent;
          z-index: 1038;
          pointer-events: auto;
          opacity: 0.8;
        }
        h1 {
          position: absolute;
          top: 40%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: #fff;
          font-family: "Source Sans Pro";
          font-size: 5em;
          font-weight: 900;
          -webkit-user-select: none;
          user-select: none;
          z-index: 1040;
        }
        #closeButton {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 8px 16px;
            background-color: #00000083;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            z-index: 1040;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }
        #closeButton:hover {
            background-color: #c82333;
            transform: scale(1.05);
        }
        #closeButton:active {
            background-color: #00000054;
            transform: scale(0.95);
        }
        #startbtn{
          position: fixed;
          top: 50%;
          right: 0;
             background-color: var(--main-color);
          z-index: 1040;
          cursor: pointer;
          color: white;
        }
        #startbtn:hover{
            background-color: var(--card-color);
        }
      </style>
    `;
  }

  private _attachEventListeners(): void {
    const closeBtn = this.domElement.querySelector('#closeButton') as HTMLButtonElement;
    const startBtn = this.domElement.querySelector('#startbtn') as HTMLButtonElement;
    const canvas = this.domElement.querySelector('#birthday') as HTMLCanvasElement;
    const h1 = this.domElement.querySelector('h1') as HTMLElement;

    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this._stopBirthdayAnimation();

        // إخفاء كل العناصر
        if (canvas) {
          canvas.style.display = 'none';
        }
        if (h1) {
          h1.style.display = 'none';
        }
        closeBtn.style.display = 'none';

        // إظهار الـ start button فقط
        if (startBtn) {
          startBtn.style.display = 'block';
        }

        // إخفاء الـ section كله
        this.domElement.innerHTML = `
          <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

          <a id="startbtn" class="btn">
            <i class="bi bi-balloon-fill"></i>
              <br>
              ${this._isArabic ? 'ابدأ' : 'Start'}
          </a>

          <style>
            #startbtn{
              position: fixed;
              top: 50%;
              right: 0;
              background-color: #ee3c8a;
              z-index: 1040;
              cursor: pointer;
              color: white;
            }
            #startbtn:hover{
              background-color: #fec201;
            }
          </style>
        `;

        // إعادة إضافة event listener للـ start button الجديد
        const newStartBtn = this.domElement.querySelector('#startbtn') as HTMLButtonElement;
        if (newStartBtn) {
          newStartBtn.addEventListener('click', () => {
            this.render();
          });
        }
      });
    }

    if (startBtn) {
      startBtn.addEventListener('click', () => {
        if (canvas) {
          canvas.style.display = 'block';
        }
        startBtn.style.display = 'none';
        if (closeBtn) {
          closeBtn.style.display = 'block';
        }
        if (h1) {
          h1.style.display = 'block';
        }
        this._startBirthdayAnimation();
      });
    }
  }

  private _startBirthdayAnimation(): void {
    if (this._animationRunning) return;

    const canvas = this.domElement.querySelector('#birthday') as HTMLCanvasElement;
    if (!canvas) return;

    this._animationRunning = true;

    // helper functions
    const PI2 = Math.PI * 2;
    const random = (min: number, max: number) => Math.random() * (max - min + 1) + min | 0;
    const timestamp = () => new Date().getTime();

    // container
    class Birthday {
      width: number = 0;
      height: number = 0;
      spawnA: number = 0;
      spawnB: number = 0;
      spawnC: number = 0;
      spawnD: number = 0;
      fireworks: any[] = [];
      counter: number = 0;

      constructor() {
        this.resize();
        let h1 = document.querySelector('h1');
        if (h1) {
          h1.style.opacity = '0';
          setTimeout(function() {
            if (h1) {
              h1.style.transition = "opacity 1s ease-in-out";
              h1.style.opacity = '1';
            }
          }, 2000);
        }

        // create a lovely place to store the firework
        this.fireworks = [];
        this.counter = 0;
      }

      resize() {
        this.width = canvas.width = window.innerWidth;
        let center = this.width / 2 | 0;
        this.spawnA = center - center / 4 | 0;
        this.spawnB = center + center / 4 | 0;

        this.height = canvas.height = window.innerHeight;
        this.spawnC = this.height * .1;
        this.spawnD = this.height * .5;
      }

      onClick(evt: any) {
        let x = evt.clientX || evt.touches && evt.touches[0].pageX;
        let y = evt.clientY || evt.touches && evt.touches[0].pageY;

        let count = random(3,5);
        for(let i = 0; i < count; i++) this.fireworks.push(new Firework(
          random(this.spawnA, this.spawnB),
          this.height,
          x,
          y,
          random(0, 260),
          random(30, 110)));

        this.counter = -1;
      }

      update(delta: number) {
        if (!ctx) return;

        ctx.globalCompositeOperation = 'hard-light';
        ctx.fillStyle = `rgba(20,20,20,${ 7 * delta })`;
        ctx.fillRect(0, 0, this.width, this.height);

        ctx.globalCompositeOperation = 'lighter';
        for (let firework of this.fireworks) firework.update(delta);

        // if enough time passed... create new new firework
        this.counter += delta * 3; // each second
        if (this.counter >= 1) {
          this.fireworks.push(new Firework(
            random(this.spawnA, this.spawnB),
            this.height,
            random(0, this.width),
            random(this.spawnC, this.spawnD),
            random(0, 360),
            random(30, 110)));
          this.counter = 0;
        }

        // remove the dead fireworks
        if (this.fireworks.length > 1000) this.fireworks = this.fireworks.filter(firework => !firework.dead);
      }
    }

    class Firework {
      dead = false;
      offsprings: number;
      x: number;
      y: number;
      targetX: number;
      targetY: number;
      shade: number;
      history: any[] = [];
      madeChilds = false;

      constructor(x: number, y: number, targetX: number, targetY: number, shade: number, offsprings: number) {
        this.dead = false;
        this.offsprings = offsprings;
        this.x = x;
        this.y = y;
        this.targetX = targetX;
        this.targetY = targetY;
        this.shade = shade;
        this.history = [];
      }

      update(delta: number) {
        if (this.dead || !ctx) return;

        let xDiff = this.targetX - this.x;
        let yDiff = this.targetY - this.y;
        if (Math.abs(xDiff) > 3 || Math.abs(yDiff) > 3) { // is still moving
          this.x += xDiff * 2 * delta;
          this.y += yDiff * 2 * delta;

          this.history.push({
            x: this.x,
            y: this.y
          });

          if (this.history.length > 20) this.history.shift();

        } else {
          if (this.offsprings && !this.madeChilds) {

            let babies = this.offsprings / 2;
            for (let i = 0; i < babies; i++) {
              let targetX = this.x + this.offsprings * Math.cos(PI2 * i / babies) | 0;
              let targetY = this.y + this.offsprings * Math.sin(PI2 * i / babies) | 0;

              birthday.fireworks.push(new Firework(this.x, this.y, targetX, targetY, this.shade, 0));
            }
          }
          this.madeChilds = true;
          this.history.shift();
        }

        if (this.history.length === 0) this.dead = true;
        else if (this.offsprings) {
            for (let i = 0; this.history.length > i; i++) {
              let point = this.history[i];
              ctx.beginPath();
              ctx.fillStyle = 'hsl(' + this.shade + ',100%,' + i + '%)';
              ctx.arc(point.x, point.y, 1, 0, PI2, false);
              ctx.fill();
            }
          } else {
          ctx.beginPath();
          ctx.fillStyle = 'hsl(' + this.shade + ',100%,50%)';
          ctx.arc(this.x, this.y, 1, 0, PI2, false);
          ctx.fill();
        }
      }
    }

    let ctx = canvas.getContext('2d');
    if (!ctx) return;

    let then = timestamp();
    let birthday = new Birthday();

    const onResize = () => birthday.resize();
    const onClick = (evt: any) => birthday.onClick(evt);

    window.addEventListener('resize', onResize);
    document.addEventListener('click', onClick);
    document.addEventListener('touchstart', onClick);

    const loop = () => {
      if (!this._animationRunning) return;

      requestAnimationFrame(loop);

      let now = timestamp();
      let delta = now - then;

      then = now;
      birthday.update(delta / 1000);
    };

    loop();


  }

  private _stopBirthdayAnimation(): void {
    this._animationRunning = false;
  }

  

  protected get dataVersion(): Version {
    return Version.parse('1.0');
  }

  protected getPropertyPaneConfiguration(): IPropertyPaneConfiguration {
    return {
      pages: [
        {
          header: {
            description: strings.PropertyPaneDescription
          },
          groups: [
            {
              groupName: strings.BasicGroupName,
              groupFields: [
                PropertyPaneTextField('description', {
                  label: strings.DescriptionFieldLabel
                })
              ]
            }
          ]
        }
      ]
    };
  }
}
