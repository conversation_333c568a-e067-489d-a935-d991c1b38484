{"$schema": "https://developer.microsoft.com/json-schemas/spfx/client-side-web-part-manifest.schema.json", "id": "f1e2d3c4-5678-9012-3456-789012345678", "alias": "CategoryDetailsWebPart", "componentType": "WebPart", "version": "*", "manifestVersion": 2, "requiresCustomScript": false, "supportedHosts": ["SharePointWebPart", "TeamsPersonalApp", "TeamsTab", "SharePointFullPage"], "preconfiguredEntries": [{"groupId": "5c03119e-3074-46fd-976b-c60198311f70", "group": {"default": "Advanced"}, "title": {"default": "Category Details"}, "description": {"default": "Display complete category data from localStorage"}, "officeFabricIconFontName": "Database", "properties": {"description": "Category Details WebPart"}}]}