# فلترة العروض بناءً على الـ Supplier

## المشكلة
- مفيش `categoryId` في العروض
- مفيش parameters في URL للفلترة
- محتاجين نعمل فلترة بناءً على الـ supplier

## الحل
عمل loop على العروض وجلب الـ supplier من كل عرض، ثم ربط الـ supplier بالفئة.

## الكود المطلوب

### 1. تعديل Offer Interface
```typescript
export interface Offer {
  id: string;
  title: string;
  bannerUrl: string;
  badgeTitle: string;
  discountType: string;
  amountCAP: number;
  amountPercentage: number;
  expiryDate: string;
  supplier: any; // استخدام any لأن البنية غير واضحة
}
```

### 2. فلترة العروض بناءً على الـ Supplier
```typescript
private filterOffersByCategory(offers: any[], targetCategoryId: string): any[] {
  if (!targetCategoryId || !offers.length) return offers;

  console.log('Filtering offers by category:', targetCategoryId);
  console.log('Total offers before filtering:', offers.length);

  // عمل loop على العروض وجلب supplier ID من كل عرض
  const filteredOffers = offers.filter(offer => {
    if (!offer.supplier) return false;

    // طباعة معلومات الـ supplier للتشخيص
    console.log('Offer:', offer.title, 'Supplier:', offer.supplier);

    // جرب طرق مختلفة للحصول على supplier ID
    const supplierId = offer.supplier.id || offer.supplier.supplierId || offer.supplier.Id;
    
    if (supplierId) {
      console.log('Supplier ID found:', supplierId);
      
      // ربط الـ supplier بالفئة
      return this.isSupplierInCategory(supplierId, targetCategoryId);
    }

    return false;
  });

  console.log('Filtered offers count:', filteredOffers.length);
  return filteredOffers;
}
```

### 3. ربط الـ Supplier بالفئة
```typescript
private isSupplierInCategory(supplierId: string, categoryId: string): boolean {
  // مثال على mapping ثابت للاختبار:
  const supplierCategoryMapping: { [supplierId: string]: string } = {
    'supplier1': 'category1',
    'supplier2': 'category2',
    // أضف المزيد حسب البيانات الفعلية
  };

  const supplierCategory = supplierCategoryMapping[supplierId];
  const isMatch = supplierCategory === categoryId;
  
  console.log(`Supplier ${supplierId} -> Category ${supplierCategory}, Target: ${categoryId}, Match: ${isMatch}`);
  
  return isMatch;
}
```

### 4. تطبيق الفلترة في fetchOffers
```typescript
const resp: HttpClientResponse = await this.context.httpClient.post(url, HttpClient.configurations.v1, options);
if (resp.ok) {
  const data = await resp.json();
  let offers = data.data.items || [];
  
  console.log('Offers fetched from API:', offers.length);
  
  // فلترة العروض محلياً بناءً على الـ supplier إذا كان فيه فلتر فئة
  if (this.selectedCategory) {
    console.log('Applying category filter:', this.selectedCategory);
    offers = this.filterOffersByCategory(offers, this.selectedCategory);
  }
  
  this.offers = offers;
  this.totalOffers = data.data.totalPages;
  
  console.log('Final offers count:', this.offers.length);
}
```

## دوال التشخيص والاختبار

### 1. تحليل الـ Suppliers في العروض
```typescript
public analyzeOfferSuppliers(): void {
  console.log('=== Analyzing Offer Suppliers ===');
  console.log('Total offers:', this.offers.length);

  this.offers.forEach((offer, index) => {
    console.log(`\nOffer ${index + 1}:`);
    console.log('Title:', offer.title);
    console.log('Supplier object:', offer.supplier);

    if (offer.supplier) {
      const supplierInfo = {
        name: offer.supplier.name,
        id: offer.supplier.id || 'NO_ID',
        supplierId: offer.supplier.supplierId || 'NO_SUPPLIER_ID',
        Id: offer.supplier.Id || 'NO_CAPITAL_ID',
        logo: offer.supplier.logo || 'NO_LOGO'
      };

      console.log('Supplier info:', supplierInfo);
    }
  });
}
```

### 2. اختبار الفلتر
```typescript
public testCategoryFilter(categoryId: string = 'test-category'): void {
  console.log('Testing category filter with:', categoryId);
  
  // تعيين الفلتر
  this.setCategoryFilter(categoryId);
  
  // عرض النتائج
  setTimeout(() => {
    console.log('Filter test results:');
    console.log('Selected category:', this.selectedCategory);
    console.log('Filtered offers count:', this.offers.length);
  }, 1000);
}
```

### 3. إنشاء Mapping تلقائي
```typescript
public createSupplierCategoryMapping(): void {
  const mapping: { [supplierName: string]: string } = {};
  
  this.offers.forEach(offer => {
    if (offer.supplier && offer.supplier.name) {
      const supplierName = offer.supplier.name.toLowerCase();
      
      // ربط الـ suppliers بفئات بناءً على أسمائهم
      if (supplierName.includes('car') || supplierName.includes('auto')) {
        mapping[supplierName] = 'automobile';
      } else if (supplierName.includes('food') || supplierName.includes('restaurant')) {
        mapping[supplierName] = 'food';
      } else if (supplierName.includes('fashion') || supplierName.includes('clothing')) {
        mapping[supplierName] = 'fashion';
      } else {
        mapping[supplierName] = 'general';
      }
    }
  });

  console.log('Generated mapping:');
  Object.entries(mapping).forEach(([supplier, category]) => {
    console.log(`${supplier} -> ${category}`);
  });
}
```

## كيفية الاستخدام

### 1. تحليل البيانات أولاً
```javascript
// في Console المتصفح بعد تحميل الصفحة
window.mazayaWebPart.analyzeOfferSuppliers();
```

### 2. إنشاء Mapping
```javascript
window.mazayaWebPart.createSupplierCategoryMapping();
```

### 3. اختبار الفلتر
```javascript
window.mazayaWebPart.testCategoryFilter('automobile');
```

### 4. تعيين فلتر يدوياً
```javascript
window.mazayaWebPart.setCategoryFilter('food');
```

## الخطوات المطلوبة

### 1. فهم بنية البيانات
- شغل `analyzeOfferSuppliers()` لفهم بنية الـ supplier
- شوف إيه الـ fields المتاحة في كل supplier

### 2. إنشاء Mapping صحيح
- بناءً على البيانات الفعلية، اعمل mapping بين supplier IDs والفئات
- ممكن يكون من API منفصل أو من قاعدة بيانات

### 3. تطبيق الفلترة
- استخدم `setCategoryFilter()` لتعيين الفلتر
- العروض هتتفلتر تلقائياً

## مثال كامل

```javascript
// 1. تحليل البيانات
window.mazayaWebPart.analyzeOfferSuppliers();

// 2. إنشاء mapping تجريبي
window.mazayaWebPart.createSupplierCategoryMapping();

// 3. اختبار الفلتر
window.mazayaWebPart.testCategoryFilter('automobile');

// 4. مسح الفلتر
window.mazayaWebPart.clearCategoryFilter();
```

## النتيجة المتوقعة

بعد تطبيق الكود ده:
- ✅ هتقدر تعمل loop على العروض
- ✅ هتجيب الـ supplier من كل عرض
- ✅ هتربط الـ supplier بالفئة
- ✅ هتفلتر العروض بناءً على الفئة المحددة

الآن النظام جاهز للفلترة بناءً على الـ supplier! 🚀
