
import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';

export interface IFAQItem {
  Id: number;
  Title: string;
  Question_AR?: string;
  Answer_EN?: string;
  Answer_AR?: string;
}

export interface IFaQsWebPartProps {
  description: string;
}

export default class FaQsWebPart extends BaseClientSideWebPart<IFaQsWebPartProps> {
  private _faqItems: IFAQItem[] = [];
  private _isArabic: boolean = false;
  private _isLoading: boolean = false;
  private _currentSiteUrl: string = '';

  public async onInit(): Promise<void> {
    await super.onInit();

    // تحديد اللغة بناءً على URL أو إعدادات المتصفح
    this._isArabic = this.context.pageContext.cultureInfo.currentCultureName.startsWith('ar');

    // تحديد الـ site URL الحالي للـ links
    this._currentSiteUrl = this._getCurrentSiteUrl();

    await this._loadFAQs();
  }

  private _getCurrentSiteUrl(): string {
    try {
      const currentUrl = window.location.pathname.toLowerCase();

      // إذا كان في الموقع الرئيسي
      if (currentUrl.includes('/sites/intranet-qm/sitepages/') && !this._isInSubsite(currentUrl)) {
        return '/sites/intranet-qm';
      }

      // إذا كان في subsite
      const subsiteMatch = currentUrl.match(/\/sites\/intranet-qm\/([^\/]+)\//);
      if (subsiteMatch && subsiteMatch[1] !== 'sitepages') {
        const subsitePath = `/sites/intranet-qm/${subsiteMatch[1]}`;
        return subsitePath;
      }

      // افتراضي: الموقع الرئيسي
      return '/sites/intranet-qm';
    } catch (error) {
      console.error('FAQs - Error determining site URL:', error);
      return '/sites/intranet-qm';
    }
  }

  private _isInSubsite(url: string): boolean {
    // تحقق من وجود subsite في الـ URL
    const subsitePattern = /\/sites\/intranet-qm\/([^\/]+)\/sitepages\//i;
    const match = url.match(subsitePattern);

    if (match && match[1]) {
      const potentialSubsite = match[1].toLowerCase();
      // تجاهل 'ar' لأنه language code وليس subsite
      return potentialSubsite !== 'ar';
    }

    return false;
  }

  public render(): void {
    const loadingText = this._isArabic ? 'جاري التحميل...' : 'Loading...';
    const noFAQsText = this._isArabic ? 'لا توجد أسئلة شائعة' : 'No FAQs found';
    const errorText = this._isArabic ? 'حدث خطأ أثناء تحميل البيانات' : 'Error loading data';

    this.domElement.innerHTML = `
    <div class="faq-section">
    <div class="card section-card card-color pt-2 mt-3 bg-remover">
      <div class="container-fluid ">
        <h2 class="fw-bold tittle head-color ">${this._isArabic ? 'الأسئلة الشائعة' : 'FAQs'}</h2>
        ${this._isLoading ? `
          <div class="loading-container">
            <div class="spinner-border" role="status"></div>
            <p class="mt-3">${loadingText}</p>
          </div>
        ` : this._renderFAQs(noFAQsText, errorText)}
      </div>
      </div>
    </div>
    `;

    // إضافة event listeners للـ accordion
    if (!this._isLoading && this._faqItems && this._faqItems.length > 0) {
      this._attachEventListeners();
    }
  }

  private getListEndpoint(listTitle: string, selectFields?: string, expandFields?: string, filterQuery?: string, orderQuery?: string, topQuery?: string, forceMainSite?: boolean): string {
    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    const siteUrl = this.context.pageContext.site.absoluteUrl;
    const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    const baseUrl = forceMainSite
      ? `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
      : (isSubSite
        ? `${currentWebUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
        : `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`);

    const queryParams: string[] = [];

    if (selectFields) {
      queryParams.push(`$select=${selectFields}`);
    }

    if (expandFields) {
      queryParams.push(`$expand=${expandFields}`);
    }

    if (filterQuery) {
      queryParams.push(filterQuery);
    }

    if (orderQuery) {
      queryParams.push(orderQuery);
    }

    if (topQuery) {
      queryParams.push(topQuery);
    }

    return queryParams.length > 0
      ? `${baseUrl}?${queryParams.join('&')}`
      : baseUrl;
  }

  private async _loadFAQs(): Promise<void> {
    try {
      this._isLoading = true;
      this.render();

      const listName = 'FAQs';
      const selectFields = '*';
      const topQuery = '$top=5'; 

      // استخدام getListEndpoint للتعامل مع main site والـ subsites
      const apiUrl = this.getListEndpoint(listName, selectFields, undefined , undefined, undefined, topQuery);

      

      const response: SPHttpClientResponse = await this.context.spHttpClient.get(
        apiUrl,
        SPHttpClient.configurations.v1
      );

      if (response.ok) {
        const data = await response.json();
        this._faqItems = data.value || [];
      } else {
        console.error('FAQs - Failed to load items:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('FAQs - Error loading items:', error);
    } finally {
      this._isLoading = false;
      this.render();
    }
  }



  private _renderFAQs(noFAQsText: string, errorText: string): string {
    if (!this._faqItems || this._faqItems.length === 0) {
      return `
        <div class="no-faqs">
          <i class="bi bi-question-circle"></i>
          <p>${noFAQsText}</p>
        </div>
      `;
    }

    const viewAllText = this._isArabic ? 'عرض الكل' : 'View All';

    return `
      <div class="faq-accordion">
        ${this._faqItems.map((faq, index) => this._renderFAQItem(faq, index)).join('')}
        <div class="text-end mt-3 card-footer">
        <a href="${this._currentSiteUrl}/SitePages/FAQs.aspx" class="text-decoration-none fw-bold  btn-main-link">${viewAllText} → </a>
        </div>
        
      </div>
    `;
  }

  private _renderFAQItem(faq: IFAQItem, index: number): string {
    const question = this._isArabic ? faq.Question_AR || faq.Title : faq.Title || '';
    const answer = this._isArabic ? faq.Answer_AR || faq.Answer_EN || '' : faq.Answer_EN || '';

    return `
    <div class="accordion my-4" id="exhibitionsAccordion">
    <div class="accordion-item border-0 border-bottom">
              <h2 class="accordion-header card-color" id="headingThree">
                <button class="accordion-button fw-bold bg-transparent shadow-none head-color px-0 py-3 d-flex justify-content-between align-items-center collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                  <span class="flex-grow-1 text-start">${question}</span>
                  <i class="bi bi-chevron-down ms-2 text-color"></i>
                </button>
              </h2>
              <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#exhibitionsAccordion" style="">
                <div class="accordion-body text-color px-0 pb-3">
                    ${answer}
                </div>
              </div>
            </div>
          </div>  
    `;
  }

  private _attachEventListeners(): void {
    const questions = this.domElement.querySelectorAll('.faq-question');

    questions.forEach((question) => {
      question.addEventListener('click', (event) => {
        event.preventDefault();

        const button = event.currentTarget as HTMLButtonElement;
        const index = button.getAttribute('data-index');

        if (index !== null) {
          this._toggleFAQ(parseInt(index));
        }
      });
    });

    // View All link
    const viewAllLink = this.domElement.querySelector('.view-all-link');
    if (viewAllLink) {
      viewAllLink.addEventListener('click', (event) => {
        event.preventDefault();
        // يمكن إضافة navigation لصفحة FAQs الكاملة هنا
      });
    }
  }

  private _toggleFAQ(index: number): void {
    const questions = this.domElement.querySelectorAll('.faq-question');
    const answers = this.domElement.querySelectorAll('.faq-answer');
    const icons = this.domElement.querySelectorAll('.faq-icon');

    // إغلاق جميع الـ FAQs الأخرى
    questions.forEach((q, i) => {
      if (i !== index) {
        q.classList.remove('active');
        answers[i]?.classList.remove('show');
        icons[i]?.classList.remove('rotated');
      }
    });

    // تبديل الـ FAQ المحدد
    const currentQuestion = questions[index];
    const currentAnswer = answers[index];
    const currentIcon = icons[index];

    if (currentQuestion && currentAnswer && currentIcon) {
      const isActive = currentQuestion.classList.contains('active');

      if (isActive) {
        currentQuestion.classList.remove('active');
        currentAnswer.classList.remove('show');
        currentIcon.classList.remove('rotated');
      } else {
        currentQuestion.classList.add('active');
        currentAnswer.classList.add('show');
        currentIcon.classList.add('rotated');
      }
    }
  }
}