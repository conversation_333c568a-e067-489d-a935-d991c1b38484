import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';



export interface IEmployeeWebPartProps {
  description: string;
}

export default class EmployeeWebPart extends BaseClientSideWebPart<IEmployeeWebPartProps> {
  private _isArabic: boolean = false;

  public render(): void {
      this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;
    this.domElement.innerHTML = `<section class="benefits-section h-100">
      <div class="card section-card card-color h-100 p-3">
        <div class="container-fluid">
          <h2 class="fw-bold tittle head-color">Enjoy Employee Benefits</h2>
          <div class="row g-3 mb-2" id="benefits-container">
            <p>Loading benefits...</p>
          </div>
        </div>
      </div>
    </section>`;

    this._getBenefits();
  }

  private async _getBenefits(): Promise<void> {
    const listUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Employee Benefits')/items`;
  
    try {
      const response = await this.context.spHttpClient.get(listUrl, SPHttpClient.configurations.v1);
      const data = await response.json();
      const items = data.value;
      let html: string = '';
  
      for (const item of items) {
        let imageUrl: string | undefined;
  
        const attachments = await this.getAttachments(item.Id);
        if (attachments.length > 0) {
          imageUrl = attachments[0]; 
        } else {
          try {
            if (item.Image && typeof item.Image === 'string') {
              const imgData = JSON.parse(item.Image);
              if (imgData?.serverUrl && imgData?.serverRelativeUrl) {
                imageUrl = imgData.serverUrl + imgData.serverRelativeUrl;
              }
            } else if (item.Image) {
              imageUrl = item.Image;
            }
          } catch (e) {
            console.warn("Invalid Image format:", item.Image);
          }
        }
  
        const link = this._isArabic ? item?.Link_AR?.Url || '#' : item?.Link_EN?.Url || '#';
        const title = this._isArabic ? item.Title_AR || '' : item.Title || '';
        const description = this._isArabic ? item.Description_AR || '' : item.Description_EN || '';
        const shortDesc = description.split(' ').slice(0, 3).join(' ') + (description.split(' ').length > 3 ? '...' : '');
  
        html += `
          <a href="${link || '#'}" class="col-lg-4 col-md-6 text-decoration-none">
            <div class="border d-flex justify-content-start align-items-center p-3 ${this._isArabic ? 'flex-row-reverse text-end' : ''}">
              <img class="mx-3" src="${imageUrl || require('./assets/img.jpg')}" alt="${title || ''}">
              <div class="flex-column d-flex">
                <h3 class="head-color mt-3">${title || ''}</h3>
                <p class="text-color">${shortDesc || ''}</p>
              </div>
            </div>
          </a>
        `;
      }
  
      const container: HTMLElement | null = this.domElement.querySelector('#benefits-container');
      if (container) {
        container.innerHTML = html;
      }
  
      const heading = this.domElement.querySelector('h2');
      if (heading && this._isArabic) {
        heading.textContent = "استمتع بمزايا الموظفين";
        heading.classList.add("text-end");
      }
  
    } catch (error) {
      console.error("Error fetching Employee Benefits list:", error);
      const container: HTMLElement | null = this.domElement.querySelector('#benefits-container');
      if (container) {
        container.innerHTML = `<p class="text-danger">فشل في تحميل البيانات. حاول مرة أخرى لاحقاً.</p>`;
      }
    }
  }
  

  getAttachments(itemId: number): Promise<string[]> {
    const listName = "Employee Benefits";
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('${listName}')/items(${itemId})/AttachmentFiles`;

    return this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        const attachments: string[] = data.value.map((file: any) => {
          return `${file.ServerRelativeUrl}`;
        });
        return attachments;
      })
      .catch((error:any) => {
        console.error('Error fetching attachments:', error);
        return [];
      });
  }
  










}
