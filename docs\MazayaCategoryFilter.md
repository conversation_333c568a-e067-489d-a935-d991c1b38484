# فلتر العروض بالفئة - بدون localStorage

## نظرة عامة
تم تطوير نظام فلتر العروض بناءً على categoryId من URL parameters فقط، بدون استخدام localStorage.

## كيفية العمل

### 1. تمرير categoryId في URL
```
/sites/intranet-qm/SitePages/Mazaya.aspx?categoryId=123
```

### 2. قراءة categoryId من URL
```javascript
private checkForCategoryFilter(): void {
  const urlParams = new URLSearchParams(window.location.search);
  const categoryIdFromUrl = urlParams.get('categoryId');
  
  if (categoryIdFromUrl) {
    this.selectedCategory = categoryIdFromUrl;
    console.log('Category filter from URL:', categoryIdFromUrl);
  }
}
```

### 3. إرسال categoryId للـ API
```javascript
const body = JSON.stringify({
  pageNumber: this.currentPage,
  pageSize: this.itemsPerPage,
  search: {
    name: this.searchTerm || null,
    supplierId: this.selectedSupplier || null,
    startDate: this.fromDate || defaultStartDate,
    endDate: this.toDate || defaultEndDate,
    offerId: null,
    categoryId: this.selectedCategory || null, // ✅ هنا
    offerType: this.selectedOfferType || null
  },
  orderBy: '',
  isDescending: true
});
```

## الفلترة

### إذا الـ API دعم الفلترة
- الـ API هيرجع العروض مفلترة بالـ categoryId
- مفيش حاجة إضافية مطلوبة

### إذا الـ API مدعمش الفلترة
```javascript
// فلترة محلية بناءً على categoryId في العرض
if (this.selectedCategory && offers.length > 0) {
  offers = offers.filter((offer: any) => {
    return offer.categoryId && offer.categoryId === this.selectedCategory;
  });
}
```

## واجهة المستخدم

### عرض معلومات الفلتر
```javascript
private getCurrentFilterInfo(): string {
  if (!this.selectedCategory) return '';

  const category = this.categories.find(c => c.id === this.selectedCategory);
  const categoryName = category ? category.name : this.selectedCategory;

  return `
    <div class="alert alert-info d-flex justify-content-between align-items-center mb-3">
      <div>
        <i class="bi bi-funnel-fill me-2"></i>
        <strong>Filtered by Category:</strong> ${categoryName}
        <small class="text-muted ms-2">(${this.offers.length} offers found)</small>
      </div>
      <button class="btn btn-sm btn-outline-secondary" onclick="window.mazayaWebPart.clearCategoryFilter()">
        <i class="bi bi-x-circle me-1"></i>Clear Filter
      </button>
    </div>
  `;
}
```

### مسح الفلتر
```javascript
public clearCategoryFilter(): void {
  this.selectedCategory = null;
  this.currentPage = 1;
  
  // إعادة تحميل الصفحة بدون categoryId في URL
  const url = new URL(window.location.href);
  url.searchParams.delete('categoryId');
  window.location.href = url.toString();
}
```

## التنقل من صفحة الفئات

### في FeaturedDealsWebPart
```javascript
// عند النقر على فئة
const detailsUrl = `/sites/intranet-qm/SitePages/Mazaya.aspx?categoryId=${categoryData.id}`;
window.location.href = detailsUrl;
```

## أمثلة على الاستخدام

### 1. رابط مباشر لفئة معينة
```html
<a href="/sites/intranet-qm/SitePages/Mazaya.aspx?categoryId=123">
  عروض فئة السيارات
</a>
```

### 2. التنقل من JavaScript
```javascript
function showCategoryOffers(categoryId) {
  const mazayaUrl = `/sites/intranet-qm/SitePages/Mazaya.aspx?categoryId=${categoryId}`;
  window.location.href = mazayaUrl;
}
```

### 3. فتح في نافذة جديدة
```javascript
function openCategoryOffers(categoryId) {
  const mazayaUrl = `/sites/intranet-qm/SitePages/Mazaya.aspx?categoryId=${categoryId}`;
  window.open(mazayaUrl, '_blank');
}
```

## بنية البيانات

### Offer Interface
```typescript
export interface Offer {
  id: string;
  title: string;
  bannerUrl: string;
  badgeTitle: string;
  discountType: string;
  amountCAP: number;
  amountPercentage: number;
  expiryDate: string;
  categoryId?: string; // ✅ إضافة categoryId
  supplier: { logo: string; name: string };
}
```

### Category Interface
```typescript
interface Category {
  id: string;
  name: string;
}
```

## سيناريوهات الاستخدام

### 1. المستخدم يدخل من صفحة الفئات
1. يدوس على فئة معينة
2. يتم التنقل لصفحة العروض مع categoryId في URL
3. تظهر العروض مفلترة بالفئة المحددة

### 2. المستخدم يدخل برابط مباشر
1. يفتح رابط يحتوي على categoryId
2. تظهر العروض مفلترة بالفئة المحددة
3. يمكنه مسح الفلتر للعودة لجميع العروض

### 3. المستخدم يريد مسح الفلتر
1. يدوس على زر "Clear Filter"
2. يتم إعادة تحميل الصفحة بدون categoryId
3. تظهر جميع العروض

## المميزات

### ✅ بساطة
- مفيش localStorage
- اعتماد على URL parameters فقط
- سهولة في المشاركة والتنقل

### ✅ وضوح
- الفلتر واضح في URL
- يمكن مشاركة الرابط مع الفلتر
- يمكن حفظ الرابط في المفضلة

### ✅ أداء
- مفيش تعقيدات في localStorage
- اعتماد على الـ API للفلترة
- فلترة محلية بسيطة إذا احتجنا

## الاختبار

### 1. اختبار الفلتر
```
/sites/intranet-qm/SitePages/Mazaya.aspx?categoryId=123
```

### 2. اختبار مسح الفلتر
- ادوس على "Clear Filter"
- تأكد من إزالة categoryId من URL

### 3. اختبار التنقل
- من صفحة الفئات لصفحة العروض
- تأكد من وجود categoryId في URL

## الخلاصة

النظام الآن:
- ✅ يقرأ categoryId من URL فقط
- ✅ يرسل categoryId للـ API
- ✅ يعرض معلومات الفلتر الحالي
- ✅ يسمح بمسح الفلتر
- ✅ مفيش localStorage خالص

بسيط وواضح ومباشر! 🚀
