import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient } from '@microsoft/sp-http';


export interface IInspireReflectWebPartProps {
  description: string;
}

export interface ISocialMediaItem {
  Title: string;
  Url: any;
  Icon: string;
}

export interface IQuoteItem {
  Title: string;
  Quote: string;
  QuoteAr?: string;
}

export default class InspireReflectWebPart extends BaseClientSideWebPart<IInspireReflectWebPartProps> {

  private _isArabic: boolean = false;
  private socialMediaListName: string = "SocialMediaLinks";
  private quoteListName: string = "QuoteSlider";

  public async render(): Promise<void> {
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;

    const quotes = await this.getQuotes();
    const socials = await this.getSocialLinks();

    const sectionTitle = this._isArabic ? 'إلهِم وتأمّل' : 'Inspire & Reflect';
    const socialTitle = this._isArabic ? 'وسائل التواصل الاجتماعي' : 'Social Media';
    const prevLabel = this._isArabic ? 'السابق' : 'Previous';
    const nextLabel = this._isArabic ? 'التالي' : 'Next';
    const dir = this._isArabic ? 'rtl' : 'ltr';

    this.domElement.innerHTML = `
      <section class="inspire-section f4-news mt-4 mt-lg-0 h-100" dir="${dir}">
        <div class="card section-card card-color h-100 p-3">
          <div class="container-fluid">
            <h2 class="fw-bold tittle head-color">${sectionTitle}</h2>

            <div class="vertical-carousel-container">
              <div class="vertical-carousel-inner">
                ${quotes.map((item: any, i: number) => `
                  <div class="vertical-carousel-item ${i === 0 ? 'active' : ''}">
                    <blockquote class="fst-italic text-color">
                      "${this._isArabic && item.QuoteAr ? item.QuoteAr || '' : item.Quote || ''}"
                    </blockquote>
                    <p class="fw-bold head-color">${this._isArabic ? item.Title_AR || '' : item.Title || ''}</p>
                  </div>
                `).join('')}
              </div>
              <div class="vertical-carousel-arrows">
                <button class="carousel-arrow-up" aria-label="${prevLabel}">
                  <i class="bi bi-arrow-up-circle"></i>
                </button>
                <button class="carousel-arrow-down" aria-label="${nextLabel}">
                  <i class="bi bi-arrow-down-circle"></i>
                </button>
              </div>
            </div>

            <h2 class="fw-bold tittle head-color">${socialTitle}</h2>
            <div class="d-flex justify-content-start gap-3 fs-4">
              ${socials.map(link => `
                <a href="${link.Url?.Url || '#'}" aria-label="${link.Title || ''}" class="text-color" target="_blank">
                  <i class="bi ${link.Icon || ''}"></i>
                </a>
              `).join('')}
            </div>

          </div>
        </div>
      </section>
    `;

    this._initializeCarousel();
  }

  private async getSocialLinks(): Promise<ISocialMediaItem[]> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('${this.socialMediaListName}')/items`;
    const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
    const json = await response.json();
    return json.value;
  }

  private async getQuotes(): Promise<IQuoteItem[]> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('${this.quoteListName}')/items`;
    const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
    const json = await response.json();
    return json.value;
  }

  private _initializeCarousel(): void {
    const upButton = this.domElement.querySelector('.carousel-arrow-up');
    const downButton = this.domElement.querySelector('.carousel-arrow-down');
    const items = this.domElement.querySelectorAll('.vertical-carousel-item');

    if (!items.length) return;

    let currentIndex = 0;

    const updateCarousel = (index: number) => {
      items.forEach((item, i) => {
        const translateY = (i - index) * 100;
        item.classList.toggle('active', i === index);
        (item as HTMLElement).style.transform = `translateY(${translateY}%)`;
      });
    };

    if (upButton) {
      upButton.addEventListener('click', () => {
        currentIndex = (currentIndex - 1 + items.length) % items.length;
        updateCarousel(currentIndex);
      });
    }

    if (downButton) {
      downButton.addEventListener('click', () => {
        currentIndex = (currentIndex + 1) % items.length;
        updateCarousel(currentIndex);
      });
    }

    updateCarousel(0);
  }
}
