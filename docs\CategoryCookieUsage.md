# استخدام الكوكيز للفئات والفئات الفرعية والموردين

## نظرة عامة
تم تطوير نظام شامل لتخزين واسترجاع بيانات الفئات والفئات الفرعية والموردين باستخدام الكوكيز، مما يتيح مشاركة هذه البيانات عبر صفحات مختلفة في الموقع.

## البيانات المخزنة في الكوكيز

### 1. بيانات الفئات (Categories)
```javascript
// مخزنة في: 'categoriesData'
[
  {
    id: "1",
    name: "Information Technology",
    icon: "https://example.com/icon.svg"
  },
  // ...المزيد من الفئات
]
```

### 2. بيانات الفئات الفرعية (SubCategories)
```javascript
// مخزنة في: 'subCategoriesData'
[
  {
    id: "1",
    name: "Software Development",
    categoryId: "1"
  },
  // ...المزيد من الفئات الفرعية
]
```

### 3. بيانات الموردين (Suppliers)
```javascript
// مخزنة في: 'suppliersData'
[
  {
    id: "1",
    name: "Tech Solutions Inc",
    subCategoryId: "1"
  },
  // ...المزيد من الموردين
]
```

### 4. الاختيارات الحالية
```javascript
// الفئة المحددة
'selectedCategoryId': "1"
'selectedCategoryName': "Information Technology"

// الفئة الفرعية المحددة
'selectedSubCategoryId': "1"
'selectedSubCategoryName': "Software Development"

// المورد المحدد
'selectedSupplierId': "1"
'selectedSupplierName': "Tech Solutions Inc"
```

## كيفية الاستخدام في صفحات أخرى

### 1. الحصول على جميع البيانات
```javascript
// في أي WebPart آخر
const featuredDealsWebPart = (window as any).featuredDealsWebPart;

if (featuredDealsWebPart) {
  // الحصول على جميع الفئات
  const categories = featuredDealsWebPart.getStoredCategories();
  
  // الحصول على جميع الفئات الفرعية
  const subCategories = featuredDealsWebPart.getStoredSubCategories();
  
  // الحصول على جميع الموردين
  const suppliers = featuredDealsWebPart.getStoredSuppliers();
}
```

### 2. الحصول على بيانات مفلترة
```javascript
// الحصول على الفئات الفرعية لفئة معينة
const subCategoriesForIT = featuredDealsWebPart.getSubCategoriesByCategory("1");

// الحصول على الموردين لفئة فرعية معينة
const suppliersForSoftware = featuredDealsWebPart.getSuppliersBySubCategory("1");
```

### 3. الحصول على الاختيارات الحالية
```javascript
const currentSelections = featuredDealsWebPart.getCurrentSelections();

console.log("الفئة المحددة:", currentSelections.category);
console.log("الفئة الفرعية المحددة:", currentSelections.subCategory);
console.log("المورد المحدد:", currentSelections.supplier);
```

### 4. تحديد اختيارات جديدة
```javascript
// تحديد فئة
featuredDealsWebPart.selectCategory("1", "Information Technology");

// تحديد فئة فرعية
featuredDealsWebPart.selectSubCategory("1", "Software Development");

// تحديد مورد
featuredDealsWebPart.selectSupplier("1", "Tech Solutions Inc");
```

## مثال عملي: WebPart للعروض المفلترة

```typescript
export default class FilteredOffersWebPart extends BaseClientSideWebPart<any> {
  
  public render(): void {
    const featuredDealsWebPart = (window as any).featuredDealsWebPart;
    
    if (!featuredDealsWebPart) {
      this.domElement.innerHTML = '<div>Loading...</div>';
      return;
    }

    // الحصول على الاختيارات الحالية
    const selections = featuredDealsWebPart.getCurrentSelections();
    
    // عرض العروض بناءً على الاختيارات
    this.domElement.innerHTML = `
      <div class="filtered-offers">
        <h3>العروض المفلترة</h3>
        
        ${selections.category ? `
          <div class="filter-info">
            <span class="badge bg-primary">${selections.category.name}</span>
            ${selections.subCategory ? `
              <span class="badge bg-secondary">${selections.subCategory.name}</span>
            ` : ''}
            ${selections.supplier ? `
              <span class="badge bg-success">${selections.supplier.name}</span>
            ` : ''}
          </div>
        ` : '<p>لم يتم تحديد أي فئة</p>'}
        
        <div class="offers-list">
          ${this.renderOffers(selections)}
        </div>
      </div>
    `;
  }

  private renderOffers(selections: any): string {
    // هنا يمكنك استدعاء API للحصول على العروض المفلترة
    // بناءً على الاختيارات الحالية
    
    if (!selections.category) {
      return '<p>يرجى تحديد فئة لعرض العروض</p>';
    }

    // مثال على العروض
    return `
      <div class="offer-card">
        <h4>عرض خاص - ${selections.category.name}</h4>
        <p>خصم 20% على جميع المنتجات</p>
      </div>
    `;
  }
}
```

## مثال: قائمة الفئات الفرعية الديناميكية

```typescript
export default class SubCategoryListWebPart extends BaseClientSideWebPart<any> {
  
  public render(): void {
    const featuredDealsWebPart = (window as any).featuredDealsWebPart;
    
    if (!featuredDealsWebPart) {
      this.domElement.innerHTML = '<div>Loading...</div>';
      return;
    }

    const selections = featuredDealsWebPart.getCurrentSelections();
    
    if (!selections.category) {
      this.domElement.innerHTML = '<p>يرجى تحديد فئة أولاً</p>';
      return;
    }

    // الحصول على الفئات الفرعية للفئة المحددة
    const subCategories = featuredDealsWebPart.getSubCategoriesByCategory(selections.category.id);
    
    this.domElement.innerHTML = `
      <div class="subcategory-list">
        <h3>الفئات الفرعية - ${selections.category.name}</h3>
        <div class="row">
          ${subCategories.map(subCat => `
            <div class="col-md-4 mb-3">
              <div class="card subcategory-card" data-id="${subCat.id}" data-name="${subCat.name}">
                <div class="card-body text-center">
                  <h5>${subCat.name}</h5>
                  <button class="btn btn-primary btn-sm">اختيار</button>
                </div>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;

    // إضافة event listeners
    this.attachEventListeners(featuredDealsWebPart);
  }

  private attachEventListeners(featuredDealsWebPart: any): void {
    const cards = this.domElement.querySelectorAll('.subcategory-card');
    
    cards.forEach(card => {
      card.addEventListener('click', () => {
        const id = card.getAttribute('data-id');
        const name = card.getAttribute('data-name');
        
        if (id && name) {
          featuredDealsWebPart.selectSubCategory(id, name);
          // إعادة تحديث العرض أو الانتقال لصفحة أخرى
          this.render();
        }
      });
    });
  }
}
```

## نصائح مهمة

### 1. التحقق من وجود البيانات
```javascript
const featuredDealsWebPart = (window as any).featuredDealsWebPart;

if (!featuredDealsWebPart) {
  console.log('FeaturedDealsWebPart غير متاح بعد');
  return;
}
```

### 2. مراقبة تغيير البيانات
```javascript
// يمكنك إضافة event listener لمراقبة تغيير الكوكيز
window.addEventListener('storage', (e) => {
  if (e.key && e.key.includes('selected')) {
    // إعادة تحديث العرض عند تغيير الاختيارات
    this.render();
  }
});
```

### 3. التعامل مع البيانات المفقودة
```javascript
const categories = featuredDealsWebPart.getStoredCategories();

if (!categories || categories.length === 0) {
  console.log('لا توجد فئات محفوظة');
  // يمكنك إعادة تحميل البيانات أو عرض رسالة
}
```

## APIs المستخدمة

- **Categories**: `https://mazaya-backend.qm.org.qa/api/Categories`
- **SubCategories**: `https://mazaya-backend.qm.org.qa/api/SubCategories`
- **Suppliers**: `https://mazaya-backend.qm.org.qa/api/Suppliers`

جميع الـ APIs تتطلب Bearer token للمصادقة.
