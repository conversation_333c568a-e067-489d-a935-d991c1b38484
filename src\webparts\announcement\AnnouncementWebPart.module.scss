@import '~@microsoft/sp-office-ui-fabric-core/dist/sass/SPFabricCore.scss';

.announcement {
  overflow: hidden;
  color: "[theme:bodyText, default: #323130]";
  color: var(--bodyText);

  &.teams {
    font-family: $ms-font-family-fallbacks;
  }

  .announcement-webpart {
    .card {
      border: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
    }

    .announcement-item {
      transition: all 0.3s ease;
      border-radius: 8px !important;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .announcement-icon {
        .badge {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            font-size: 1.2rem;
          }
        }
      }

      .announcement-title {
        font-size: 1.1rem;
        line-height: 1.4;
        margin-bottom: 0.5rem;
      }

      .announcement-description {
        font-size: 0.95rem;
        line-height: 1.5;
        color: #6c757d;
      }

      .announcement-meta {
        font-size: 0.85rem;

        .badge {
          font-size: 0.75rem;
          padding: 0.25rem 0.5rem;
        }
      }
    }

    // Priority colors
    .border-danger {
      border-color: #dc3545 !important;
    }

    .border-warning {
      border-color: #ffc107 !important;
    }

    .border-success {
      border-color: #198754 !important;
    }

    .border-secondary {
      border-color: #6c757d !important;
    }

    .bg-light {
      background-color: #f8f9fa !important;
    }
  }
}