import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';


export interface ISettingSiteWebPartProps {
  description: string;
}

export default class SettingSiteWebPart extends BaseClientSideWebPart<ISettingSiteWebPartProps> {
private _isArabic: boolean = false;

  private setPageDirection(): void {
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;

    const dir = this._isArabic ? 'rtl' : 'ltr';
    const lang = this._isArabic ? 'ar' : 'en';

    document.documentElement.setAttribute('dir', dir);
    document.documentElement.setAttribute('lang', lang);
  }
  public render(): void {
    this.domElement.innerHTML = `
      <section class="setting-section">
        <!-- Fixed Settings Button -->
        <div class="theme-toggle-btn">
          <button class="btn bg-main bg-main-hover text-white" aria-label="themeBtn" id="themeBtn">
            <span class="bi bi-gear-fill mx-1"></span>

          </button>
          <div class="dropdown-panel g-3" id="themeDropdown">
            <div class="py-3 px-4 panel-container">
              <div class="d-flex justify-content-between">
                <h2 class="head-color">Setting</h2>
                <!-- close btn -->
                <button type="button" aria-label="closeBtn" class="btn btn-outline-secondary btn-sm mb-3">
                  <i class="bi bi-x-lg"></i>
                </button>
              </div>
              <hr>
              <div class="theme py-2">
                <h3 class="head-color">Theme</h3>
                <!-- Theme Toggle Options -->
                <div class="theme-options d-flex gap-1 mt-2">
                  <div class="theme-choice d-flex align-items-center gap-1 selected" id="lightOption">
                    <i class="bi bi-sun"></i>
                    <span>Light</span>
                  </div>
                  <div class="theme-choice d-flex align-items-center gap-1" id="darkOption">
                    <i class="bi bi-moon"></i>
                    <span>Dark</span>
                  </div>
                </div>
              </div>
              <hr>
              <div class="colors py-2">
                <h3 class="head-color">Colors</h3>
                <div class="color-palette d-flex gap-2">
                  <div class="color-circle bg-pink" data-color="--pink"></div>
                  <div class="color-circle bg-orange" data-color="--orange"></div>
                  <div class="color-circle bg-blue" data-color="--blue"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="teams-section">
        <div class="teams-btn">
          <a href="https://teams.microsoft.com/l/chat/0/0?users=<EMAIL>"
            class="btn bg-main bg-main-hover text-white d-flex align-items-center gap-2" target="_blank">
            <i class="bi bi-microsoft-teams icon-md"></i>
            <span>
            Contact Us
            </span>
          </a>
        </div>
      </section>
    `;

    this.initializeComponents();
  }

  private initializeComponents(): void {
    this.initializeThemeToggle();
    this.setPageDirection();
    this.initializeThemeCustomization();
  }

  private initializeThemeToggle(): void {
    const themeBtn = document.getElementById('themeBtn') as HTMLButtonElement;
    const themeDropdown = document.getElementById('themeDropdown') as HTMLDivElement;

    if (themeBtn && themeDropdown) {
      themeBtn.addEventListener('click', (e: MouseEvent) => {
        e.stopPropagation();
        themeDropdown.classList.toggle('show');
      });

      document.addEventListener('click', (e: MouseEvent) => {
        const target = e.target as HTMLElement;
        if (!themeDropdown.contains(target) && !themeBtn.contains(target)) {
          themeDropdown.classList.remove('show');
        }
      });

      const themeCloseBtn = themeDropdown.querySelector('.btn-outline-secondary') as HTMLButtonElement;
      if (themeCloseBtn) {
        themeCloseBtn.addEventListener('click', () => {
          themeDropdown.classList.remove('show');
        });
      }

      const lightOption = document.getElementById('lightOption') as HTMLElement;
      const darkOption = document.getElementById('darkOption') as HTMLElement;

      const savedTheme: any = localStorage.getItem('theme') || 'light';
      this.applyTheme(savedTheme);

      lightOption.addEventListener('click', () => {
        this.applyTheme('light');
      });

      darkOption.addEventListener('click', () => {
        this.applyTheme('dark');
      });
    }
  }

  private applyTheme(theme: 'light' | 'dark'): void {
    const body = document.body;
    const lightOption = document.getElementById('lightOption');
    const darkOption = document.getElementById('darkOption');
  
    if (theme === 'dark') {
      body.classList.add('dark-theme');
      body.classList.remove('light-theme');
  
      darkOption?.classList.add('selected');
      lightOption?.classList.remove('selected');
    } else {
      body.classList.add('light-theme');
      body.classList.remove('dark-theme');
  
      lightOption?.classList.add('selected');
      darkOption?.classList.remove('selected');
    }
  
    localStorage.setItem('theme', theme);
  }
  


  private initializeThemeCustomization(): void {
    const colorCircles = document.querySelectorAll('.color-circle') as NodeListOf<HTMLElement>;

    const updateSelectedColor = (element: HTMLElement): void => {
      colorCircles.forEach(c => c.classList.remove('selected'));
      element.classList.add('selected');

      const selectedColor = getComputedStyle(document.documentElement)
        .getPropertyValue(element.dataset.color || '');
      document.documentElement.style.setProperty('--main-color', selectedColor);

      localStorage.setItem('selectedColorKey', element.dataset.color || '');
    };

    const savedColorKey = localStorage.getItem('selectedColorKey');
    if (savedColorKey) {
      const savedColorElement = Array.from(colorCircles).find(c => c.dataset.color === savedColorKey);
      if (savedColorElement) updateSelectedColor(savedColorElement);
    } else {
      const defaultColor = document.querySelector('.color-circle.bg-pink') as HTMLElement;
      if (defaultColor) updateSelectedColor(defaultColor);
    }

    colorCircles.forEach(circle => {
      circle.addEventListener('click', () => updateSelectedColor(circle));
    });
  }
}
