import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';

export interface IFAQItem {
  Id: number;
  Title: string;
  Question_AR?: string;
  Answer_EN?: string;
  Answer_AR?: string;
}

export interface IAllFaQsWebPartProps {
  description: string;
}

export default class AllFaQsWebPart extends BaseClientSideWebPart<IAllFaQsWebPartProps> {
  // private _faqItems: IFAQItem[] = [];
  private _filteredFaqItems: IFAQItem[] = [];
  private _isArabic: boolean = false;
  private _isLoading: boolean = false;
  private _searchQuery: string = '';
  private _currentPage: number = 1;
  private _itemsPerPage: number = 10;
  private _totalPages: number = 1;
  private _searchTimeout: any = null;

  public async onInit(): Promise<void> {
    await super.onInit();

    // تحديد اللغة بناءً على URL أو إعدادات المتصفح
    this._isArabic = this.context.pageContext.cultureInfo.currentCultureName.startsWith('ar');

    await this._loadFAQs();
  }

  public render(): void {
    const loadingText = this._isArabic ? 'جاري التحميل...' : 'Loading...';
    const noFAQsText = this._isArabic ? 'لا توجد أسئلة شائعة' : 'No FAQs found';

    this.domElement.innerHTML = `
      <div class="faq-section news-page">
    <div class="card section-card card-color pt-2 mt-3 bg-remover">
      <div class="container-fluid ">
      <h2 class="fw-bold tittle head-color ">${this._isArabic ? 'الأسئلة الشائعة' : 'FAQs'}</h2>
        ${this._renderSearchBox()}
        ${this._isLoading ? `
          <div class="loading-container">
            <div class="spinner-border" role="status"></div>
            <p class="mt-3">${loadingText}</p>
          </div>
        ` : this._renderFAQs(noFAQsText)}
      </div>
      </div>
      </div>
    `;

    // إضافة event listeners
    if (!this._isLoading) {
      this._attachEventListeners();
    }
  }

  private getListEndpoint(listTitle: string, selectFields?: string, expandFields?: string, filterQuery?: string, orderQuery?: string, topQuery?: string, forceMainSite?: boolean): string {
    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    const siteUrl = this.context.pageContext.site.absoluteUrl;
    const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    const baseUrl = forceMainSite
      ? `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
      : (isSubSite
        ? `${currentWebUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
        : `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`);

    const queryParams: string[] = [];

    if (selectFields) {
      queryParams.push(`$select=${selectFields}`);
    }

    if (expandFields) {
      queryParams.push(`$expand=${expandFields}`);
    }

    if (filterQuery) {
      queryParams.push(`$filter=${filterQuery}`);
    }


    if (orderQuery) {
      queryParams.push(orderQuery);
    }

    if (topQuery) {
      queryParams.push(topQuery);
    }

    return queryParams.length > 0
      ? `${baseUrl}?${queryParams.join('&')}`
      : baseUrl;
  }

  private async _loadFAQs(): Promise<void> {
    // تحميل البيانات الأساسية (بدون بحث)
    await this._performSearch();
  }



  private _calculatePagination(): void {
    this._totalPages = Math.ceil(this._filteredFaqItems.length / this._itemsPerPage);
    if (this._currentPage > this._totalPages) {
      this._currentPage = 1;
    }
  }

  private _renderSearchBox(): string {
    const searchPlaceholder = this._isArabic ? 'ابحث في الأسئلة الشائعة...' : 'Search FAQs...';
    const clearText = this._isArabic ? 'مسح' : 'Clear';

    return `
      <div class="">
  <div class="input-group">
  <span class="input-group-text">
    <i class="bi bi-search"></i>
    </span>
    <input
      type="text"
      class="form-control"
      placeholder="${searchPlaceholder}"
      value="${this._searchQuery}"
      id="faq-search-input"
    />
    <button
      class="btn btn-outline-secondary ${this._searchQuery ? 'd-block' : 'd-none'}"
      type="button"
      id="clear-search"
      title="${clearText}"
    >
      ✕
    </button>
  </div>
</div>

    `;
  }

  private _renderFAQs(noFAQsText: string): string {
    if (!this._filteredFaqItems || this._filteredFaqItems.length === 0) {
      return `
        <div class="no-faqs">
          <i>❓</i>
          <p>${noFAQsText}</p>
        </div>
      `;
    }

    const startIndex = (this._currentPage - 1) * this._itemsPerPage;
    const endIndex = startIndex + this._itemsPerPage;
    const currentPageItems = this._filteredFaqItems.slice(startIndex, endIndex);


    return `

      <div class="faq-accordion p-3">
        ${currentPageItems.map((faq, index) => this._renderFAQItem(faq, startIndex + index)).join('')}
      </div>

      ${this._renderPagination()}
    `;
  }

  private _renderFAQItem(faq: IFAQItem, index: number): string {
    const question = this._isArabic ? faq.Question_AR || faq.Title : faq.Title || '';
    const answer = this._isArabic ? faq.Answer_AR || faq.Answer_EN || '' : faq.Answer_EN || '';

    // إنشاء IDs فريدة لكل سؤال لتجنب تضارب Bootstrap
    const uniqueId = `faq-${faq.Id}-${index}`;
    const accordionId = `accordion-${uniqueId}`;
    const headingId = `heading-${uniqueId}`;
    const collapseId = `collapse-${uniqueId}`;

    return `
     <div class="accordion my-4" id="${accordionId}">
     <div class="accordion-item border-0 border-bottom">
               <h2 class="accordion-header card-color" id="${headingId}">
                 <button class="accordion-button fw-bold bg-transparent shadow-none head-color px-0 py-3 d-flex justify-content-between align-items-center collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#${collapseId}" aria-expanded="false" aria-controls="${collapseId}">
                   <span class="flex-grow-1 text-start">${question}</span>
                   <i class="bi bi-chevron-down ms-2 text-color"></i>
                 </button>
               </h2>
               <div id="${collapseId}" class="accordion-collapse collapse" aria-labelledby="${headingId}" data-bs-parent="#${accordionId}" style="">
                 <div class="accordion-body text-color px-0 pb-3">
                     ${answer}
                 </div>
               </div>
             </div>
           </div>
     `;
  }

  private _renderPagination(): string {
    if (this._totalPages <= 1) {
      return '';
    }

    const prevText = this._isArabic ? 'السابق' : 'Previous';
    const nextText = this._isArabic ? 'التالي' : 'Next';
    // const pageText = this._isArabic ? 'صفحة' : 'Page';
    const firstText = this._isArabic ? 'الأولى' : 'First';
    const lastText = this._isArabic ? 'الأخيرة' : 'Last';

    let paginationHTML = `
      <div class="pagination justify-content-center flex-wrap gap-1">
    `;

    // First page button (if not on first page and there are many pages)
    if (this._currentPage > 3 && this._totalPages > 7) {
      paginationHTML += `
        <button class="pagination-btn" data-page="1" title="${firstText}">
          1
        </button>
        <span class="pagination-dots">...</span>
      `;
    }

    // Previous button
    paginationHTML += `
      <button class="page-link rounded-pill px-3" id="prev-page" ${this._currentPage === 1 ? 'disabled' : ''} title="${prevText}">
      <i class="bi bi-arrow-left"></i>
        ${prevText}
      </button>
    `;

    // Page numbers
    const maxVisiblePages = 5;
    let startPage = Math.max(1, this._currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(this._totalPages, startPage + maxVisiblePages - 1);

    // Adjust start page if we're near the end
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      paginationHTML += `
      <button class="page-link ${i === this._currentPage ? 'active' : ''}" data-page="${i}">
      ${i}
    </button>

      `;
    }

    // Next button
    paginationHTML += `
      <button class="page-link rounded-pill px-3" id="next-page" ${this._currentPage === this._totalPages ? 'disabled' : ''} title="${nextText}">
        ${nextText}
        <i class="bi bi-arrow-right"></i>
      </button>
    `;

    // Last page button (if not on last page and there are many pages)
    if (this._currentPage < this._totalPages - 2 && this._totalPages > 7) {
      paginationHTML += `
        <span class="pagination-dots">...</span>
        <button class="pagination-btn" data-page="${this._totalPages}" title="${lastText}">
          ${this._totalPages}
        </button>
      `;
    }

    // Page info
    paginationHTML += `
        
      </div>
    `;

    return paginationHTML;
  }

  private _attachEventListeners(): void {

    // Search functionality with debouncing
    const searchInput = this.domElement.querySelector('#faq-search-input') as HTMLInputElement;
    const clearButton = this.domElement.querySelector('#clear-search') as HTMLButtonElement;

    if (searchInput) {
      searchInput.addEventListener('input', (event) => {
        const target = event.target as HTMLInputElement;
        this._searchQuery = target.value;

        // Clear previous timeout
        if (this._searchTimeout) {
          clearTimeout(this._searchTimeout);
        }

        // Set new timeout for debouncing (300ms delay)
        this._searchTimeout = setTimeout(async () => {
          await this._performSearch();
        }, 300);

        // Update clear button visibility immediately
        const clearBtn = this.domElement.querySelector('#clear-search');
        if (clearBtn) {
          if (this._searchQuery.trim()) {
            clearBtn.classList.add('show');
          } else {
            clearBtn.classList.remove('show');
          }
        }
      });

      // Handle Enter key for immediate search
      searchInput.addEventListener('keydown', async (event) => {
        if (event.key === 'Enter') {
          event.preventDefault();
          if (this._searchTimeout) {
            clearTimeout(this._searchTimeout);
          }
          await this._performSearch();
        }
      });
    }

    if (clearButton) {
      clearButton.addEventListener('click', async () => {
        this._searchQuery = '';
        if (searchInput) {
          searchInput.value = '';
        }
        clearButton.classList.remove('show');
        await this._performSearch();
      });
    }

    // FAQ accordion functionality
    const questions = this.domElement.querySelectorAll('.faq-question');
    questions.forEach((question) => {
      question.addEventListener('click', (event) => {
        event.preventDefault();

        const button = event.currentTarget as HTMLButtonElement;
        const index = button.getAttribute('data-index');

        if (index !== null) {
          this._toggleFAQ(parseInt(index));
        }
      });
    });

    // Pagination functionality
    const prevButton = this.domElement.querySelector('#prev-page');
    const nextButton = this.domElement.querySelector('#next-page');
    const pageButtons = this.domElement.querySelectorAll('[data-page]');
    

    if (prevButton) {
      prevButton.addEventListener('click', (event) => {
        event.preventDefault();
        if (this._currentPage > 1) {
          this._currentPage--;
          this.render();
        }
      });
    }

    if (nextButton) {
      nextButton.addEventListener('click', (event) => {
        event.preventDefault();
        if (this._currentPage < this._totalPages) {
          this._currentPage++;
          this.render();
        }
      });
    }

    pageButtons.forEach((button) => {
      button.addEventListener('click', (event) => {
        event.preventDefault();
        const target = event.target as HTMLButtonElement;
        const page = parseInt(target.getAttribute('data-page') || '1');


        if (page && page !== this._currentPage && page >= 1 && page <= this._totalPages) {
          this._currentPage = page;
          this.render();
        }
      });
    });
  }

  private async _performSearch(): Promise<void> {
    try {
      this._isLoading = true;
      this._currentPage = 1;
      this.render();


      const listName = 'FAQs';


      let filterQuery = '';
      if (this._searchQuery.trim()) {
        const searchTerm = this._searchQuery.replace(/'/g, "''");
        const searchFilter = `substringof('${searchTerm}',Title) or substringof('${searchTerm}',Question_AR)`;
        filterQuery = `${searchFilter}`;
      }




      // استخدام getListEndpoint للتعامل مع main site والـ subsites
      const apiUrl = this.getListEndpoint(listName, undefined, undefined, filterQuery);

      const response: SPHttpClientResponse = await this.context.spHttpClient.get(
        apiUrl,
        SPHttpClient.configurations.v1
      );

      if (response.ok) {
        const data = await response.json();
        this._filteredFaqItems = data.value || [];
        this._calculatePagination();
      } else {
        console.error('All FAQs - Search failed:', response.status, response.statusText);
        this._filteredFaqItems = [];
        this._calculatePagination();
      }
    } catch (error) {
      console.error('All FAQs - Search error:', error);
      this._filteredFaqItems = [];
      this._calculatePagination();
    } finally {
      this._isLoading = false;
      this.render();
    }
  }

  private _toggleFAQ(index: number): void {
    const questions = this.domElement.querySelectorAll('.faq-question');
    const answers = this.domElement.querySelectorAll('.faq-answer');
    const icons = this.domElement.querySelectorAll('.faq-icon');

    // إغلاق جميع الـ FAQs الأخرى
    questions.forEach((q, i) => {
      if (i !== index) {
        q.classList.remove('active');
        answers[i]?.classList.remove('show');
        icons[i]?.classList.remove('rotated');
      }
    });

    // تبديل الـ FAQ المحدد
    const currentQuestion = questions[index];
    const currentAnswer = answers[index];
    const currentIcon = icons[index];

    if (currentQuestion && currentAnswer && currentIcon) {
      const isActive = currentQuestion.classList.contains('active');

      if (isActive) {
        currentQuestion.classList.remove('active');
        currentAnswer.classList.remove('show');
        currentIcon.classList.remove('rotated');
      } else {
        currentQuestion.classList.add('active');
        currentAnswer.classList.add('show');
        currentIcon.classList.add('rotated');
      }
    }
  }
}
