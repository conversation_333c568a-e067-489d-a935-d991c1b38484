
import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';

export interface IFooterWebPartProps {
  description: string;
}

export default class FooterWebPart extends BaseClientSideWebPart<IFooterWebPartProps> {


  public render(): void {
    this.domElement.innerHTML = `<footer class="mt-4 container-fluid">
    <span>
      © <a class=" text-decoration-none footer-text" href="https://qm.org.qa/en/">Qatar Museums </a> <span id="currentYear">2025</span>
    </span>

    <span class="">
      <a href="#" class="text-decoration-none mx-4 text-white">
        Disclaimer </a>
    </span>

  </footer>`;
  }

}