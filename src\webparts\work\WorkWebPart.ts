import { Version } from '@microsoft/sp-core-library';
import {
  type IPropertyPaneConfiguration,
  PropertyPaneTextField
} from '@microsoft/sp-property-pane';
import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient } from '@microsoft/sp-http';
import { MSGraphClientV3 } from '@microsoft/sp-http';


import * as strings from 'BirthdayWebPartStrings';

export interface IWorkWebPartProps {
  description: string;
}

export interface IWorkUser {
  Id: number;
  Title: string;
  DisplayName?: string;
  Email?: string;
  HireDate?: string;
  Department?: string;
  JobTitle?: string;
  PictureUrl?: string;
}

export default class WorkWebPart extends BaseClientSideWebPart<IWorkWebPartProps> {
  private _workUsers: IWorkUser[] = [];
  private _isArabic: boolean = false;
  private _isLoading: boolean = false;

  protected async onInit(): Promise<void> {
    await super.onInit();
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;
    await this._loadTodaysWorkAnniversaries();
  }
  private async _getCurrentUserId(): Promise<number | null> {
    try {
      const response = await this.context.spHttpClient.get(
        `${this.context.pageContext.web.absoluteUrl}/_api/web/currentuser?$select=Id`,
        SPHttpClient.configurations.v1
      );

      const data = await response.json();
      return data.Id;
    } catch (error) {
      console.error('Error fetching current user ID:', error);
      return null;
    }
  }


  private async _loadTodaysWorkAnniversaries(): Promise<void> {
    try {
      this._isLoading = true;
      this.render();

      const today = new Date();
      const todayMonth = today.getMonth() + 1;
      const todayDay = today.getDate();

      const currentUser = this.context.pageContext.user;
      const isAnniversary = await this._checkCurrentUserWorkAnniversary(currentUser, todayMonth, todayDay);
      const userId = await this._getCurrentUserId();
      this._workUsers = isAnniversary
        ? [{
          Id: userId || 0,
          Title: currentUser.displayName,
          DisplayName: currentUser.displayName,
          Email: currentUser.email
        }]
        : [];
    } catch (error) {
      console.error('Work Anniversary - Error:', error);
    } finally {
      this._isLoading = false;
      this.render();
    }
  }

  private async _checkCurrentUserWorkAnniversary(currentUser: any, todayMonth: number, todayDay: number): Promise<boolean> {
    try {
      const graphClient = await this.context.msGraphClientFactory.getClient('3') as MSGraphClientV3;
      const response = await graphClient.api('/me?$select=hireDate').get();

      if (!response.hireDate) return false;

      const hireDate = new Date(response.hireDate);
      return (hireDate.getMonth() + 1 === todayMonth && hireDate.getDate() === todayDay);
    } catch (err) {
      console.error('Error fetching hireDate:', err);
      return false;
    }
  }




  public render(): void {
    const loadingText = this._isArabic ? 'جاري التحميل...' : 'Loading...';

    if (!this._isLoading && (!this._workUsers || this._workUsers.length === 0)) {
      this.domElement.innerHTML = ``;
      return;
    }

    this.domElement.innerHTML = `
      <div class="work-anniversary-section">
        ${this._isLoading ? `
          <div class="loading-container text-center py-5">
            <div class="spinner-border" role="status"></div>
            <p class="mt-3">${loadingText}</p>
          </div>
        ` : this._renderWorkAnniversaryCelebration()}
      </div>
    `;

    if (!this._isLoading && this._workUsers && this._workUsers.length > 0) {
      // Auto-start the fireworks
      this._attachEventListeners();
    }
  }

  private _renderWorkAnniversaryCelebration(): string {
    if (!this._workUsers || this._workUsers.length === 0) {
      return '';
    }

    const user = this._workUsers[0]; // نأخذ أول مستخدم للاحتفال

    return `
      <a id="show-fireworks" class="btn">
        <i class="bi bi-balloon-fill"></i>
          <br>
          ${this._isArabic ? 'ابدا' : 'Start'}
      </a>
      <!-- fireworks overlay -->
      <div id="fireworks-overlay">
        <canvas id="fireworks-canvas"></canvas>
        <div class="fw-controls">
        </div>

        <h1>Happy Work Anniversary! <br>
        <span class="text-center d-block" style="color: #ee3c8a;"> ${user.DisplayName || user.Title} <i class="bi bi-balloon-fill"></i></span>
        </h1>
        <button id="close-fireworks"><i class="bi bi-x-circle"></i></button>
      </div>

      <style>
        /* full-screen semi-transparent overlay */
        #fireworks-overlay {
          position: fixed;
          top: 0; left: 0;
          width: 100%; height: 100%;
          background: rgba(0,0,0,0.6);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 9999;
        }
        /* the actual canvas */
        #fireworks-canvas {
          position: absolute;
          top: 0; left: 0;
          width: 100%; height: 100%;
        }
        /* control buttons container */
        .fw-controls {
          position: absolute;
          top: 1rem; right: 1rem;
          display: flex;
          gap: .5rem;
        }
        #anniv-message {
          position: absolute;
          top: 1rem;
          left: 50%;
          transform: translateX(-50%);
          color: #fff;
          font-size: 2rem;
          z-index: 10000;
        }

        /* ==== */
        h1 {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: #fff;
          font-family: "Source Sans Pro";
          font-size: 5em;
          font-weight: 900;
          -webkit-user-select: none;
          user-select: none;
          z-index: 1040;
          text-align: center;
        }
        #close-fireworks {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 8px 16px;
            background-color: #00000083;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            z-index: 1040;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }
        #close-fireworks:hover {
            background-color: var(--main-color);
            transform: scale(1.05);
        }
        #close-fireworks:active {
            background-color: #00000054;
            transform: scale(0.95);
        }
        #show-fireworks{
          position: fixed;
          top: 60%;
          right: 0;
          background-color: var(--main-color);
          z-index: 1040;
          cursor: pointer;
          color: white;
          box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        #show-fireworks:hover{
          background-color: var(--card-color, #f8f9fa);
          color: var(--head-color);
        }
        /* ==== */
      </style>
    `;
  }

  private _attachEventListeners(): void {
    // Load canvas-confetti library
    if (!(window as any).confetti) {
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/canvas-confetti@1.5.1/dist/confetti.browser.min.js';
      document.head.appendChild(script);
      script.onload = () => {
        this._initializeFireworks();
      };
    } else {
      this._initializeFireworks();
    }
  }

  private _initializeFireworks(): void {
    // grab elements
    const overlay = this.domElement.querySelector('#fireworks-overlay') as HTMLElement;
    const canvas = this.domElement.querySelector('#fireworks-canvas') as HTMLCanvasElement;
    const showBtn = this.domElement.querySelector('#show-fireworks') as HTMLButtonElement;
    const closeBtn = this.domElement.querySelector('#close-fireworks') as HTMLButtonElement;

    if (!overlay || !canvas || !showBtn || !closeBtn) return;

    let fwInterval: any;

    // ensure Start button is hidden when overlay is active
    function toggleShowBtn(hide: boolean) {
      showBtn.style.display = hide ? 'none' : 'block';
    }

    function disableBodyScroll() {
      document.body.style.overflow = 'hidden';
    }
    function enableBodyScroll() {
      document.body.style.overflow = '';
    }

    // resize canvas to fill screen
    function resizeCanvas() {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    }
    window.addEventListener('resize', resizeCanvas);
    resizeCanvas();

    // create a confetti instance bound to our canvas
    const myConfetti = (window as any).confetti.create(canvas, { resize: true });

    function launchFireworks() {
      // clear any existing interval
      clearInterval(fwInterval);
      // continuously fire bursts until stopped
      fwInterval = setInterval(() => {
        myConfetti({
          particleCount: 50,
          spread: 200,
          startVelocity: 30,
          origin: { x: Math.random(), y: Math.random() * 0.6 }
        });
      }, 400);
    }

    // auto-start
    setTimeout(() => {
      toggleShowBtn(true);
      launchFireworks();
      disableBodyScroll();
    }, 100);

    // close overlay
    closeBtn.addEventListener('click', () => {
      toggleShowBtn(false);
      clearInterval(fwInterval);
      enableBodyScroll();
      overlay.style.display = 'none';
    });

    // if you want to re-show it after closing
    showBtn.addEventListener('click', () => {
      overlay.style.display = 'flex';
      launchFireworks();
      toggleShowBtn(true);
      disableBodyScroll();
    });
  }





  protected get dataVersion(): Version {
    return Version.parse('1.0');
  }

  protected getPropertyPaneConfiguration(): IPropertyPaneConfiguration {
    return {
      pages: [
        {
          header: {
            description: strings.PropertyPaneDescription
          },
          groups: [
            {
              groupName: strings.BasicGroupName,
              groupFields: [
                PropertyPaneTextField('description', {
                  label: strings.DescriptionFieldLabel
                })
              ]
            }
          ]
        }
      ]
    };
  }
}
