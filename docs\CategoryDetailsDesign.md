# تصميم صفحة تفاصيل الفئات - الديزاين الجديد

## نظرة عامة
تم تطوير تصميم جديد لصفحة تفاصيل الفئات يعرض الفئات الفرعية بشكل جذاب ومنظم.

## مكونات التصميم

### 1. رأس الفئة (Category Header)
```html
<div class="category-header">
  <img src="category-icon.png" class="category-icon" />
  <div>
    <h3 class="category-name">اسم الفئة</h3>
    <p class="category-description">وصف الفئة</p>
    <small>ID: 123</small>
  </div>
</div>
```

### 2. بطاقة الفئات الفرعية
```html
<div class="card card-color mb-4">
  <div class="card-body">
    <!-- العنوان وأزرار العرض -->
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h4>الفئات الفرعية</h4>
      <div class="view-btns">
        <span class="text-color">عرض</span>
        <button class="btn btn-outline-secondary btn-sm active" data-view="list">
          <i class="bi bi-list"></i>
        </button>
        <button class="btn btn-outline-secondary btn-sm" data-view="grid">
          <i class="bi bi-grid-3x3-gap"></i>
        </button>
      </div>
    </div>
    
    <hr>
    
    <!-- قائمة الفئات الفرعية -->
    <div class="row g-4" id="subCategoriesContainer">
      <!-- بطاقات الفئات الفرعية -->
    </div>
  </div>
</div>
```

### 3. بطاقة الفئة الفرعية
```html
<div class="col-md-6 col-lg-4">
  <a href="#" class="card vacancy-card h-100 text-decoration-none">
    <div class="border-accent"></div>
    <div class="card-body">
      <div class="job-info">
        <h4 class="job-title head-color">اسم الفئة الفرعية</h4>
        <p class="text-muted small">وصف الفئة الفرعية</p>
      </div>
      <div class="d-flex justify-content-end">
        <i class="bi bi-arrow-right-circle-fill text-color"></i>
      </div>
    </div>
  </a>
</div>
```

## الوظائف المتاحة

### 1. تبديل العرض (List/Grid)
```javascript
// تبديل بين عرض القائمة والشبكة
listViewBtn.addEventListener('click', () => {
  container.className = 'row g-2'; // مسافات أصغر للقائمة
});

gridViewBtn.addEventListener('click', () => {
  container.className = 'row g-4'; // مسافات أكبر للشبكة
});
```

### 2. اختيار الفئة الفرعية
```javascript
public selectSubCategory(subCategoryId: string, subCategoryName: string): void {
  // حفظ الفئة الفرعية المختارة
  localStorage.setItem('selectedSubCategory', JSON.stringify({
    id: subCategoryId,
    name: subCategoryName,
    parentCategoryId: this.getCategoryIdFromUrl()
  }));
  
  // عرض رسالة تأكيد
  alert(`تم اختيار الفئة الفرعية: ${subCategoryName}`);
  
  // التنقل لصفحة الموردين (اختياري)
  // const suppliersUrl = `/sites/intranet-qm/SitePages/Suppliers.aspx?subCategoryId=${subCategoryId}`;
  // window.location.href = suppliersUrl;
}
```

## الأنماط المستخدمة (CSS)

### 1. بطاقة الفئة الفرعية
```css
.vacancy-card {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.vacancy-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
```

### 2. الحد الملون
```css
.border-accent {
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(45deg, #007bff, #0056b3);
}
```

### 3. أزرار العرض
```css
.view-btns .btn.active {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}
```

## كيفية الاستخدام

### 1. عرض الفئات الفرعية
```javascript
// في صفحة التفاصيل
const categoryData = this.getCompleteCategoryDataById(categoryId);

if (categoryData && categoryData.subCategories) {
  // عرض الفئات الفرعية
  const subCategoriesHtml = this.renderSubCategories(categoryData);
}
```

### 2. التفاعل مع البطاقات
```javascript
// عند النقر على بطاقة فئة فرعية
onclick="window.categoryDetailsWebPart.selectSubCategory('${subCategory.id}', '${subCategory.name}')"
```

### 3. تبديل العرض
```javascript
// إضافة event listeners للأزرار
this.attachViewToggleEvents();
```

## البيانات المطلوبة

### بنية الفئة الكاملة
```javascript
{
  "id": "123",
  "name": "Automobile",
  "icon": "https://example.com/icon.png",
  "description": "وصف الفئة",
  "subCategories": [
    {
      "id": "sub1",
      "name": "Buying Cars",
      "description": "شراء السيارات"
    },
    {
      "id": "sub2", 
      "name": "Renting Cars",
      "description": "تأجير السيارات"
    },
    {
      "id": "sub3",
      "name": "Car Service", 
      "description": "خدمة السيارات"
    }
  ]
}
```

## حالات خاصة

### 1. عدم وجود فئات فرعية
```html
<div class="col-12">
  <div class="no-data">
    <p>لا توجد فئات فرعية لهذه الفئة</p>
  </div>
</div>
```

### 2. عدم وجود فئة محددة
```html
<div class="no-data">
  <h3>لا توجد فئة محددة</h3>
  <p>يرجى اختيار فئة من الصفحة الرئيسية أو تمرير ID صحيح في URL</p>
  <button onclick="showAvailableIds()">عرض جميع الـ IDs المتاحة</button>
</div>
```

## التنقل والروابط

### 1. من الصفحة الرئيسية
```javascript
// عند النقر على فئة
const detailsUrl = `/sites/intranet-qm/SitePages/CategoryDetails.aspx?categoryId=${categoryId}`;
window.location.href = detailsUrl;
```

### 2. إلى صفحة الموردين
```javascript
// عند النقر على فئة فرعية
const suppliersUrl = `/sites/intranet-qm/SitePages/Suppliers.aspx?subCategoryId=${subCategoryId}`;
window.location.href = suppliersUrl;
```

## الميزات

### ✅ تصميم جذاب ومنظم
- بطاقات أنيقة مع تأثيرات hover
- حد ملون مميز لكل بطاقة
- تخطيط متجاوب

### ✅ تبديل العرض
- عرض قائمة أو شبكة
- أزرار تفاعلية للتبديل
- مسافات متغيرة حسب نوع العرض

### ✅ تفاعل سهل
- نقرة واحدة لاختيار الفئة الفرعية
- رسائل تأكيد واضحة
- تخزين الاختيارات في localStorage

### ✅ معالجة الأخطاء
- رسائل واضحة للحالات الخاصة
- إرشادات للمستخدم
- عرض البدائل المتاحة

الآن التصميم جاهز ومطابق للشكل المطلوب! 🎨
