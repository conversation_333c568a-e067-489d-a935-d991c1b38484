import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient } from '@microsoft/sp-http';


export interface IHeaderWebPartProps {
  description: string;
}

interface AnnouncementItem {
  Id: number;
  Title: string;
  Title_AR: string;
  Description_EN: string;
  Description_AR: string;
  Begins: string;
  Expires: string;
  Active: boolean;
  Category: {
    Id: number;
    Title: string;
    Title_AR: string;
    Icon: string;
  };
  Link_EN: string;
  Link_AR: string;
}

export default class HeaderWebPart extends BaseClientSideWebPart<IHeaderWebPartProps> {
  private _isArabic: boolean = false;
  private announcements: AnnouncementItem[] = [];
  private categories: any[] = [];
  private unreadAnnouncementsCount: number = 0;

  render(): void {

    this._isArabic = window.location.pathname.toLowerCase().includes('/ar/');
    const placeholderText = this._isArabic ? 'ابحث هنا...' : 'Search...';
    const langText = this._isArabic ? 'English' : 'العربية';

    // إذا لم يتم تحميل الإعلانات بعد، عرض loading
    // const announcementsCount = this.announcements ? this.announcements.length : 0;



    this.domElement.innerHTML = `
      <style>
        .search-result-item a:hover {
          background-color: #f8f9fa;
          transition: 0.2s;
        }
        .hover-shadow:hover {
          box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .d-none {
          display: none !important;
        }
      </style>

      <header class="py-2 border-bottom">
        <div class="container-fluid">
          <div class="d-flex align-items-center justify-content-between flex-wrap">

            <!-- Logo -->
            <div class="logo-container d-flex align-items-center">
              <a href="/sites/intranet-qm" class="d-flex align-items-center text-decoration-none">
                <img class="logo-b" src="${require('./assets/logo.svg')}" alt="Logo" height="50">
                <img class="logo-w" src="${require('./assets/logo-w.svg')}" alt="Logo" height="50">
              </a>
            </div>

            <!-- Navbar -->
            <nav class="navbar navbar-expand-lg">
              <div class="container-fluid">
                <button class="navbar-toggler d-lg-none" aria-label="menuToggle" id="menuToggle" type="button">
                  <i class="bi bi-list fs-2"></i>
                </button>
                <div class="responsive-menu" id="mainMenu">
                  <div class="d-flex justify-content-end d-lg-none">
                    <button type="button" aria-label="closeMenuBtn" id="closeMenuBtn" class="btn btn-outline-secondary btn-sm mb-3">
                      <i class="bi bi-x-lg"></i>
                    </button>
                  </div>
                  <ul class="navbar-nav me-auto mb-2 mb-lg-0"></ul>
                </div>
              </div>
            </nav>

            <!-- Right Side -->
            <div class="d-flex align-items-center wrap-checker">
              <!-- Search -->
              <form class="d-flex me-3 position-relative d-none" id="searchForm">
                <input class="form-control" type="search" id="searchInput" placeholder="${placeholderText}" aria-label="Search">
                <button class="btn position-absolute end-0 top-0 bottom-0 btn-main" type="submit">
                  <i class="bi bi-search icon-white"></i>
                </button>
              </form>

              <!-- Notifications -->
             <div class="dropdown position-relative me-3 notifi">
            <a href="#" class="text-decoration-none position-relative" data-bs-toggle="dropdown" aria-expanded="false">
              <!-- Wrapper div for the icon with animation -->
              <div class="icon-wrapper d-inline-block ${this.hasNewAnnouncements() ? 'animate__animated animate__pulse animate__infinite' : ''}">
                <i class="bi bi-megaphone-fill btn-main-link icon-md"></i>
              </div>

              <!-- Badge without animation -->
              <span class="position-absolute top-0 start-100 translate-top badge rounded-pill bg-danger">
                ${this.unreadAnnouncementsCount}
              </span>
            </a>
            <div class="dropdown-menu dropdown-menu-end p-3 shadow" style="min-width: 320px; max-width: 360px;">
              ${this.getAnnouncementsHtml()}

              ${this.announcements.length > 0 ? '<hr class="my-2">' : ''}

              <div class="text-end">
                <a href="${this._isArabic ? '/sites/intranet-qm/SitePages/ar/Announcement.aspx' : '/sites/intranet-qm/SitePages/Announcement.aspx'}" class="fw-bold text-pink text-decoration-none btn-main-link">${this._isArabic ? 'عرض الكل' : 'View All'}</a>
              </div>
            </div>
          </div>
              <!-- Language Switch -->
              <div class="lang-toggle">
                <a href="#" id="langToggleBtn" class="btn btn-sm btn-main-link d-flex">
                  ${langText} 
                </a>
              </div>
            </div>

          </div>
        </div>
      </header>

      <style>
      .animate__pulse {
        animation-duration: 1.5s;
      }
      .icon-wrapper {
        transition: transform 0.2s;
      }
      .icon-wrapper:active {
        transform: scale(0.9);
      }
      </style>
    `;
    this.updateNotificationBadge();
    const iconWrapper: any = this.domElement.querySelector('.icon-wrapper');

    iconWrapper?.classList.remove('animate__headShake');
    void iconWrapper?.offsetWidth;
    iconWrapper?.classList.add('animate__headShake');

    iconWrapper?.addEventListener('click', () => {
      iconWrapper.classList.remove('animate__infinite', 'animate__headShake');
    });
    const menuToggle = this.domElement.querySelector('#menuToggle');
    const mainMenu = this.domElement.querySelector('#mainMenu');
    const closeBtn = this.domElement.querySelector('#closeMenuBtn');

    menuToggle?.addEventListener('click', (e) => {

      mainMenu?.classList.toggle('active');
    });

    closeBtn?.addEventListener('click', (e) => {

      mainMenu?.classList.remove('active');
    });

    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;

      if (!mainMenu?.contains(target) && !menuToggle?.contains(target)) {
        mainMenu?.classList.remove('active');
      }
    });
    const langBtn = this.domElement.querySelector('#langToggleBtn');
    langBtn?.addEventListener('click', (e) => {
      e.preventDefault();
      const path = window.location.pathname;
      const isArabic = path.toLowerCase().includes('/sitepages/ar/');
      const newPath = isArabic
        ? path.replace(/\/sitepages\/ar\//i, '/SitePages/')
        : path.replace(/\/sitepages\//i, '/SitePages/ar/');
      const newUrl = `${window.location.origin}${newPath}${window.location.search}${window.location.hash}`;
      window.location.href = newUrl;
    });

    this.loadMenuFromList();
    this.attachAnnouncementClickHandlers();

  }
  private hasNewAnnouncements(): boolean {
    // const now = new Date();
    const readIds = this.getReadAnnouncements();

    return this.announcements.some(ann => {
      const isUnread = !readIds.includes(ann.Id);
      const isRecentlyNew = this.isNewAnnouncement(ann.Begins);
      return isUnread && isRecentlyNew;
    });
  }
  private isNewAnnouncement(beginsDate: string): boolean {
    const now = new Date();
    const created = new Date(beginsDate);
    const diffHours = (now.getTime() - created.getTime()) / (1000 * 60 * 60);
    return diffHours <= 24;
  }

  private getListEndpoint(listTitle: string, selectFields?: string, expandFields?: string, filterQuery?: string, orderQuery?: string, topQuery?: string, forceMainSite?: boolean): string {
    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    const siteUrl = this.context.pageContext.site.absoluteUrl;
    const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    const baseUrl = forceMainSite
      ? `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
      : (isSubSite
        ? `${currentWebUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
        : `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`);

    const queryParams: string[] = [];

    if (selectFields) {
      queryParams.push(`$select=${selectFields}`);
    }

    if (expandFields) {
      queryParams.push(`$expand=${expandFields}`);
    }

    if (filterQuery) {
      queryParams.push(filterQuery);
    }

    if (orderQuery) {
      queryParams.push(orderQuery);
    }

    if (topQuery) {
      queryParams.push(topQuery);
    }

    return queryParams.length > 0
      ? `${baseUrl}?${queryParams.join('&')}`
      : baseUrl;
  }


  private getMenuEndpoint(listTitle: string = 'MainMenu'): string {
    return this.getListEndpoint(listTitle);
  }

  async loadMenuFromList(): Promise<void> {
    const endpoint = this.getMenuEndpoint();
    const response = await this.context.spHttpClient.get(endpoint, SPHttpClient.configurations.v1);
    const data = await response.json();

    const items = data.value || [];
    if (!items.length) return;

    items.sort((a: any, b: any) => (a.order0 ?? 0) - (b.order0 ?? 0));

    const parents = items.filter((item: any) => !item.ParentId);
    const children = items.filter((item: any) => item.ParentId);

    const childrenMap = children.reduce((acc: any, child: any) => {
      if (!acc[child.ParentId]) acc[child.ParentId] = [];
      acc[child.ParentId].push(child);
      return acc;
    }, {});



    const menuContainer = this.domElement.querySelector('#mainMenu ul');
    if (!menuContainer) return;




    parents.forEach((parent: any) => {
      const parentTitle = this._isArabic ? parent.Title_AR || '' : parent.Title || '';
      const parentChildren = childrenMap[parent.Id];
      const hasGroupInChildren = parentChildren?.some((child: any) => {
        const group = this._isArabic ? child.GroupName_AR : child.GroupName;
        return group && group.trim() !== '';
      });
      const li = document.createElement('li');
      li.className = parentChildren ? 'nav-item dropdown' : 'nav-item';

      li.innerHTML = `
        <a class="nav-link ${parentChildren ? 'dropdown-toggle' : ''}" href="${parentChildren ? '#' : (this._isArabic ? parent.LinkUrl_AR?.Url : parent.LinkUrl?.Url) || '#'}" role="button">
          ${parentTitle}
        </a>
        ${parentChildren ? `<ul class="dropdown-menu mega-menu ${hasGroupInChildren ? '' : 'small-menu'}"><div class="container-fluid"><div class="row"></div></div></ul>` : ''}
      `;

      if (parentChildren) {
        const dropdown = li.querySelector('.dropdown-menu')!;
        const row = dropdown.querySelector('.row')!;

        const groupedChildren = parentChildren.reduce((acc: any, child: any) => {
          const group = this._isArabic ? child.GroupName_AR || '' : child.GroupName || '';
          if (!acc[group]) acc[group] = [];
          acc[group].push(child);
          return acc;
        }, {});

        Object.keys(groupedChildren).forEach(group => {
          const col = document.createElement('div');
          const hasGroup = group.trim() !== '';
          col.className = hasGroup ? 'col-md-2' : 'col';
          col.innerHTML = `<h6 class="text-uppercase fw-bold mb-3">${group || ''}</h6><ul class="list-unstyled"></ul>`;

          const ul = col.querySelector('ul')!;
          groupedChildren[group].forEach((child: any) => {
            const title = this._isArabic ? child.Title_AR || '' : child.Title || '';
            const link = this._isArabic ? child.LinkUrl_AR?.Url || '' : child.LinkUrl?.Url || '';
            ul.innerHTML += `<li><a href="${link}" class="dropdown-item">${title || ''}</a></li>`;
          });

          row.appendChild(col);
        });
      }

      menuContainer.appendChild(li);
    });

    this._toggleDropdownManually();
  }

  async onInit(): Promise<void> {
    await super.onInit();
    this._loadAnimateCSS();
    await Promise.all([
      this.fetchCategories(),
      this.fetchLatestAnnouncements()
    ]);
  }

  private async fetchCategories(): Promise<void> {
    try {
      const selectFields = 'Id,Title,Title_AR,Icon,AttachmentFiles/FileName,AttachmentFiles/ServerRelativeUrl';
      const expandFields = 'AttachmentFiles';

      // الفئات دائماً من الموقع الرئيسي
      const url = this.getListEndpoint('Announcement%20Categories', selectFields, expandFields, undefined, undefined, undefined, true);


      const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
      if (!response.ok) {
        console.warn('Announcement Categories list not found in main site');
        this.categories = [];
        return;
      }

      const data = await response.json();
      this.categories = data.value || [];
    } catch (error) {
      console.error('Error fetching categories from main site:', error);
      this.categories = [];
    }
  }


  private async fetchLatestAnnouncements(): Promise<void> {
    try {
      const currentDate = new Date().toISOString();
      const selectFields = '*,Category/Id,Category/Title,Category/Title_AR';
      const expandFields = 'Category';
      const filterQuery = `$filter=(Active eq 1) and (Expires ge datetime'${currentDate}')`;
      const orderQuery = `$orderby=Begins desc`;
      const topQuery = `$top=5`;

      // الإعلانات دائماً من الموقع الرئيسي
      const url = this.getListEndpoint('Announcement', selectFields, expandFields, filterQuery, orderQuery, topQuery, true);


      const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
      if (!response.ok) {
        console.warn('Announcement list not found in main site');
        this.announcements = [];
        return;
      }

      const data = await response.json();
      this.announcements = data.value || [];

      this.calculateUnreadCount();
      this.render();
      this.attachAnnouncementClickHandlers();


    } catch (error) {
      console.error('Error fetching announcements from main site:', error);
      this.announcements = [];
      this.updateNotificationBadge();

    }
  }

  private calculateUnreadCount(): void {
    const readIds = this.getReadAnnouncements();
    this.unreadAnnouncementsCount = this.announcements.filter(ann => !readIds.includes(ann.Id)).length;
  }

  private getReadAnnouncements(): number[] {
    const stored = localStorage.getItem('readAnnouncements');
    return stored ? JSON.parse(stored) : [];
  }



  private updateNotificationBadge(): void {
    // تحديث العداد
    const badge = this.domElement.querySelector('.badge.bg-danger');
    if (badge) {
      badge.textContent = this.unreadAnnouncementsCount.toString();
    }

    // التحكم في أنيميشن الأيقونة الرئيسية
    const iconWrapper = this.domElement.querySelector('.icon-wrapper');
    if (this.hasNewAnnouncements()) {
      iconWrapper?.classList.add('animate__animated', 'animate__pulse', 'animate__infinite');
    } else {
      iconWrapper?.classList.remove('animate__animated', 'animate__pulse', 'animate__infinite');
    }
  }


  _toggleDropdownManually(): void {
    const toggles = this.domElement.querySelectorAll('.dropdown-toggle');

    toggles.forEach(toggle => {
      toggle.addEventListener('click', (e) => {
        e.preventDefault();

        // Close others
        this.domElement.querySelectorAll('.dropdown-menu.show').forEach(menu => {
          if (menu !== (toggle as HTMLElement).nextElementSibling) {
            menu.classList.remove('show');
          }
        });

        // Toggle current
        const dropdownMenu = (toggle as HTMLElement).nextElementSibling;
        dropdownMenu?.classList.toggle('show');
      });
    });

    // Click outside to close
    document.addEventListener('click', (e: MouseEvent) => {
      if (!this.domElement.contains(e.target as Node)) {
        this.domElement.querySelectorAll('.dropdown-menu.show').forEach(menu => {
          menu.classList.remove('show');
        });
      }
    });
  }

  private getAnnouncementsHtml(): string {
    const translate = (en: string, ar: string) => (this._isArabic ? ar : en);
    const now = new Date();
    const readAnnouncements = this.getReadAnnouncements();

    const isNew = (dateStr: string): boolean => {
      const created = new Date(dateStr);
      const diffHours = (now.getTime() - created.getTime()) / (1000 * 60 * 60);
      return diffHours <= 24;
    };

    if (!this.announcements || this.announcements.length === 0) {
      return `
      <div class="text-center py-3">
        <i class="bi bi-info-circle icon-md text-color"></i>
        <p class="text-color mb-0 mt-2">${translate('No announcements', 'لا توجد إعلانات')}</p>
      </div>
    `;
    }

    return this.announcements.map((announcement: any) => {
      const title = this._isArabic ? (announcement.Title_AR || announcement.Title || '') : (announcement.Title || '');
      const description = this._isArabic ? (announcement.Description_AR || announcement.Description_EN || '') : (announcement.Description_EN || '');
      const link = this._isArabic ? (announcement.Link_AR?.Url || '') : (announcement.Link_EN?.Url || '');

      const announcementCategory = announcement.Category || {};
      const categoryTitle = this._isArabic ?
        (announcementCategory.Title_AR || announcementCategory.Title || '') :
        (announcementCategory.Title || '');
      const fullCategoryData = this.categories.find(cat =>
        cat.Id === announcementCategory?.Id || cat.Title === announcementCategory?.Title
      );

      let iconFileName = '';
      if (fullCategoryData?.Icon) {
        try {
          const iconData = JSON.parse(fullCategoryData.Icon);
          iconFileName = iconData.fileName || '';
        } catch (e) {
          console.warn('Invalid Icon JSON format:', fullCategoryData.Icon);
        }
      }

      let iconSrc = '';
      if (fullCategoryData?.AttachmentFiles && fullCategoryData.AttachmentFiles.length > 0) {
        const iconFile = fullCategoryData.AttachmentFiles.find((file: any) =>
          file.FileName === iconFileName || (iconFileName && file.FileName.includes(iconFileName.split('_')[0]))
        );
        iconSrc = iconFile ? iconFile.ServerRelativeUrl : fullCategoryData.AttachmentFiles[0].ServerRelativeUrl;
      }

      const formattedDate = this.formatDate(announcement.Begins);
      const shortDescription = description && description.length > 60 ? description.substring(0, 60) + '...' : (description || '');

      const isUnread = !readAnnouncements.includes(announcement.Id);
      const newBadge = isNew(announcement.Begins) ? `<span class="badge bg-warning text-dark ms-2">${translate('New', 'جديد')}</span>` : '';

      return `
      <a href="${link || '#'}" 
         class="mb-3 d-flex align-items-start text-decoration-none announcement-item" 
         data-ann-id="${announcement.Id}" 
         data-link="${link || ''}">
        <div class="me-3 rounded p-2 d-flex align-items-center justify-content-center">
          ${iconSrc ? `<img src="${iconSrc}" alt="${categoryTitle}"/>` : ''}
        </div>
        <div>
          <strong class="head-color ${isUnread ? 'fw-bold' : ''}">
              ${title}${newBadge}
            </strong>
            <br>
          <small class="text-color">${shortDescription}</small><br>
          <small class="text-color">${formattedDate}</small>
        </div>
      </a>
    `;
    }).join('<hr class="my-2">');
  }

  private attachAnnouncementClickHandlers(): void {
    const announcementItems = this.domElement.querySelectorAll('.announcement-item');

    announcementItems.forEach(item => {
      // إزالة أي معالج أحداث موجود مسبقًا لتجنب التكرار
      item.removeEventListener('click', this.handleAnnouncementClick);
      item.addEventListener('click', this.handleAnnouncementClick.bind(this));
    });
  }

  private handleAnnouncementClick(e: Event): void {
    e.preventDefault();
    const target = e.currentTarget as HTMLElement;
    const annId = parseInt(target.dataset.annId || '0');

    if (!annId) return;

    this.markAnnouncementAsRead(annId);

    const link = target.dataset.link || '';
    if (link) {
      setTimeout(() => window.open(link, '_blank'), 300);
    }
  }


  private markAnnouncementAsRead(id: number): void {
    const read = this.getReadAnnouncements();
    if (!read.includes(id)) {
      const updatedRead = [...read, id]; // إنشاء مصفوفة جديدة
      localStorage.setItem('readAnnouncements', JSON.stringify(updatedRead));

      // إعادة حساب العداد
      this.calculateUnreadCount();

      // تحديث الـ badge
      this.updateNotificationBadge();

      // إعادة توليد القائمة
      this.updateAnnouncementsDropdown();
    }
  }
  private updateAnnouncementsDropdown(): void {
    const dropdownContent = this.domElement.querySelector('.dropdown-menu.p-3');
    if (!dropdownContent) return;

    // إعادة توليد HTML للقائمة
    const newContent = `
    ${this.getAnnouncementsHtml()}
    ${this.announcements.length > 0 ? '<hr class="my-2">' : ''}
    <div class="text-end">
      <a href="${this._isArabic ? '/sites/intranet-qm/SitePages/ar/Announcement.aspx' : '/sites/intranet-qm/SitePages/Announcement.aspx'}" 
         class="fw-bold text-pink text-decoration-none btn-main-link">
        ${this._isArabic ? 'عرض الكل' : 'View All'}
      </a>
    </div>
  `;

    dropdownContent.innerHTML = newContent;

    // إعادة ربط event handlers
    this.attachAnnouncementClickHandlers();
  }





  private formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US');
  }

  private _loadAnimateCSS(): void {
    const linkId = 'animate-css-cdn';
    if (!document.getElementById(linkId)) {
      const link = document.createElement('link');
      link.id = linkId;
      link.rel = 'stylesheet';
      link.type = 'text/css';
      link.href = 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css';
      document.head.appendChild(link);
    }
  }







}



