import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient } from '@microsoft/sp-http';

export interface IOurStoriesWebPartProps {
  description: string;
}

export default class OurStoriesWebPart extends BaseClientSideWebPart<IOurStoriesWebPartProps> {
  private _isArabic: boolean = false;

  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().includes('/ar/');

    const viewAllLink = this._isArabic
      ? "/sites/intranet-qm/SitePages/ar/OurStories.aspx"
      : "/sites/intranet-qm/SitePages/OurStories.aspx";

    this.domElement.innerHTML = `
      <style>
        .vertical-carousel-inner {
          position: relative;
          height: 400px;
          overflow: hidden;
        }

        .vertical-carousel-item {
          position: absolute;
          width: 100%;
          transition: transform 0.5s ease-in-out;
          opacity: 0;
          pointer-events: none;
        }

        .vertical-carousel-item.active {
          opacity: 1;
          pointer-events: auto;
        }

        .vertical-carousel-arrows {
          display: flex;
          justify-content: space-between;
          margin-top: 10px;
        }

        .vertical-carousel-arrows button {
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
        }

        .news-hero-item img {
          width: 100%;
          height: auto;
          border-radius: 6px;
        }

        .news-hero-caption {
          margin-top: 10px;
        }

        .news-hero-caption h2 {
          font-size: 1.1rem;
        }
      </style>

      <div class="col-lg-12">
        <section class="f4-news stories-section h-100">
          <div class="card section-card card-color h-100 p-3">
            <div class="container-fluid">
              <h2 class="fw-bold tittle head-color ${this._isArabic ? "text-end" : ""}">
                ${this._isArabic ? "قراءات مثيرة للاهتمام" : "Interesting Readings"}
              </h2>
              <div class="news-hero-secondary custom-vertical-carousel">
                <div class="vertical-carousel-inner" id="carouselItems2">
                  <div class=" text-color  px-2 py-5">${this._isArabic ? "جارٍ تحميل القصص..." : "Loading stories..."}</div>
                </div>
                <div class="vertical-carousel-arrows">
                  <button class="carousel-arrow-up" aria-label="Previous"><i class="bi bi-arrow-up-circle"></i></button>
                  <button class="carousel-arrow-down" aria-label="Next"><i class="bi bi-arrow-down-circle"></i></button>
                </div>
              </div>
              <div class="${this._isArabic ? "text-start" : "text-end"} mt-3 card-footer">
                <a href="${viewAllLink}" class="text-decoration-none fw-bold btn-main-link">
                  ${this._isArabic ? "عرض المزيد" : "View All"}
                  <i class="bi ${this._isArabic ? 'bi-arrow-left-circle' : 'bi-arrow-right-circle'}"></i>
                </a>
              </div>
            </div>
          </div>
        </section>
      </div>
    `;

    this._getStories();
  }

  private async _getStories(): Promise<void> {
    const listUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Through%20Our%20Lens')/items?$filter=Active eq '1'&$top=2`;

    try {
      const response = await this.context.spHttpClient.get(listUrl, SPHttpClient.configurations.v1);
      const data = await response.json();
      const items = data.value;
      const container = this.domElement.querySelector('#carouselItems2');
      if (!container) return;

      if (!items || items.length === 0) {
        container.innerHTML = `<p class=" text-color ">${this._isArabic ? "لا توجد قصص متاحة حالياً." : "No stories available."}</p>`;
        return;
      }

      container.innerHTML = '';

      for (let index = 0; index < items.length; index++) {
        const item = items[index];
        const title = this._isArabic ? (item.Title_AR || '') : item.Title || '';
        const link = this._isArabic ? item?.Link_AR?.Url || '#' : item?.Link_EN?.Url || '#';
        let image = item.Image?.Url;

        // If no image is provided, try getting from attachments
        if (!image) {
          const attachments = await this.getAttachments(item.Id);
          image = attachments.length > 0 ? attachments[0] : require('./assets/img.jpg');
        }

        const div = document.createElement('div');
        div.className = `vertical-carousel-item ${index === 0 ? 'active' : ''}`;
        div.innerHTML = `
          <div class="news-hero-item news-overlay d-block">
            <img src="${image}" alt="${title}">
            <div class="news-hero-caption">
              <a href="${link}" class="text-decoration-none text-white main-color-hover">
                <h2 class="text-white">${title}</h2>
              </a>
            </div>
          </div>
        `;
        container.appendChild(div);
      }

      this._initializeCarousel(items.length);
    } catch (error) {
      console.error("Error fetching stories:", error);
      const container = this.domElement.querySelector('#carouselItems2');
      if (container) {
        container.innerHTML = `<p class="text-danger">${this._isArabic ? "فشل في تحميل القصص." : "Failed to load stories."}</p>`;
      }
    }
  }

  private _initializeCarousel(totalItems: number): void {
    let currentIndex = 0;
    const items = this.domElement.querySelectorAll('.vertical-carousel-item');

    const updateCarousel = () => {
      items.forEach((item: Element, index: number) => {
        item.classList.toggle('active', index === currentIndex);
      });
    };

    const upBtn = this.domElement.querySelector('.carousel-arrow-up');
    const downBtn = this.domElement.querySelector('.carousel-arrow-down');

    upBtn?.addEventListener('click', () => {
      if (currentIndex > 0) {
        currentIndex--;
        updateCarousel();
      }
    });

    downBtn?.addEventListener('click', () => {
      if (currentIndex < totalItems - 1) {
        currentIndex++;
        updateCarousel();
      }
    });

    updateCarousel();
  }

  private async getAttachments(itemId: number): Promise<string[]> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Through%20Our%20Lens')/items(${itemId})/AttachmentFiles`;

    try {
      const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
      const data = await response.json();
      return data.value.map((file: any) => file.ServerRelativeUrl);
    } catch (err) {
      console.error('Error fetching attachments for item', itemId, err);
      return [];
    }
  }
}
