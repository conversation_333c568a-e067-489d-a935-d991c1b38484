import { BaseClientSideWebPart } from "@microsoft/sp-webpart-base";
import { HttpClient, IHttpClientOptions, HttpClientResponse } from "@microsoft/sp-http";

export interface IHomeSliderWebPartProps {
  description: string;
}

export interface ICategory {
  id: string;
  name: string;
  icon: string;
  subCategories?: ISubCategory[];
}

export interface ISubCategory {
  id: string;
  name: string;
  categoryId: string;
  suppliers?: ISupplier[];
}

export interface ISupplier {
  id: string;
  name: string;
  subCategoryId: string;
}

export default class HomeSliderWebPart extends BaseClientSideWebPart<IHomeSliderWebPartProps> {
  private _slidesData: any[] = [];
  private _categoriesData: ICategory[] = [];
  private _subCategoriesData: ISubCategory[] = [];
  private _suppliersData: ISupplier[] = [];
  private _isArabic: boolean = false;
  private _isInSubsite: boolean = false;
  private token: string = "";
  private _isDataLoaded: boolean = false;

  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().includes("/ar/");
    this._isInSubsite = this._checkIfInSubsite();

    if (!this._isDataLoaded) {
      this.domElement.innerHTML = `<div class="loading-spinner">Loading content...</div>`;
      return;
    }

    const previousText = this._isArabic ? "السابق" : "Previous";
    const nextText = this._isArabic ? "التالي" : "Next";

    const sectionClass = this._isInSubsite
      ? "HomeBanner DepartmentCarousel"
      : "HomeBanner";

    this.domElement.innerHTML = `
    <style>
      .carousel-item img {
        width: 100%;
        height: auto;
        max-height: 500px;
        object-fit: cover;
      }
      .loading-spinner {
        text-align: center;
        padding: 20px;
      }
      .no-slides-message {
        text-align: center;
        padding: 50px;
        background: #f5f5f5;
      }
      .category-card {
        transition: transform 0.3s ease;
      }
      .category-card:hover {
        transform: translateY(-5px);
      }
      .category-card img {
        object-fit: contain;
        max-height: 48px;
      }
      .selected-category-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
      }
      .selected-category-info .badge {
        background-color: #007bff;
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.875rem;
      }
    </style>

    <!-- Selected Category Info (if in subsite) -->
    ${this._renderSelectedCategoryInfo()}

    <!-- Slider Section -->
    <section class="${sectionClass}">
      <div id="qmCarousel" class="carousel slide carousel-fade" data-bs-ride="carousel">
        <div class="carousel-inner" id="carouselSlidesContainer">
          ${this._renderSlides()}
        </div>
        ${this._slidesData.length > 1 ? `
          <button class="carousel-control-prev" type="button" data-bs-target="#qmCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">${previousText}</span>
          </button>
          <button class="carousel-control-next" type="button" data-bs-target="#qmCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">${nextText}</span>
          </button>
          <div class="carousel-indicators" id="carouselIndicators">
            ${this._renderIndicators()}
          </div>
        ` : ''}
      </div>
    </section>

    <!-- Categories Section -->
    ${this._renderCategories()}
    `;

    this._attachImageClickEvents();
  }

  protected async onInit(): Promise<void> {
    try {
      // Set global reference for accessing methods from HTML
      (window as any).featuredDealsWebPart = this;

      await this._login();
      await Promise.all([
        this._getSlidesData(),
        this._getCategoriesData(),
        this._getSubCategoriesData(),
        this._getSuppliersData()
      ]);
      this._isDataLoaded = true;
      this.render();
    } catch (error) {
      console.error("Initialization failed:", error);
      this.domElement.innerHTML = `<div class="error-message">Failed to load content. Please try again later.</div>`;
    }
  }

  private async _login(): Promise<void> {
    try {
      const url = 'https://mazaya-backend.qm.org.qa/api/Account/login';
      const body = JSON.stringify({
        username: '<EMAIL>',
        password: 'Qatar@2025'
      });

      const options: IHttpClientOptions = {
        headers: { 'Content-Type': 'application/json' },
        body
      };

      const resp: HttpClientResponse = await this.context.httpClient.post(
        url,
        HttpClient.configurations.v1,
        options
      );

      if (!resp.ok) {
        throw new Error(`Login failed with status ${resp.status}`);
      }

      const data = await resp.json();
      this.token = data.accessToken || data.token || data;
      console.log("Login successful, token received");
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  private async _getSlidesData(): Promise<void> {
    try {
      if (!this.token) {
        throw new Error("No authentication token available");
      }

      const apiUrl = 'https://mazaya-backend.qm.org.qa/api/Offers/GetFeatureDeals/';
      const response = await this.context.httpClient.get(
        apiUrl,
        HttpClient.configurations.v1,
        {
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Accept': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const result = await response.json();
      console.log("API response data:", result);

      // Filter out items with null or empty bannerUrl
      const items = result?.data?.items || [];

      this._slidesData = items
        .filter((item: any) => item?.bannerUrl)
        .map((item: any) => ({
          id: item.id,
          image: item.bannerUrl
        }));

      console.log("Processed slides data:", this._slidesData);
    } catch (error) {
      console.error("Error fetching slides data:", error);
      this._slidesData = [];
    }
  }

  private async _getCategoriesData(): Promise<void> {
    try {
      if (!this.token) {
        throw new Error("No authentication token available");
      }

      const apiUrl = 'https://mazaya-backend.qm.org.qa/api/Categories';
      const response = await this.context.httpClient.get(
        apiUrl,
        HttpClient.configurations.v1,
        {
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Accept': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Categories API request failed with status ${response.status}`);
      }

      const result = await response.json();
      console.log("Categories API response:", result);

      // Assuming the API returns an array of categories
      this._categoriesData = (Array.isArray(result.data) ? result.data : [])
      .filter((item: any) => item?.name && item?.icon)
      .map((item: any) => ({
        id: item.id,
        name: item.name,
        icon: item.icon,
      }));

      // Store categories in cookies
      this._setCookie('categoriesData', JSON.stringify(this._categoriesData));

      console.log("Processed categories data:", this._categoriesData);
    } catch (error) {
      console.error("Error fetching categories data:", error);
      this._categoriesData = [];
    }
  }

  // Get SubCategories data from API
  private async _getSubCategoriesData(): Promise<void> {
    try {
      if (!this.token) {
        throw new Error("No authentication token available");
      }

      const apiUrl = 'https://mazaya-backend.qm.org.qa/api/SubCategories';
      const response = await this.context.httpClient.get(
        apiUrl,
        HttpClient.configurations.v1,
        {
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Accept': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`SubCategories API request failed with status ${response.status}`);
      }

      const result = await response.json();
      console.log("SubCategories API response:", result);

      this._subCategoriesData = (Array.isArray(result.data) ? result.data : [])
        .filter((item: any) => item?.name && item?.categoryId)
        .map((item: any) => ({
          id: item.id,
          name: item.name,
          categoryId: item.categoryId
        }));

      // Store in cookies
      this._setCookie('subCategoriesData', JSON.stringify(this._subCategoriesData));
      console.log("Processed subCategories data:", this._subCategoriesData);
    } catch (error) {
      console.error("Error fetching subCategories data:", error);
      this._subCategoriesData = [];
    }
  }

  // Get Suppliers data from API
  private async _getSuppliersData(): Promise<void> {
    try {
      if (!this.token) {
        throw new Error("No authentication token available");
      }

      const apiUrl = 'https://mazaya-backend.qm.org.qa/api/Suppliers';
      const response = await this.context.httpClient.get(
        apiUrl,
        HttpClient.configurations.v1,
        {
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Accept': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Suppliers API request failed with status ${response.status}`);
      }

      const result = await response.json();
      console.log("Suppliers API response:", result);

      this._suppliersData = (Array.isArray(result.data) ? result.data : [])
        .filter((item: any) => item?.name)
        .map((item: any) => ({
          id: item.id,
          name: item.name,
          subCategoryId: item.subCategoryId || ''
        }));

      // Store in cookies
      this._setCookie('suppliersData', JSON.stringify(this._suppliersData));
      console.log("Processed suppliers data:", this._suppliersData);
    } catch (error) {
      console.error("Error fetching suppliers data:", error);
      this._suppliersData = [];
    }
  }

  private _renderSlides(): string {
    if (this._slidesData.length === 0) {
      return `<div class="carousel-item active">
                <div class="no-slides-message">No slides available</div>
              </div>`;
    }

    return this._slidesData
      .map((slide, index) => {
        return `
        <div class="carousel-item ${index === 0 ? "active" : ""}" data-slide-id="${slide.id}">
          <img 
            src="${slide.image}" 
            alt="Slide ${index + 1}" 
            class="d-block w-100" 
            loading="lazy"
            onerror="this.onerror=null;this.style.display='none';"
          />
        </div>
      `;
      })
      .join("");
  }

  private _renderIndicators(): string {
    return this._slidesData
      .map((_, index) =>
        `<button type="button" data-bs-target="#qmCarousel" data-bs-slide-to="${index}" 
          class="${index === 0 ? "active" : ""}" aria-label="Slide ${index + 1}"></button>`
      )
      .join("");
  }

  private _renderCategories(): string {
    if (this._categoriesData.length === 0) {
      return '';
    }

    // If we're in a subsite, show filtered categories or highlight the selected one
    const selectedCategoryId = this._getCookie('selectedCategoryId');

    return `
    <div class="col-lg-12 mt-4">
      <div class="p-3">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h2 class="fw-bold head-color mb-0">${this._isArabic ? 'تصفح حسب الفئات' : 'Browse by Categories'}</h2>
          ${this._isInSubsite ? `
            <a href="/sites/intranet-qm/SitePages/TopicHome.aspx" class="btn btn-outline-primary btn-sm">
              ${this._isArabic ? 'عرض جميع الفئات' : 'View All Categories'}
            </a>
          ` : ''}
        </div>
        <div class="row g-4 mazya-cat">
          ${this._categoriesData.map(category => {
            const isSelected = selectedCategoryId === category.id;
            const cardClass = isSelected ? 'category-card card-color h-100 text-center border-2 border-primary shadow-lg' : 'category-card card-color h-100 text-center border-0 shadow-sm transition-hover';

            return `
              <div class="col-md-6 col-lg-4">
                <a href="#" class="card text-decoration-none ${cardClass}">
                  <div class="card-body d-flex flex-column align-items-center justify-content-center py-4">
                    <img src="${category.icon}" alt="${category.name}" width="48" height="48" />
                    <h5 class="fw-bold head-color mb-0 mt-2">${category.name}</h5>
                    ${isSelected ? `
                      <small class="text-primary mt-1">
                        <i class="bi bi-check-circle-fill"></i> ${this._isArabic ? 'محدد حالياً' : 'Currently Selected'}
                      </small>
                    ` : ''}
                  </div>
                </a>
              </div>
            `;
          }).join('')}
        </div>
      </div>
    </div>
    `;
  }

  // Helper function to check if we're in a subsite
  private _checkIfInSubsite(): boolean {
    try {
      const currentUrl = window.location.pathname.toLowerCase();
      const subsitePattern = /\/sites\/intranet-qm\/([^\/]+)\/sitepages\//i;
      const match = currentUrl.match(subsitePattern);

      if (match && match[1]) {
        const potentialSubsite = match[1].toLowerCase();
        // تجاهل 'ar' لأنه language code وليس subsite
        return potentialSubsite !== 'ar';
      }

      return false;
    } catch (error) {
      console.error('Error checking if in subsite:', error);
      return false;
    }
  }

  // Render selected category information
  private _renderSelectedCategoryInfo(): string {
    if (!this._isInSubsite) {
      return '';
    }

    const selections = this.getCurrentSelections();

    if (!selections.category) {
      return '';
    }

    const clearText = this._isArabic ? 'مسح الاختيار' : 'Clear Selection';
    const categoryText = this._isArabic ? 'الفئة:' : 'Category:';
    const subCategoryText = this._isArabic ? 'الفئة الفرعية:' : 'SubCategory:';
    const supplierText = this._isArabic ? 'المورد:' : 'Supplier:';

    return `
      <div class="selected-category-info">
        <div class="d-flex justify-content-between align-items-center flex-wrap">
          <div class="d-flex flex-wrap gap-2 align-items-center">
            <span class="text-muted">${categoryText}</span>
            <span class="badge bg-primary">${selections.category.name}</span>

            ${selections.subCategory ? `
              <span class="text-muted">${subCategoryText}</span>
              <span class="badge bg-secondary">${selections.subCategory.name}</span>
            ` : ''}

            ${selections.supplier ? `
              <span class="text-muted">${supplierText}</span>
              <span class="badge bg-success">${selections.supplier.name}</span>
            ` : ''}
          </div>
          <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.featuredDealsWebPart.clearAllSelections()">
            ${clearText}
          </button>
        </div>
      </div>
    `;
  }

  // Clear category selection
  public clearCategorySelection(): void {
    this._deleteCookie('selectedCategoryId');
    this._deleteCookie('selectedCategoryName');

    // Navigate back to main site
    const currentOrigin = window.location.origin;
    const mainSiteUrl = `${currentOrigin}/sites/intranet-qm/SitePages/TopicHome.aspx`;
    window.location.href = mainSiteUrl;
  }

  // Clear all selections
  public clearAllSelections(): void {
    this._deleteCookie('selectedCategoryId');
    this._deleteCookie('selectedCategoryName');
    this._deleteCookie('selectedSubCategoryId');
    this._deleteCookie('selectedSubCategoryName');
    this._deleteCookie('selectedSupplierId');
    this._deleteCookie('selectedSupplierName');

    // Navigate back to main site
    const currentOrigin = window.location.origin;
    const mainSiteUrl = `${currentOrigin}/sites/intranet-qm/SitePages/TopicHome.aspx`;
    window.location.href = mainSiteUrl;
  }

  // Get category statistics (you can extend this to fetch real data)
  private _getCategoryStats(categoryId: string): { deals: number; offers: number } {
    // This is a placeholder - you can implement actual API calls to get statistics
    const stats = {
      deals: Math.floor(Math.random() * 50) + 10, // Random number for demo
      offers: Math.floor(Math.random() * 20) + 5   // Random number for demo
    };

    return stats;
  }

  // Get current subsite name from URL
  private _getCurrentSubsiteName(): string | null {
    try {
      const currentUrl = window.location.pathname.toLowerCase();
      const subsiteMatch = currentUrl.match(/\/sites\/intranet-qm\/([^\/]+)\//);

      if (subsiteMatch && subsiteMatch[1] !== 'sitepages') {
        return subsiteMatch[1];
      }

      return null;
    } catch (error) {
      console.error('Error getting current subsite name:', error);
      return null;
    }
  }

  // Cookie management functions
  private _setCookie(name: string, value: string, days: number = 30): void {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
  }

  private _getCookie(name: string): string | null {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
  }

  private _deleteCookie(name: string): void {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  }

  // Get stored data from cookies
  public getStoredCategories(): ICategory[] {
    const stored = this._getCookie('categoriesData');
    return stored ? JSON.parse(stored) : [];
  }

  public getStoredSubCategories(): ISubCategory[] {
    const stored = this._getCookie('subCategoriesData');
    return stored ? JSON.parse(stored) : [];
  }

  public getStoredSuppliers(): ISupplier[] {
    const stored = this._getCookie('suppliersData');
    return stored ? JSON.parse(stored) : [];
  }

  // Get subcategories for a specific category
  public getSubCategoriesByCategory(categoryId: string): ISubCategory[] {
    const subCategories = this.getStoredSubCategories();
    return subCategories.filter(sub => sub.categoryId === categoryId);
  }

  // Get suppliers for a specific subcategory
  public getSuppliersBySubCategory(subCategoryId: string): ISupplier[] {
    const suppliers = this.getStoredSuppliers();
    return suppliers.filter(supplier => supplier.subCategoryId === subCategoryId);
  }

  // Store selected category, subcategory, and supplier
  public selectCategory(categoryId: string, categoryName: string): void {
    this._setCookie('selectedCategoryId', categoryId);
    this._setCookie('selectedCategoryName', categoryName);
    // Clear previous subcategory and supplier selections
    this._deleteCookie('selectedSubCategoryId');
    this._deleteCookie('selectedSubCategoryName');
    this._deleteCookie('selectedSupplierId');
    this._deleteCookie('selectedSupplierName');
  }

  public selectSubCategory(subCategoryId: string, subCategoryName: string): void {
    this._setCookie('selectedSubCategoryId', subCategoryId);
    this._setCookie('selectedSubCategoryName', subCategoryName);
    // Clear previous supplier selection
    this._deleteCookie('selectedSupplierId');
    this._deleteCookie('selectedSupplierName');
  }

  public selectSupplier(supplierId: string, supplierName: string): void {
    this._setCookie('selectedSupplierId', supplierId);
    this._setCookie('selectedSupplierName', supplierName);
  }

  // Get current selections
  public getCurrentSelections(): {
    category?: { id: string; name: string };
    subCategory?: { id: string; name: string };
    supplier?: { id: string; name: string };
  } {
    const categoryId = this._getCookie('selectedCategoryId');
    const categoryName = this._getCookie('selectedCategoryName');
    const subCategoryId = this._getCookie('selectedSubCategoryId');
    const subCategoryName = this._getCookie('selectedSubCategoryName');
    const supplierId = this._getCookie('selectedSupplierId');
    const supplierName = this._getCookie('selectedSupplierName');

    const selections: any = {};

    if (categoryId && categoryName) {
      selections.category = { id: categoryId, name: categoryName };
    }

    if (subCategoryId && subCategoryName) {
      selections.subCategory = { id: subCategoryId, name: subCategoryName };
    }

    if (supplierId && supplierName) {
      selections.supplier = { id: supplierId, name: supplierName };
    }

    return selections;
  }

  // Category to subsite mapping function (simplified)
  private _getCategorySubsite(categoryId: string, categoryName: string): string | null {
    // If no direct mapping, try to map by category name
    const categoryNameLower = categoryName.toLowerCase();

    // Define category name to subsite mappings
    const nameToSubsiteMapping: { [key: string]: string } = {
      // English mappings
      'information technology': 'it',
      'it': 'it',
      'technology': 'it',
      'human resources': 'hr',
      'hr': 'hr',
      'finance': 'finance',
      'financial': 'finance',
      'accounting': 'finance',
      'marketing': 'marketing',
      'communications': 'marketing',
      'administration': 'administration',
      'admin': 'administration',
      'legal': 'legal',
      'security': 'security',
      'facilities': 'facilities',
      'operations': 'operations',

      // Arabic mappings
      'تقنية المعلومات': 'it',
      'الموارد البشرية': 'hr',
      'المالية': 'finance',
      'المحاسبة': 'finance',
      'التسويق': 'marketing',
      'الاتصالات': 'marketing',
      'الإدارة': 'administration',
      'القانونية': 'legal',
      'الأمن': 'security',
      'المرافق': 'facilities',
      'العمليات': 'operations'
    };

    // Check for exact match
    if (nameToSubsiteMapping[categoryNameLower]) {
      return nameToSubsiteMapping[categoryNameLower];
    }

    // Check for partial matches
    for (const [key, subsite] of Object.entries(nameToSubsiteMapping)) {
      if (categoryNameLower.includes(key) || key.includes(categoryNameLower)) {
        return subsite;
      }
    }

    return null;
  }

  private _navigateToSubsite(categoryId: string, categoryName: string): void {
    try {
      // Store category information using the public method
      this.selectCategory(categoryId, categoryName);

      // Get the appropriate subsite
      const subsiteName = this._getCategorySubsite(categoryId, categoryName);

      if (subsiteName) {
        // Navigate to the specific subsite
        const currentOrigin = window.location.origin;
        const subsiteUrl = `${currentOrigin}/sites/intranet-qm/${subsiteName}/SitePages/Home.aspx`;

        console.log(`Navigating to subsite: ${subsiteUrl}`);
        window.location.href = subsiteUrl;
      } else {
        // If no specific subsite mapping found, store the category and stay on main site
        console.log(`No subsite mapping found for category: ${categoryName}. Staying on main site.`);

        // You could redirect to a general category page or show a message
        // For now, we'll just log the category selection
        console.log(`Category selected: ${categoryName} (ID: ${categoryId})`);
      }
    } catch (error) {
      console.error('Error navigating to subsite:', error);
    }
  }

  private _attachImageClickEvents(): void {
    // For slider images
    const carouselItems = this.domElement.querySelectorAll(".carousel-item");
    carouselItems.forEach((item) => {
      item.addEventListener("click", (e) => {
        if ((e.target as HTMLElement).tagName === 'IMG') {
          const img = e.target as HTMLImageElement;
          if (img.src) {
            window.open(img.src, "_blank");
          }
        }
      });
    });

    // For category cards
    const categoryCards = this.domElement.querySelectorAll(".category-card");
    categoryCards.forEach((card, index) => {
      card.addEventListener("click", (e) => {
        e.preventDefault();
        const category = this._categoriesData[index];
        if (category) {
          console.log(`Category clicked: ${category.name} (ID: ${category.id})`);
          this._navigateToSubsite(category.id, category.name);
        }
      });
    });
  }
}