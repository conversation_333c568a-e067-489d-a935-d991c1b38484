import { BaseClientSideWebPart } from "@microsoft/sp-webpart-base";
import { HttpClient, IHttpClientOptions, HttpClientResponse } from "@microsoft/sp-http";

export interface IHomeSliderWebPartProps {
  description: string;
}

export interface ICategory {
  id: string;
  name: string;
  icon: string;
}

export interface ICategorySubsiteMapping {
  [categoryId: string]: string; // categoryId -> subsiteName
}

export default class HomeSliderWebPart extends BaseClientSideWebPart<IHomeSliderWebPartProps> {
  private _slidesData: any[] = [];
  private _categoriesData: ICategory[] = [];
  private _isArabic: boolean = false;
  private _isInSubsite: boolean = false;
  private token: string = "";
  private _isDataLoaded: boolean = false;

  // Mapping between category names and subsite names
  private _categorySubsiteMapping: ICategorySubsiteMapping = {
    // Add your category to subsite mappings here
    // Example: "1": "it", "2": "hr", "3": "finance"
    // You can customize these based on your actual category IDs and subsite names
  };

  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().includes("/ar/");
    this._isInSubsite = this._checkIfInSubsite();

    if (!this._isDataLoaded) {
      this.domElement.innerHTML = `<div class="loading-spinner">Loading content...</div>`;
      return;
    }

    const previousText = this._isArabic ? "السابق" : "Previous";
    const nextText = this._isArabic ? "التالي" : "Next";

    const sectionClass = this._isInSubsite
      ? "HomeBanner DepartmentCarousel"
      : "HomeBanner";

    this.domElement.innerHTML = `
    <style>
      .carousel-item img {
        width: 100%;
        height: auto;
        max-height: 500px;
        object-fit: cover;
      }
      .loading-spinner {
        text-align: center;
        padding: 20px;
      }
      .no-slides-message {
        text-align: center;
        padding: 50px;
        background: #f5f5f5;
      }
      .category-card {
        transition: transform 0.3s ease;
      }
      .category-card:hover {
        transform: translateY(-5px);
      }
      .category-card img {
        object-fit: contain;
        max-height: 48px;
      }
      .selected-category-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
      }
      .selected-category-info .badge {
        background-color: #007bff;
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.875rem;
      }
    </style>

    <!-- Selected Category Info (if in subsite) -->
    ${this._renderSelectedCategoryInfo()}

    <!-- Slider Section -->
    <section class="${sectionClass}">
      <div id="qmCarousel" class="carousel slide carousel-fade" data-bs-ride="carousel">
        <div class="carousel-inner" id="carouselSlidesContainer">
          ${this._renderSlides()}
        </div>
        ${this._slidesData.length > 1 ? `
          <button class="carousel-control-prev" type="button" data-bs-target="#qmCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">${previousText}</span>
          </button>
          <button class="carousel-control-next" type="button" data-bs-target="#qmCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">${nextText}</span>
          </button>
          <div class="carousel-indicators" id="carouselIndicators">
            ${this._renderIndicators()}
          </div>
        ` : ''}
      </div>
    </section>

    <!-- Categories Section -->
    ${this._renderCategories()}
    `;

    this._attachImageClickEvents();
  }

  protected async onInit(): Promise<void> {
    try {
      // Set global reference for accessing methods from HTML
      (window as any).featuredDealsWebPart = this;

      await this._login();
      await Promise.all([
        this._getSlidesData(),
        this._getCategoriesData()
      ]);
      this._isDataLoaded = true;
      this.render();
    } catch (error) {
      console.error("Initialization failed:", error);
      this.domElement.innerHTML = `<div class="error-message">Failed to load content. Please try again later.</div>`;
    }
  }

  private async _login(): Promise<void> {
    try {
      const url = 'https://mazaya-backend.qm.org.qa/api/Account/login';
      const body = JSON.stringify({
        username: '<EMAIL>',
        password: 'Qatar@2025'
      });

      const options: IHttpClientOptions = {
        headers: { 'Content-Type': 'application/json' },
        body
      };

      const resp: HttpClientResponse = await this.context.httpClient.post(
        url,
        HttpClient.configurations.v1,
        options
      );

      if (!resp.ok) {
        throw new Error(`Login failed with status ${resp.status}`);
      }

      const data = await resp.json();
      this.token = data.accessToken || data.token || data;
      console.log("Login successful, token received");
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  private async _getSlidesData(): Promise<void> {
    try {
      if (!this.token) {
        throw new Error("No authentication token available");
      }

      const apiUrl = 'https://mazaya-backend.qm.org.qa/api/Offers/GetFeatureDeals/';
      const response = await this.context.httpClient.get(
        apiUrl,
        HttpClient.configurations.v1,
        {
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Accept': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const result = await response.json();
      console.log("API response data:", result);

      // Filter out items with null or empty bannerUrl
      const items = result?.data?.items || [];

      this._slidesData = items
        .filter((item: any) => item?.bannerUrl)
        .map((item: any) => ({
          id: item.id,
          image: item.bannerUrl
        }));

      console.log("Processed slides data:", this._slidesData);
    } catch (error) {
      console.error("Error fetching slides data:", error);
      this._slidesData = [];
    }
  }

  private async _getCategoriesData(): Promise<void> {
    try {
      if (!this.token) {
        throw new Error("No authentication token available");
      }

      const apiUrl = 'https://mazaya-backend.qm.org.qa/api/Categories';
      const response = await this.context.httpClient.get(
        apiUrl,
        HttpClient.configurations.v1,
        {
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Accept': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Categories API request failed with status ${response.status}`);
      }

      const result = await response.json();
      console.log("Categories API response:", result);

      // Assuming the API returns an array of categories
      this._categoriesData = (Array.isArray(result.data) ? result.data : [])
      .filter((item: any) => item?.name && item?.icon)
      .map((item: any) => ({
        id: item.id,
        name: item.name,
        icon: item.icon,
      }));

      // Update category to subsite mapping based on actual data
      this._updateCategorySubsiteMapping();

      console.log("Processed categories data:", this._categoriesData);
    } catch (error) {
      console.error("Error fetching categories data:", error);
      this._categoriesData = [];
    }
  }

  // Update category to subsite mapping based on actual category data
  private _updateCategorySubsiteMapping(): void {
    this._categoriesData.forEach(category => {
      const subsite = this._getCategorySubsite(category.id, category.name);
      if (subsite) {
        this._categorySubsiteMapping[category.id] = subsite;
      }
    });

    console.log("Updated category to subsite mapping:", this._categorySubsiteMapping);
  }

  private _renderSlides(): string {
    if (this._slidesData.length === 0) {
      return `<div class="carousel-item active">
                <div class="no-slides-message">No slides available</div>
              </div>`;
    }

    return this._slidesData
      .map((slide, index) => {
        return `
        <div class="carousel-item ${index === 0 ? "active" : ""}" data-slide-id="${slide.id}">
          <img 
            src="${slide.image}" 
            alt="Slide ${index + 1}" 
            class="d-block w-100" 
            loading="lazy"
            onerror="this.onerror=null;this.style.display='none';"
          />
        </div>
      `;
      })
      .join("");
  }

  private _renderIndicators(): string {
    return this._slidesData
      .map((_, index) =>
        `<button type="button" data-bs-target="#qmCarousel" data-bs-slide-to="${index}" 
          class="${index === 0 ? "active" : ""}" aria-label="Slide ${index + 1}"></button>`
      )
      .join("");
  }

  private _renderCategories(): string {
    if (this._categoriesData.length === 0) {
      return '';
    }

    // If we're in a subsite, show filtered categories or highlight the selected one
    const selectedCategoryId = this._getCookie('selectedCategoryId');

    return `
    <div class="col-lg-12 mt-4">
      <div class="p-3">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h2 class="fw-bold head-color mb-0">${this._isArabic ? 'تصفح حسب الفئات' : 'Browse by Categories'}</h2>
          ${this._isInSubsite ? `
            <a href="/sites/intranet-qm/SitePages/TopicHome.aspx" class="btn btn-outline-primary btn-sm">
              ${this._isArabic ? 'عرض جميع الفئات' : 'View All Categories'}
            </a>
          ` : ''}
        </div>
        <div class="row g-4 mazya-cat">
          ${this._categoriesData.map(category => {
            const isSelected = selectedCategoryId === category.id;
            const cardClass = isSelected ? 'category-card card-color h-100 text-center border-2 border-primary shadow-lg' : 'category-card card-color h-100 text-center border-0 shadow-sm transition-hover';

            return `
              <div class="col-md-6 col-lg-4">
                <a href="#" class="card text-decoration-none ${cardClass}">
                  <div class="card-body d-flex flex-column align-items-center justify-content-center py-4">
                    <img src="${category.icon}" alt="${category.name}" width="48" height="48" />
                    <h5 class="fw-bold head-color mb-0 mt-2">${category.name}</h5>
                    ${isSelected ? `
                      <small class="text-primary mt-1">
                        <i class="bi bi-check-circle-fill"></i> ${this._isArabic ? 'محدد حالياً' : 'Currently Selected'}
                      </small>
                    ` : ''}
                  </div>
                </a>
              </div>
            `;
          }).join('')}
        </div>
      </div>
    </div>
    `;
  }

  // Helper function to check if we're in a subsite
  private _checkIfInSubsite(): boolean {
    try {
      const currentUrl = window.location.pathname.toLowerCase();
      const subsitePattern = /\/sites\/intranet-qm\/([^\/]+)\/sitepages\//i;
      const match = currentUrl.match(subsitePattern);

      if (match && match[1]) {
        const potentialSubsite = match[1].toLowerCase();
        // تجاهل 'ar' لأنه language code وليس subsite
        return potentialSubsite !== 'ar';
      }

      return false;
    } catch (error) {
      console.error('Error checking if in subsite:', error);
      return false;
    }
  }

  // Render selected category information
  private _renderSelectedCategoryInfo(): string {
    if (!this._isInSubsite) {
      return '';
    }

    const selectedCategoryId = this._getCookie('selectedCategoryId');
    const selectedCategoryName = this._getCookie('selectedCategoryName');

    if (!selectedCategoryId || !selectedCategoryName) {
      return '';
    }

    const clearText = this._isArabic ? 'مسح الاختيار' : 'Clear Selection';
    const selectedText = this._isArabic ? 'الفئة المحددة:' : 'Selected Category:';

    return `
      <div class="selected-category-info">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <span class="text-muted">${selectedText}</span>
            <span class="badge ms-2">${selectedCategoryName}</span>
          </div>
          <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.featuredDealsWebPart.clearCategorySelection()">
            ${clearText}
          </button>
        </div>
      </div>
    `;
  }

  // Clear category selection
  public clearCategorySelection(): void {
    this._deleteCookie('selectedCategoryId');
    this._deleteCookie('selectedCategoryName');

    // Navigate back to main site
    const currentOrigin = window.location.origin;
    const mainSiteUrl = `${currentOrigin}/sites/intranet-qm/SitePages/TopicHome.aspx`;
    window.location.href = mainSiteUrl;
  }

  // Cookie management functions
  private _setCookie(name: string, value: string, days: number = 30): void {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
  }

  private _getCookie(name: string): string | null {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
  }

  private _deleteCookie(name: string): void {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  }

  // Category to subsite mapping function
  private _getCategorySubsite(categoryId: string, categoryName: string): string | null {
    // First check if we have a direct mapping by ID
    if (this._categorySubsiteMapping[categoryId]) {
      return this._categorySubsiteMapping[categoryId];
    }

    // If no direct mapping, try to map by category name
    const categoryNameLower = categoryName.toLowerCase();

    // Define category name to subsite mappings
    const nameToSubsiteMapping: { [key: string]: string } = {
      // English mappings
      'information technology': 'it',
      'it': 'it',
      'technology': 'it',
      'human resources': 'hr',
      'hr': 'hr',
      'finance': 'finance',
      'financial': 'finance',
      'accounting': 'finance',
      'marketing': 'marketing',
      'communications': 'marketing',
      'administration': 'administration',
      'admin': 'administration',
      'legal': 'legal',
      'security': 'security',
      'facilities': 'facilities',
      'operations': 'operations',

      // Arabic mappings
      'تقنية المعلومات': 'it',
      'الموارد البشرية': 'hr',
      'المالية': 'finance',
      'المحاسبة': 'finance',
      'التسويق': 'marketing',
      'الاتصالات': 'marketing',
      'الإدارة': 'administration',
      'القانونية': 'legal',
      'الأمن': 'security',
      'المرافق': 'facilities',
      'العمليات': 'operations'
    };

    // Check for exact match
    if (nameToSubsiteMapping[categoryNameLower]) {
      return nameToSubsiteMapping[categoryNameLower];
    }

    // Check for partial matches
    for (const [key, subsite] of Object.entries(nameToSubsiteMapping)) {
      if (categoryNameLower.includes(key) || key.includes(categoryNameLower)) {
        return subsite;
      }
    }

    return null;
  }

  private _navigateToSubsite(categoryId: string, categoryName: string): void {
    try {
      // Store category information in cookies
      this._setCookie('selectedCategoryId', categoryId);
      this._setCookie('selectedCategoryName', categoryName);

      // Get the appropriate subsite
      const subsiteName = this._getCategorySubsite(categoryId, categoryName);

      if (subsiteName) {
        // Navigate to the specific subsite
        const currentOrigin = window.location.origin;
        const subsiteUrl = `${currentOrigin}/sites/intranet-qm/${subsiteName}/SitePages/Home.aspx`;

        console.log(`Navigating to subsite: ${subsiteUrl}`);
        window.location.href = subsiteUrl;
      } else {
        // If no specific subsite mapping found, store the category and stay on main site
        console.log(`No subsite mapping found for category: ${categoryName}. Staying on main site.`);

        // You could redirect to a general category page or show a message
        // For now, we'll just log the category selection
        console.log(`Category selected: ${categoryName} (ID: ${categoryId})`);
      }
    } catch (error) {
      console.error('Error navigating to subsite:', error);
    }
  }

  private _attachImageClickEvents(): void {
    // For slider images
    const carouselItems = this.domElement.querySelectorAll(".carousel-item");
    carouselItems.forEach((item) => {
      item.addEventListener("click", (e) => {
        if ((e.target as HTMLElement).tagName === 'IMG') {
          const img = e.target as HTMLImageElement;
          if (img.src) {
            window.open(img.src, "_blank");
          }
        }
      });
    });

    // For category cards
    const categoryCards = this.domElement.querySelectorAll(".category-card");
    categoryCards.forEach((card, index) => {
      card.addEventListener("click", (e) => {
        e.preventDefault();
        const category = this._categoriesData[index];
        if (category) {
          console.log(`Category clicked: ${category.name} (ID: ${category.id})`);
          this._navigateToSubsite(category.id, category.name);
        }
      });
    });
  }
}