import { BaseClientSideWebPart } from "@microsoft/sp-webpart-base";
import { HttpClient, IHttpClientOptions, HttpClientResponse } from "@microsoft/sp-http";

export interface IHomeSliderWebPartProps {
  description: string;
}

export interface ICategory {
  id: string;
  name: string;
  icon: string;
  priority?: number;
  color?: string | null;
  isActive?: boolean;
  categoryStatus?: string;
  description?: string;
  parentCategoryId?: string | null;
  parentCategory?: any | null;
  subCategories?: any[];
  subCategorySuppliers?: any[];
}

export interface ICategorySubsiteMapping {
  [categoryId: string]: string; // categoryId -> subsiteName
}

export default class HomeSliderWebPart extends BaseClientSideWebPart<IHomeSliderWebPartProps> {
  private _slidesData: any[] = [];
  // private _categoriesData: ICategory[] = [];
  private _isArabic: boolean = false;
  private _isInSubsite: boolean = false;
  private token: string = "";
  private _isDataLoaded: boolean = false;

  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().includes("/ar/");

    if (!this._isDataLoaded) {
      this.domElement.innerHTML = `<div class="loading-spinner">Loading content...</div>`;
      return;
    }

    const previousText = this._isArabic ? "السابق" : "Previous";
    const nextText = this._isArabic ? "التالي" : "Next";

    const sectionClass = this._isInSubsite
      ? "HomeBanner DepartmentCarousel"
      : "HomeBanner";

    this.domElement.innerHTML = `
    <style>
      .carousel-item img {
        width: 100%;
        height: auto;
        max-height: 500px;
        object-fit: cover;
      }
      .loading-spinner {
        text-align: center;
        padding: 20px;
      }
      .no-slides-message {
        text-align: center;
        padding: 50px;
        background: #f5f5f5;
      }
      .category-card {
        transition: transform 0.3s ease;
      }
      .category-card:hover {
        transform: translateY(-5px);
      }
      .category-card img {
        object-fit: contain;
        max-height: 48px;
      }
    </style>

    <!-- Slider Section -->
    <section class="${sectionClass}">
      <div id="qmCarousel" class="carousel slide carousel-fade" data-bs-ride="carousel">
        <div class="carousel-inner" id="carouselSlidesContainer">
          ${this._renderSlides()}
        </div>
        ${this._slidesData.length > 1 ? `
          <button class="carousel-control-prev" type="button" data-bs-target="#qmCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">${previousText}</span>
          </button>
          <button class="carousel-control-next" type="button" data-bs-target="#qmCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">${nextText}</span>
          </button>
          <div class="carousel-indicators" id="carouselIndicators">
            ${this._renderIndicators()}
          </div>
        ` : ''}
      </div>
    </section>

    
    `;

    this._attachImageClickEvents();
  }

  protected async onInit(): Promise<void> {
    try {
      await this._login();
      await Promise.all([
        this._getSlidesData(),
        // this._getCategoriesData() 
      ]);
      this._isDataLoaded = true;
      this.render();
    } catch (error) {
      console.error("Initialization failed:", error);
      this.domElement.innerHTML = `<div class="error-message">Failed to load content. Please try again later.</div>`;
    }
  }

  private async _login(): Promise<void> {
    try {
      const url = 'https://mazaya-backend.qm.org.qa/api/Account/login';
      const body = JSON.stringify({
        username: '<EMAIL>',
        password: 'Qatar@2025'
      });

      const options: IHttpClientOptions = {
        headers: { 'Content-Type': 'application/json' },
        body
      };

      const resp: HttpClientResponse = await this.context.httpClient.post(
        url,
        HttpClient.configurations.v1,
        options
      );

      if (!resp.ok) {
        throw new Error(`Login failed with status ${resp.status}`);
      }

      const data = await resp.json();
      this.token = data.accessToken || data.token || data;
      console.log("Login successful, token received");
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  private async _getSlidesData(): Promise<void> {
    try {
      if (!this.token) {
        throw new Error("No authentication token available");
      }

      const apiUrl = 'https://mazaya-backend.qm.org.qa/api/Offers/GetFeatureDeals/';
      const response = await this.context.httpClient.get(
        apiUrl,
        HttpClient.configurations.v1,
        {
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Accept': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const result = await response.json();
      console.log("API response data:", result);

      // Filter out items with null or empty bannerUrl
      const items = result?.data?.items || [];

      this._slidesData = items
        .filter((item: any) => item?.bannerUrl)
        .map((item: any) => ({
          id: item.id,
          image: item.bannerUrl
        }));

      console.log("Processed slides data:", this._slidesData);
    } catch (error) {
      console.error("Error fetching slides data:", error);
      this._slidesData = [];
    }
  }

  // private async _getCategoriesData(): Promise<void> {
  //   try {
  //     if (!this.token) {
  //       throw new Error("No authentication token available");
  //     }

  //     const apiUrl = 'https://mazaya-backend.qm.org.qa/api/Categories';
  //     const response = await this.context.httpClient.get(
  //       apiUrl,
  //       HttpClient.configurations.v1,
  //       {
  //         headers: {
  //           'Authorization': `Bearer ${this.token}`,
  //           'Accept': 'application/json'
  //         }
  //       }
  //     );

  //     if (!response.ok) {
  //       throw new Error(`Categories API request failed with status ${response.status}`);
  //     }

  //     const result = await response.json();
  //     console.log("Categories API response:", result);

  //     // Store complete raw data in localStorage
  //     if (result.data && Array.isArray(result.data)) {
  //       localStorage.setItem('allCategoriesRawData', JSON.stringify(result.data));
  //       console.log("Complete categories data stored in localStorage");
  //     }

  //     // Process data for display (keeping only essential fields for UI)
  //     this._categoriesData = (Array.isArray(result.data) ? result.data : [])
  //     .filter((item: any) => item?.name && item?.icon)
  //     .map((item: any) => ({
  //       id: item.id,
  //       name: item.name,
  //       icon: item.icon,
  //       // Store reference to complete data
  //       _fullData: item
  //     }));

  //     console.log("Processed categories data:", this._categoriesData);
  //   } catch (error) {
  //     console.error("Error fetching categories data:", error);
  //     this._categoriesData = [];
  //   }
  // }

  private _renderSlides(): string {
    if (this._slidesData.length === 0) {
      return `<div class="carousel-item active">
                <div class="no-slides-message">No slides available</div>
              </div>`;
    }

    return this._slidesData
      .map((slide, index) => {
        return `
        <div class="carousel-item ${index === 0 ? "active" : ""}" data-slide-id="${slide.id}">
          <img 
            src="${slide.image}" 
            alt="Slide ${index + 1}" 
            class="d-block w-100" 
            loading="lazy"
            onerror="this.onerror=null;this.style.display='none';"
          />
        </div>
      `;
      })
      .join("");
  }

  private _renderIndicators(): string {
    return this._slidesData
      .map((_, index) =>
        `<button type="button" data-bs-target="#qmCarousel" data-bs-slide-to="${index}" 
          class="${index === 0 ? "active" : ""}" aria-label="Slide ${index + 1}"></button>`
      )
      .join("");
  }

  // private _renderCategories(): string {
  //   if (this._categoriesData.length === 0) {
  //     return '';
  //   }

  //   return `
  //   <div class="col-lg-12 mt-4">
  //     <div class="p-3">
  //       <div class="d-flex justify-content-between align-items-center mb-4">
  //         <h2 class="fw-bold head-color mb-0">${this._isArabic ? 'تصفح حسب الفئات' : 'Browse by Categories'}</h2>
  //       </div>
  //       <div class="row g-4 mazya-cat">
  //         ${this._categoriesData.map(category => `
  //           <div class="col-md-6 col-lg-4">
  //             <a href="#" class="card text-decoration-none category-card card-color h-100 text-center border-0 shadow-sm transition-hover">
  //               <div class="card-body d-flex flex-column align-items-center justify-content-center py-4">
  //                 <img src="${category.icon}" alt="${category.name}" width="48" height="48" />
  //                 <h5 class="fw-bold head-color mb-0 mt-2">${category.name}</h5>
  //               </div>
  //             </a>
  //           </div>
  //         `).join('')}
  //       </div>
  //     </div>
  //   </div>
  //   `;
  // }

  private _attachImageClickEvents(): void {
    // For slider images
    const carouselItems = this.domElement.querySelectorAll(".carousel-item");
    carouselItems.forEach((item) => {
      item.addEventListener("click", (e) => {
        if ((e.target as HTMLElement).tagName === 'IMG') {
          const img = e.target as HTMLImageElement;
          if (img.src) {
            window.open(img.src, "_blank");
          }
        }
      });
    });

    // For category cards
    // const categoryCards = this.domElement.querySelectorAll(".category-card");
    // categoryCards.forEach((card, index) => {
    //   card.addEventListener("click", (e) => {
    //     e.preventDefault();
    //     const categoryData = this._categoriesData[index];
    //     if (categoryData) {
    //       const completeData = this._getCompleteCategoryData(categoryData.id);
    //       if (completeData) {
    //         this._storeSelectedCategory(completeData);

    //         // Navigate to details page with category ID
    //         const detailsUrl = `/sites/intranet-qm/SitePages/CategoryDetails.aspx?categoryId=${categoryData.id}`;
    //         window.location.href = detailsUrl;
    //       }
    //     }
    //   });
    // });
  }

  // // Get complete category data by ID from stored raw data
  // private _getCompleteCategoryData(categoryId: string): any {
  //   try {
  //     const rawData = localStorage.getItem('allCategoriesRawData');
  //     if (rawData) {
  //       const allCategories = JSON.parse(rawData);
  //       return allCategories.find((cat: any) => cat.id === categoryId);
  //     }
  //     return null;
  //   } catch (error) {
  //     console.error('Error getting complete category data:', error);
  //     return null;
  //   }
  // }

  // // Store selected category with complete data
  // private _storeSelectedCategory(completeData: any): void {
  //   try {
  //     // Store only the complete object as received from API
  //     localStorage.setItem('selectedCategoryComplete', JSON.stringify(completeData));

  //     console.log('Complete category data stored in localStorage:', completeData);
  //   } catch (error) {
  //     console.error('Error storing complete category data:', error);
  //   }
  // }

  // // Public method to get stored complete category data
  // public getStoredCompleteCategoryData(): any {
  //   try {
  //     const storedData = localStorage.getItem('selectedCategoryComplete');
  //     if (storedData) {
  //       return JSON.parse(storedData);
  //     }
  //     return null;
  //   } catch (error) {
  //     console.error('Error retrieving complete category data:', error);
  //     return null;
  //   }
  // }

  // // Public method to get all raw categories data
  // public getAllRawCategoriesData(): any[] {
  //   try {
  //     const rawData = localStorage.getItem('allCategoriesRawData');
  //     if (rawData) {
  //       return JSON.parse(rawData);
  //     }
  //     return [];
  //   } catch (error) {
  //     console.error('Error retrieving raw categories data:', error);
  //     return [];
  //   }
  // }

  // // Public method to clear all stored data
  // public clearAllStoredData(): void {
  //   localStorage.removeItem('selectedCategoryComplete');
  //   localStorage.removeItem('allCategoriesRawData');
  //   console.log('All stored category data cleared');
  // }
}