import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';



export interface INewsDetailsWebPartProps {
  description: string;
}
export interface IBreadcrumbItem {
  title: string;
  url?: string;
  isCurrent?: boolean;
}
export default class NewsDetailsWebPart extends BaseClientSideWebPart<INewsDetailsWebPartProps> {
  _isArabic: boolean = false;
  newsItems: {
    ID: number;
    Title: string;
    Title_AR: string;
    NewsDate: string;
    ViewCount: number;
    Image: string;
    Content: string;
    Content_AR: string;
    attachments: string[]; // Add this property to hold attachments
  }[] = [];

  currentNews: {
    ID: number;
    Title: string;
    Title_AR: string;
    NewsDate: string;
    ViewCount: number;
    Image: string;
    Content: string;
    Content_AR: string;
    alt_EN: string;
    alt_AR: string,
  } | null = null;

  attachments: string[] = [];

  private getListEndpoint(listTitle: string, selectFields?: string, expandFields?: string, filterQuery?: string, orderQuery?: string, topQuery?: string, forceMainSite?: boolean): string {
    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    const siteUrl = this.context.pageContext.site.absoluteUrl;
    const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    // لتفاصيل الأخبار: نقرأ من main site والـ subsites
    const baseUrl = forceMainSite
      ? `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
      : (isSubSite
        ? `${currentWebUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
        : `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`);

    // بناء query parameters
    const queryParams: string[] = [];

    if (selectFields) {
      queryParams.push(`$select=${selectFields}`);
    }

    if (expandFields) {
      queryParams.push(`$expand=${expandFields}`);
    }

    if (filterQuery) {
      queryParams.push(filterQuery);
    }

    if (orderQuery) {
      queryParams.push(orderQuery);
    }

    if (topQuery) {
      queryParams.push(topQuery);
    }

    

    return queryParams.length > 0
      ? `${baseUrl}?${queryParams.join('&')}`
      : baseUrl;
  }

  fetchRecentNews(): Promise<void> {

    const listName = 'News Articles';
    const selectFields = '*';
    const orderQuery = '$orderby=NewsDate desc';
    const topQuery = '$top=3';

    const url = this.getListEndpoint(listName, selectFields, undefined, undefined, orderQuery, topQuery);

   
    return this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => response.json())
      .then((data) => {
        this.newsItems = data.value.map((item: any) => ({
          ID: item.ID,
          Title: item.Title,
          Title_AR: item.Title_AR,
          NewsDate: item.NewsDate,
          ViewCount: item.ViewCount || 0,
          Image: item.Image || '',  // Use the Image field directly
          Content: item.NewsBody_EN || '',
          Content_AR: item.NewsBody_AR || '',
          alt_EN: item.alt_EN || '',
          alt_AR: item.alt_AR || '',
          attachments: [] // Initialize attachments array for each news item
        }));
        // Now, fetch the attachments for each news item
        return Promise.all(this.newsItems.map(item => this.getAttachments(item.ID)));
      }).then((attachmentsArray) => {
        // Now we associate each item with its attachments
        this.newsItems = this.newsItems.map((item, index) => {
          item.attachments = attachmentsArray[index]; // Add the attachments to each item
          return item;
        });
        this.render();  // Now render the news with attachments
      }).catch(error => {
        console.error('Error fetching recent news:', error);
      });
  }

  getAttachments(itemId: number): Promise<string[]> {
    const listName = "News Articles";

    // استخدام نفس الـ pattern للقراءة من main site والـ subsites
    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    const siteUrl = this.context.pageContext.site.absoluteUrl;
    const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    const baseUrl = isSubSite
      ? `${currentWebUrl}/_api/web/lists/getbytitle('${listName}')/items(${itemId})/AttachmentFiles`
      : `${siteUrl}/_api/web/lists/getbytitle('${listName}')/items(${itemId})/AttachmentFiles`;


    return this.context.spHttpClient.get(baseUrl, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        return data.value.map((file: any) => {
          return `${file.ServerRelativeUrl}`;
        });
      })
      .catch(error => {
        console.error('Error fetching attachments:', error);
        return [];
      });
  }

  fetchNewsDetails(newsId: number): Promise<void> {

    const listName = 'News Articles';
    const selectFields = '*';
    const filterQuery = `$filter=ID eq ${newsId}`;

    const url = this.getListEndpoint(listName, selectFields, undefined, filterQuery);

    
    return this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => response.json())
      .then((data) => {
        if (data.value.length > 0) {
          const news = data.value[0];
          return this.getAttachments(news.ID).then((attachments) => {
            this.attachments = attachments;

            let imageUrl = '';
            const bannerImageData = news?.Image ? JSON.parse(news.Image) : null;

            if (bannerImageData?.serverUrl && bannerImageData?.serverRelativeUrl) {
              imageUrl = bannerImageData.serverUrl + bannerImageData.serverRelativeUrl;
            } else if (this.attachments.length > 0) {
              imageUrl = this.attachments[0];
            } else {
              imageUrl = require('./assets/img.jpg');
            }

            this.currentNews = {
              ID: news.ID,
              Title: news.Title,
              Title_AR: news.Title_AR,
              NewsDate: news.NewsDate,
              ViewCount: news.ViewCount || 0,
              Image: imageUrl,
              Content: news.NewsBody_EN || '',
              Content_AR: news.NewsBody_AR || '',
              alt_EN: news.alt_EN || '',
              alt_AR: news.alt_AR || '',
            };
            this.render();
          });
        }
      }).catch(error => {
        console.error('Error fetching news details:', error);
      });
  }

  onInit(): Promise<void> {
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;

    const urlParams = new URLSearchParams(window.location.search);
    const newsId = urlParams.get('newsid');

    return Promise.all([
      this.fetchNewsDetails(Number(newsId)),
      this.fetchRecentNews()  // Ensure recent news is fetched properly
    ]).then(() => {
      this.render();
    }).catch(error => {
      console.error('Error fetching news:', error);
    });
  }

  render(): void {

    if (this.currentNews) {
     

      
      

      

      // const referrerName = document.title
      this.domElement.innerHTML = `
      
      
        <div class="row news-details">
    <div class="col-lg-8">
        <article class="news-article card card-color section-card mx-2 p-4 rounded-3 shadow-sm">
            <header class="mb-4">
                <h1 class="head-color">${this._isArabic ? this.currentNews.Title_AR : this.currentNews.Title}</h1>
                <hr>
                <div class="meta d-flex align-items-center gap-3 text-color">
                    <div>
                        <span><i class="bi bi-calendar main-color"></i>
                            ${this.formatDateSimple(this.currentNews.NewsDate)}</span>
                        <span><i class="bi bi-eye main-color"></i> ${this.currentNews.ViewCount}</span>
                    </div>
                    <div class="dropdown ms-auto">
                        <a class="text-decoration-none text-color d-flex align-items-center" href="#" id="shareDropdown"
                            role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-share main-color me-1"></i> ${this._isArabic ? 'مشاركة' : 'Share'}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="shareDropdown">
                            <li>
                                <a class="dropdown-item d-flex align-items-center gap-2" href="#" id="sendEmail">
                                    <i class="bi bi-send main-color"></i> ${this._isArabic ? 'إرسال' : 'Send'}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center gap-2" href="#" id="copyLink">
                                    <i class="bi bi-clipboard main-color"></i> ${this._isArabic ? 'نسخ الرابط' : 'Copy Link'}
                                </a>
                        </ul>
                    </div>
                </div>

            </header>

            <!-- Image / Carousel for Attachments -->
            <figure class="mb-4">
                ${this.attachments.length > 1 ? `
                <div id="newsCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        ${this.attachments.map((img, i) => `
                        <div class="carousel-item ${i === 0 ? 'active' : ''}">
                            <img src="${img}" class="d-block w-100 rounded" alt="News image ${i + 1}">
                        </div>
                        `).join('')}
                    </div>
                    <button class="carousel-control-prev" type="button" data-bs-target="#newsCarousel"
                        data-bs-slide="prev">
                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Previous</span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#newsCarousel"
                        data-bs-slide="next">
                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Next</span>
                    </button>
                </div>
                ` : `
                <img src="${this.currentNews.Image}" alt="${this._isArabic ? this.currentNews.alt_AR || '' : this.currentNews.alt_EN || ''}" class="img-fluid rounded">
                `}
            </figure>

            <div class="article-content text-color">
                <p>${this._isArabic ? this.currentNews.Content_AR : this.currentNews.Content}</p>
            </div>
        </article>
    </div>

    <!-- Recent News Section -->
    <div class="col-lg-4">
        <aside class="p-4 rounded-3 shadow-sm section-card card card-color mx-2">
            <h2 class="head-color h5 mb-4 fw-bold tittle head-color ">${this._isArabic ? 'أخبار حديثة' : 'Recent News'}</h2>
            <div class="recent-news">
                ${this.newsItems.map(item => `
                <a href="/sites/intranet-qm/SitePages/NewsDetails.aspx?newsid=${item.ID}"
                    class="text-decoration-none mb-3 d-flex align-items-start gap-3">
                    <!-- Check if there are attachments for each news item -->
                    <img src="${item.attachments.length > 0 ? item.attachments[0] : require('./assets/img.jpg')}"
                        alt="News thumbnail" class="rounded" width="80">
                    <div>
                        <h3 class="h6 head-color">${this._isArabic ? item.Title_AR : item.Title}</h3>
                        <small class="text-color">${this.formatDateSimple(item.NewsDate)}</small>
                    </div>
                </a>
                `).join('')}
            </div>
        </aside>
    </div>
</div>
      `;

      this.addEventListeners();
    } else {
      this.domElement.innerHTML = `<div>خبر غير موجود</div>`;
    }
  }

  formatDateSimple(date: string): string {
    const parsedDate = new Date(date);
    const year = parsedDate.getFullYear();
    const month = (parsedDate.getMonth() + 1).toString();
    const day = parsedDate.getDate().toString();
    return `${year}-${month}-${day}`;
  }

  addEventListeners(): void {
    const sendEmailBtn = this.domElement.querySelector('#sendEmail');
    const copyLinkBtn = this.domElement.querySelector('#copyLink');

    if (sendEmailBtn) {
      sendEmailBtn.addEventListener('click', () => this.sendEmail(window.location.href));
    }

    if (copyLinkBtn) {
      copyLinkBtn.addEventListener('click', () => this.copyLink(window.location.href));
    }
  }

  copyLink(url: string): void {
    navigator.clipboard.writeText(url).then(() => {
    }).catch((error) => {
      console.error('Error copying link:', error);
    });
  }

  sendEmail(url: string): void {
    const subject = "Check this out!";
    const body = `Here is the link to the news article: ${url}`;
    window.location.href = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  }
  async getPageTitle(serverRelativeUrl: string): Promise<string> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/getfilebyserverrelativeurl('${serverRelativeUrl}')/ListItemAllFields?$select=Title`;

    const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
    const data = await response.json();

    return data?.Title || '';
  }
  generateDynamicBreadcrumb(): IBreadcrumbItem[] {
    const path = window.location.pathname;
    const segments = path.split('/').filter(seg => seg); // Remove empty strings
    const breadcrumbItems: IBreadcrumbItem[] = [];

    let accumulatedUrl = '';

    segments.forEach((segment, index) => {
      accumulatedUrl += `/${segment}`;

      const skipList = ['sitepages', 'pages', 'sites'];
      if (skipList.indexOf(segment.toLowerCase()) !== -1) return;

      const nameMap: { [key: string]: string } = {
        'news': 'News',
        'newsdetails': 'News Details',
      };

      const formattedTitle = nameMap[segment.toLowerCase()] ||
        decodeURIComponent(segment)
          .replace(/[-_]/g, ' ')
          .replace(/\.aspx/i, '')
          .replace(/\b\w/g, c => c.toUpperCase());

      if (index === segments.length - 1) {
        breadcrumbItems.push({
          title: formattedTitle,
          isCurrent: true
        });
      } else {
        breadcrumbItems.push({
          title: formattedTitle,
          url: accumulatedUrl
        });
      }
    });




    return breadcrumbItems;
  }
  generateBreadcrumbHtml(items: IBreadcrumbItem[]): string {
    return `
      <ol class="breadcrumb m-0">
        ${items
        .map(item => {
          if (item.isCurrent) {
            return `<li class="breadcrumb-item active" aria-current="page">${item.title}</li>`;
          } else {
            return `<li class="breadcrumb-item"><a href="${item.url}" class="btn-main-link fw-bold">${item.title}</a></li>`;
          }
        })
        .join('')}
      </ol>
    `;
  }
}
