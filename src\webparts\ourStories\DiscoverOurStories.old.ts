
import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';



import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';

export interface IOurStoriesWebPartProps {
  description: string;
}

export default class OurStoriesWebPart extends BaseClientSideWebPart<IOurStoriesWebPartProps> {
  private _isArabic: boolean = false;


  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;

    const viewAllLink = this._isArabic
      ? "/sites/intranet-qm/SitePages/ar/OurStories.aspx"
      : "/sites/intranet-qm/SitePages/OurStories.aspx";

    this.domElement.innerHTML = `
      <div class="col-lg-12">
        <section class="stories-section h-100">
          <div class="card section-card card-color h-100 p-3">
            <div class="container-fluid">
              <h2 class="fw-bold tittle head-color">${this._isArabic ? "اكتشف قصصنا" : "Discover Our Stories"}</h2>
              <div class="d-flex flex-column" id="stories-container">
                <p>${this._isArabic ? "جارٍ تحميل القصص..." : "Loading stories..."}</p>
              </div>
              <div class="${this._isArabic ? "text-start" : "text-end"} mt-3 card-footer">
                <a href="${viewAllLink}" class="text-decoration-none fw-bold btn-main-link">
                  ${this._isArabic ? "عرض المزيد "  + '<i class="bi bi-arrow-left-circle"></i>' : "View All " + '<i class="bi bi-arrow-right-circle"></i>'}
                </a>
              </div>
            </div>
          </div>
        </section>
      </div>
    `;

    this._getStories();
  }

  private _getStories(): void {
    const listUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Our%20Stories')/items?$filter=Active eq '1'&$top=4`;


    this.context.spHttpClient.get(listUrl, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => response.json())
      .then((data: any) => {
        const items = data.value;
        let html = '';

        items.forEach((item: any) => {
          const title = this._isArabic ? (item.Title_AR || '') : item.Title || '';
          const link = this._isArabic ? item?.Link_AR?.Url || '#' : item?.Link_EN?.Url || '#';
          html += `
            <a href="${link}" class="text-decoration-none my-2">
              <span class="head-color fw-bold mt-3 main-color-hover">
                ${title}
              </span>
            </a>
          `;
        });

        const container = this.domElement.querySelector('#stories-container');
        if (container) {
          container.innerHTML = html;
        }

        const heading = this.domElement.querySelector('h2');
        if (heading) {
          heading.textContent = this._isArabic ? "اكتشف قصصنا" : "Discover Our Stories";
          if (this._isArabic) heading.classList.add("text-end");
        }
      })
      .catch(error => {
        console.error("Error fetching stories:", error);
        const container = this.domElement.querySelector('#stories-container');
        if (container) {
          container.innerHTML = `<p class="text-danger">${this._isArabic ? "فشل في تحميل القصص." : "Failed to load stories."}</p>`;
        }
      });
  }




}