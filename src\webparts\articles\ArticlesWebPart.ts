import { Version } from '@microsoft/sp-core-library';
import {
  type IPropertyPaneConfiguration,
  PropertyPaneTextField
} from '@microsoft/sp-property-pane';
import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';

import * as strings from 'ArticlesWebPartStrings';

export interface IArticlesWebPartProps {
  description: string;
}

export interface IArticleItem {
  Id: number;
  Title: string;
  Title_AR?: string;
  NewsDate: string;
  ViewCount?: number;
  Image?: string;
  NewsBody_EN?: string;
  NewsBody_AR?: string;
  ShowOnHomepage?: boolean;
  IsFeatured?: boolean;
}

export default class ArticlesWebPart extends BaseClientSideWebPart<IArticlesWebPartProps> {
  private _articleItems: IArticleItem[] = [];
  private _isArabic: boolean = false;
  private _isLoading: boolean = false;
  private _currentSiteUrl: string = '';

  protected async onInit(): Promise<void> {
    await super.onInit();
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;

    this._currentSiteUrl = this._getCurrentSiteUrl();

    await this._loadArticles();
  }

  private _getCurrentSiteUrl(): string {
    try {
      const currentUrl = window.location.pathname.toLowerCase();

      // إذا كان في الموقع الرئيسي
      if (currentUrl.includes('/sites/intranet-qm/sitepages/') && !this._isInSubsite(currentUrl)) {
        return '/sites/intranet-qm';
      }

      // إذا كان في subsite
      const subsiteMatch = currentUrl.match(/\/sites\/intranet-qm\/([^\/]+)\//);
      if (subsiteMatch && subsiteMatch[1] !== 'sitepages') {
        const subsitePath = `/sites/intranet-qm/${subsiteMatch[1]}`;
        return subsitePath;
      }

      // افتراضي: الموقع الرئيسي
      return '/sites/intranet-qm';
    } catch (error) {
      console.error('Articles - Error determining site URL:', error);
      return '/sites/intranet-qm';
    }
  }

  private _isInSubsite(url: string): boolean {
    // تحقق من وجود subsite في الـ URL
    const subsitePattern = /\/sites\/intranet-qm\/([^\/]+)\/sitepages\//i;
    const match = url.match(subsitePattern);

    if (match && match[1]) {
      const potentialSubsite = match[1].toLowerCase();
      // تجاهل 'ar' لأنه language code وليس subsite
      return potentialSubsite !== 'ar';
    }

    return false;
  }

  private getListEndpoint(listTitle: string, selectFields?: string, expandFields?: string, filterQuery?: string, orderQuery?: string, topQuery?: string, forceMainSite?: boolean): string {
    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    const siteUrl = this.context.pageContext.site.absoluteUrl;
    const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    // للمقالات: نقرأ من main site والـ subsites
    const baseUrl = forceMainSite
      ? `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
      : (isSubSite
        ? `${currentWebUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
        : `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`);

    // بناء query parameters
    const queryParams: string[] = [];

    if (selectFields) {
      queryParams.push(`$select=${selectFields}`);
    }

    if (expandFields) {
      queryParams.push(`$expand=${expandFields}`);
    }

    if (filterQuery) {
      queryParams.push(filterQuery);
    }

    if (orderQuery) {
      queryParams.push(orderQuery);
    }

    if (topQuery) {
      queryParams.push(topQuery);
    }


    return queryParams.length > 0
      ? `${baseUrl}?${queryParams.join('&')}`
      : baseUrl;
  }

  private async _loadArticles(): Promise<void> {
    try {
      this._isLoading = true;
      this.render();


      const listName = 'News Articles';
      const selectFields = 'Id,Title,Title_AR,NewsDate,ViewCount,Image,NewsBody_EN,NewsBody_AR,ShowOnHomepage,IsFeatured';
      const orderQuery = '$orderby=NewsDate desc';
      const topQuery = '$top=5';

      const url = this.getListEndpoint(listName, selectFields, undefined, undefined, orderQuery, topQuery);


      const response: SPHttpClientResponse = await this.context.spHttpClient.get(
        url,
        SPHttpClient.configurations.v1
      );

      if (response.ok) {
        const data = await response.json();
        this._articleItems = data.value || [];
      } else {
        console.error('Articles - Failed to load items:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Articles - Error loading items:', error);
    } finally {
      this._isLoading = false;
      this.render();
    }
    
  }

  public render(): void {
    const loadingText = this._isArabic ? 'جاري التحميل...' : 'Loading...';
    const noArticlesText = this._isArabic ? 'لا توجد مقالات' : 'No articles found';
    const articlesTitle = this._isArabic ? 'المقالات' : 'Articles';

    this.domElement.innerHTML = `
      <div class="articles-section">
        <div class="card section-card card-color pt-2 mt-3 bg-remover">
          <div class="container-fluid">
            <h2 class="fw-bold tittle head-color">${articlesTitle}</h2>
            ${this._isLoading ? `
              <div class="loading-container text-center py-5">
                <div class="spinner-border" role="status"></div>
                <p class="mt-3">${loadingText}</p>
              </div>
            ` : this._renderArticles(noArticlesText)}
          </div>
        </div>
      </div>
    `;
  }

  private _renderArticles(noArticlesText: string): string {
    if (!this._articleItems || this._articleItems.length === 0) {
      return `
        <div class="no-articles text-center py-5">
          <i class="bi bi-file-earmark-text display-1 text-color"></i>
          <h4 class="mt-3 head-color">${noArticlesText}</h4>
          <p class="text-color">${this._isArabic ? 'لا توجد مقالات متاحة في الوقت الحالي.' : 'No articles available at the moment.'}</p>
        </div>
      `;
    }

    return `
      <div class="articles-list">
        ${this._articleItems.map((article) => this._renderArticleCard(article)).join('')}
        <div class="text-end mt-3 card-footer">
            <a href="${this._currentSiteUrl}/SitePages/News.aspx" class="text-decoration-none fw-bold  btn-main-link">${this._isArabic ? 'عرض الكل' : 'View All'} →</a>
        </div>
      </div>

    `;
  }

  private _renderArticleCard(article: IArticleItem): string {
    const title = this._isArabic ? (article.Title_AR || article.Title) : article.Title;

    // Handle image
    let imageUrl = '';
    if (article.Image) {
      try {
        const imageData = JSON.parse(article.Image);
        if (imageData?.serverUrl && imageData?.serverRelativeUrl) {
          imageUrl = imageData.serverUrl + imageData.serverRelativeUrl;
        }
      } catch (e) {
        console.warn('Invalid Image JSON format:', article.Image);
      }
    }

    if (!imageUrl) {
      imageUrl = require('./assets/welcome-light.png'); // Default image
    }

    return `
      
          <a href="${this._currentSiteUrl}/SitePages/NewsDetails.aspx?newsid=${article.Id}" class="article-item d-flex my-2 text-decoration-none">
            <div class="mx-2">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="25" height="25" viewBox="0 0 25 25">
                <defs>
                  <clipPath id="clip-path">
                    <rect id="Rectangle_6580" data-name="Rectangle 6580" width="25" height="25" fill="#fff"></rect>
                  </clipPath>
                </defs>
                <g id="article-svgrepo-com" clip-path="url(#clip-path)">
                  <path id="Path_78321" data-name="Path 78321" d="M5.682,19.73V1.784A.767.767,0,0,1,6.463,1H23.632a.769.769,0,0,1,.78.784v20.29a2.29,2.29,0,0,1-2.341,2.337H3.341S1,24.416,1,21.294V9.588A.767.767,0,0,1,1.78,8.8H3.341m7.024-3.122h3.9M10.365,8.8H19.73m-9.365,3.122H19.73m-9.365,3.122H19.73m-9.365,3.122H19.73" transform="translate(-0.22 -0.22)" fill="none" stroke="var(--head-color)" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
                </g>
              </svg>

            </div>

            <h5 class="text-color">${title}</h5>

          </a>
          
    
    `;
  }

 

  protected get dataVersion(): Version {
    return Version.parse('1.0');
  }

  protected getPropertyPaneConfiguration(): IPropertyPaneConfiguration {
    return {
      pages: [
        {
          header: {
            description: strings.PropertyPaneDescription
          },
          groups: [
            {
              groupName: strings.BasicGroupName,
              groupFields: [
                PropertyPaneTextField('description', {
                  label: strings.DescriptionFieldLabel
                })
              ]
            }
          ]
        }
      ]
    };
  }
}
