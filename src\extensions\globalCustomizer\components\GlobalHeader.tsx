import React, { useState, useEffect, useRef } from 'react';


import { BaseComponentContext } from '@microsoft/sp-component-base';
interface IHeaderProps {
  description: string;
  context : BaseComponentContext
}

interface MenuItem {
  Id: number;
  ParentId?: number;
  Title: string;
  Title_AR?: string;
  LinkUrl?: string;
  LinkUrl_AR?: string;
  GroupName?: string;
  GroupName_AR?: string;
  order0?: number;
}

const HeaderWebPart: React.FC<IHeaderProps> = ({ description , context  }) => {
  const [isArabic, setIsArabic] = useState<boolean>(false);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [notifications] = useState<number>(2);
  const mainMenuRef = useRef<HTMLDivElement>(null);
  const menuToggleRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const path = window.location.pathname.toLowerCase();
    setIsArabic(path.includes('/ar/'));
    loadMenuFromList('MainMenu', context);
    setupEventListeners();

    return () => {
      // Cleanup event listeners
      document.removeEventListener('click', handleDocumentClick);
    };
  }, []);

  useEffect(() => {
    // Reinitialize dropdowns when menu items change
    initializeDropdowns();
  }, [menuItems]);

  const setupEventListeners = () => {
    if (menuToggleRef.current && mainMenuRef.current) {
      menuToggleRef.current.addEventListener('click', toggleMobileMenu);
    }
    
    document.addEventListener('click', handleDocumentClick);
  };

  const toggleMobileMenu = (e: Event) => {
    e.preventDefault();
    mainMenuRef.current?.classList.toggle('active');
  };

  const closeMobileMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    mainMenuRef.current?.classList.remove('active');
  };

  const handleDocumentClick = (e: MouseEvent) => {
    const target = e.target as HTMLElement;
    
    if (mainMenuRef.current && !mainMenuRef.current.contains(target) && 
        menuToggleRef.current && !menuToggleRef.current.contains(target)) {
      mainMenuRef.current.classList.remove('active');
    }
  };

  const initializeDropdowns = () => {
    // This replicates the _toggleDropdownManually functionality
    const toggles = document.querySelectorAll('.dropdown-toggle');
    
    toggles.forEach(toggle => {
      toggle.addEventListener('click', (e) => {
        e.preventDefault();
        
        // Close other dropdowns
        document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
          if (menu !== (toggle as HTMLElement).nextElementSibling) {
            menu.classList.remove('show');
          }
        });
        
        // Toggle current dropdown
        const dropdownMenu = (toggle as HTMLElement).nextElementSibling as HTMLElement;
        dropdownMenu?.classList.toggle('show');
      });
    });
  };
 

  const handleLanguageToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    const path = window.location.pathname;
    const newPath = isArabic
      ? path.replace(/\/sitepages\/ar\//i, '/SitePages/')
      : path.replace(/\/sitepages\//i, '/SitePages/ar/');
    const newUrl = `${window.location.origin}${newPath}${window.location.search}${window.location.hash}`;
    window.location.href = newUrl;
  };

  async function loadMenuFromList(listTitle: string = 'MainMenu', context: BaseComponentContext) {

    try {
      const currentWebUrl = context.pageContext.web.absoluteUrl;
      const siteUrl = context.pageContext.site.absoluteUrl;
      const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();
  
      const endpoint = isSubSite
        ? `${currentWebUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
        : `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`;
  
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Accept': 'application/json;odata=nometadata',
        },
      });
  
      const data = await response.json();
      const items: MenuItem[] = data.value || [];
  
      if (items.length) {
        items.sort((a, b) => (a.order0 ?? 0) - (b.order0 ?? 0));
        setMenuItems(items);
      }
    } catch (error) {
      console.error('Error loading menu:', error);
    }
  };
  

  const renderMenu = () => {
    const parents = menuItems.filter(item => !item.ParentId);
    const children = menuItems.filter(item => item.ParentId);

    // Create children map
    const childrenMap = children.reduce<Record<number, MenuItem[]>>((acc, child) => {
      if (!acc[child.ParentId!]) acc[child.ParentId!] = [];
      acc[child.ParentId!].push(child);
      return acc;
    }, {});

    return parents.map((parent) => {
      const parentTitle = isArabic ? parent.Title_AR || '' : parent.Title || '';
      const parentChildren = childrenMap[parent.Id] || [];
      const hasChildren = parentChildren.length > 0;

      return (
        <li className={hasChildren ? 'nav-item dropdown' : 'nav-item'} key={parent.Id}>
          <a
            className={`nav-link ${hasChildren ? 'dropdown-toggle' : ''}`}
            href={hasChildren ? '#' : (isArabic ? parent.LinkUrl_AR : parent.LinkUrl) || '#'}
            role="button"
          >
            {parentTitle}
          </a>
          {hasChildren && renderDropdown(parentChildren)}
        </li>
      );
    });
  };

  const renderDropdown = (children: MenuItem[]) => {
    // Group children by their group name
    const groupedChildren = children.reduce<Record<string, MenuItem[]>>((acc, child) => {
      const group = isArabic ? child.GroupName_AR || '' : child.GroupName || '';
      if (!acc[group]) acc[group] = [];
      acc[group].push(child);
      return acc;
    }, {});

    return (
      <ul className="dropdown-menu mega-menu">
        <div className="container">
          <div className="row">
            {Object.entries(groupedChildren).map(([group, items]) => (
              <div className="col-md-4" key={group}>
                <h6 className="text-uppercase fw-bold mb-3">{group}</h6>
                <ul className="list-unstyled">
                  {items.map((child) => (
                    <li key={child.Id}>
                      <a 
                        href={isArabic ? child.LinkUrl_AR || '' : child.LinkUrl || ''} 
                        className="dropdown-item"
                      >
                        {isArabic ? child.Title_AR || '' : child.Title || ''}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </ul>
    );
  };

  const placeholderText = isArabic ? 'ابحث هنا...' : 'Search...';
  const langText = isArabic ? 'English' : 'العربية';

  return (
    <header className="py-2 border-bottom">
      <div className="container-fluid">
        <div className="d-flex align-items-center justify-content-between flex-wrap">
          {/* Logo */}
          <div className="logo-container d-flex align-items-center">
            <a href="/" className="d-flex align-items-center text-decoration-none">
              <img className="logo-b" src={require('../../../webparts/header/assets/logo.svg')} alt="Logo" height="50" />
              <img className="logo-w" src={require('../../../webparts/header/assets/logo-w.svg')} alt="Logo" height="50" />
            </a>
          </div>

          {/* Navbar */}
          <nav className="navbar navbar-expand-lg">
            <div className="container-fluid">
              <button 
                className="navbar-toggler d-lg-none" 
                id="menuToggle" 
                type="button"
                ref={menuToggleRef}
              >
                <i className="bi bi-list fs-2"></i>
              </button>
              <div className="responsive-menu" id="mainMenu" ref={mainMenuRef}>
                <div className="d-flex justify-content-end d-lg-none">
                  <button 
                    type="button" 
                    id="closeMenuBtn" 
                    className="btn btn-outline-secondary btn-sm mb-3"
                    onClick={closeMobileMenu}
                  >
                    <i className="bi bi-x-lg"></i>
                  </button>
                </div>
                <ul className="navbar-nav me-auto mb-2 mb-lg-0">
                  {renderMenu()}
                </ul>
              </div>
            </div>
          </nav>

          {/* Right Side */}
          <div className="d-flex align-items-center wrap-checker">
            {/* Search */}
            <form className="d-flex me-3 position-relative d-none" id="searchForm">
              <input 
                className="form-control" 
                type="search" 
                id="searchInput" 
                placeholder={placeholderText} 
                aria-label="Search" 
              />
              <button className="btn position-absolute end-0 top-0 bottom-0 btn-main" type="submit">
                <i className="bi bi-search icon-white"></i>
              </button>
            </form>

            {/* Notifications */}
            <div className="dropdown position-relative me-3 notifi">
              <a href="#" className="text-decoration-none position-relative" data-bs-toggle="dropdown">
                <i className="bi bi-bell btn-main-link icon-md main-color"></i>
                <span className="position-absolute top-0 start-100 translate-top badge rounded-pill bg-danger">
                  {notifications}
                </span>
              </a>
              <div className="dropdown-menu dropdown-menu-end p-3 shadow" style={{ minWidth: '320px', maxWidth: '360px' }}>
                {/* Notifications content */}
              </div>
            </div>

            {/* Language Switch */}
            <div className="lang-toggle">
              <a
                href="#"
                id="langToggleBtn"
                className="btn btn-sm btn-main-link d-flex"
                onClick={handleLanguageToggle}
              >
                {langText} <i className="bi bi-globe icon-md mx-1 main-color"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default HeaderWebPart;