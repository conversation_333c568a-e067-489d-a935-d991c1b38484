
import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';

export interface INewsletterItem {
  Id: number;
  Title: string;
  Language?: string;
  FileRef?: string;
  FileLeafRef?: string;
  Created?: string;
  Modified?: string;
  File?: {
    ServerRelativeUrl: string;
    Name: string;
  };
  Author?: {
    Title: string;
  };
}

export interface INewsletterWebPartProps {
  description: string;
}

export default class NewsletterWebPart extends BaseClientSideWebPart<INewsletterWebPartProps> {
  private _newsletterItems: INewsletterItem[] = [];
  private _isArabic: boolean = false;
  private _isLoading: boolean = false;
  private _currentSiteUrl: string = '';

  public async onInit(): Promise<void> {
    await super.onInit();

    this._isArabic = this.context.pageContext.cultureInfo.currentCultureName.startsWith('ar');

    // تحديد الـ site URL الحالي للـ links
    this._currentSiteUrl = this._getCurrentSiteUrl();

    await this._loadNewsletters();
  }

  private _getCurrentSiteUrl(): string {
    try {
      const currentUrl = window.location.pathname.toLowerCase();

      // إذا كان في الموقع الرئيسي
      if (currentUrl.includes('/sites/intranet-qm/sitepages/') && !this._isInSubsite(currentUrl)) {
        return '/sites/intranet-qm';
      }

      // إذا كان في subsite
      const subsiteMatch = currentUrl.match(/\/sites\/intranet-qm\/([^\/]+)\//);
      if (subsiteMatch && subsiteMatch[1] !== 'sitepages') {
        const subsitePath = `/sites/intranet-qm/${subsiteMatch[1]}`;
        return subsitePath;
      }

      // افتراضي: الموقع الرئيسي
      return '/sites/intranet-qm';
    } catch (error) {
      console.error('Newsletter - Error determining site URL:', error);
      return '/sites/intranet-qm';
    }
  }

  private _isInSubsite(url: string): boolean {
    // تحقق من وجود subsite في الـ URL
    const subsitePattern = /\/sites\/intranet-qm\/([^\/]+)\/sitepages\//i;
    const match = url.match(subsitePattern);

    if (match && match[1]) {
      const potentialSubsite = match[1].toLowerCase();
      // تجاهل 'ar' لأنه language code وليس subsite
      return potentialSubsite !== 'ar';
    }

    return false;
  }

  public render(): void {
    const loadingText = this._isArabic ? 'جاري التحميل...' : 'Loading...';
    const noNewslettersText = this._isArabic ? 'لا توجد نشرات إخبارية' : 'No newsletters found';
    const errorText = this._isArabic ? 'حدث خطأ أثناء تحميل البيانات' : 'Error loading data';

    this.domElement.innerHTML = `
      <div class="card section-card card-color pt-2  bg-remover newsletter-card">
      <div class="container-fluid ">
      <h2 class="fw-bold tittle head-color ">${this._isArabic ? 'النشرات الإخبارية' : 'Newsletters'}</h2>
        ${this._isLoading ? `
          <div class="loading-container">
            <div class="spinner-border" role="status"></div>
            <p class="mt-3">${loadingText}</p>
          </div>
        ` : this._renderNewsletters(noNewslettersText, errorText)}
      </div>
      </div>
    `;

    if (!this._isLoading && this._newsletterItems && this._newsletterItems.length > 0) {
      this._attachEventListeners();
    }
  }

  private getDocumentLibraryEndpoint(libraryTitle: string, selectFields?: string, expandFields?: string, filterQuery?: string, orderQuery?: string, topQuery?: string): string {
    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    // const siteUrl = this.context.pageContext.site.absoluteUrl;
    // const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    const baseUrl = `${currentWebUrl}/_api/web/lists/getbytitle('${libraryTitle}')/items`;

    const queryParams: string[] = [];

    if (selectFields) {
      queryParams.push(`$select=${selectFields}`);
    }

    if (expandFields) {
      queryParams.push(`$expand=${expandFields}`);
    }

    if (filterQuery) {
      queryParams.push(filterQuery);
    }

    if (orderQuery) {
      queryParams.push(orderQuery);
    }

    if (topQuery) {
      queryParams.push(topQuery);
    }

    ;

    return queryParams.length > 0
      ? `${baseUrl}?${queryParams.join('&')}`
      : baseUrl;
  }

  private async _loadNewsletters(): Promise<void> {
    try {
      this._isLoading = true;
      this.render();


      const listName = 'News Letter';
      const selectFields = 'Id,Title,Language,FileRef,FileLeafRef,Created,Modified,File/ServerRelativeUrl,File/Name,Author/Title';
      const expandFields = 'File,Author';

      const languageFilter = this._isArabic ? "Language eq 'AR'" : "Language eq 'EN'";
      const filterQuery = `$filter=${languageFilter}`;

      const orderQuery = '$orderby=Modified desc';
      const topQuery = '$top=5'; 

      const apiUrl = this.getDocumentLibraryEndpoint(listName, selectFields, expandFields, filterQuery, orderQuery, topQuery);


      const response: SPHttpClientResponse = await this.context.spHttpClient.get(
        apiUrl,
        SPHttpClient.configurations.v1
      );

      if (response.ok) {
        const data = await response.json();
        this._newsletterItems = data.value || [];
      } else {
        console.error('Newsletter - Failed to load items:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Newsletter - Error loading items:', error);
    } finally {
      this._isLoading = false;
      this.render();
    }
  }





  private _renderNewsletters(noNewslettersText: string, errorText: string): string {
    if (!this._newsletterItems || this._newsletterItems.length === 0) {
      return `
        <div class="no-newsletters">
          <i class="bi bi-file-earmark-pdf"></i>
          <p>${noNewslettersText}</p>
          ${errorText ? `<small class="text-danger">${errorText}</small>` : ''}
        </div>
      `;
    }

    const viewAllText = this._isArabic ? 'عرض الكل' : 'View All';

    return `
      <div class="newsletter-grid">
        ${this._newsletterItems.map((newsletter) => this._renderNewsletterCard(newsletter)).join('')}
      </div>

      <div class="text-end mt-3 card-footer">
        <a href="${this._currentSiteUrl}/SitePages/Newsletters.aspx" class="text-decoration-none fw-bold  btn-main-link">
          ${viewAllText} →
        </a>
      </div>
    `;
  }

  private _renderNewsletterCard(newsletter: INewsletterItem): string {
    const title = newsletter.Title || newsletter.FileLeafRef || 'Newsletter';
    const fileUrl = newsletter.FileRef || newsletter.File?.ServerRelativeUrl || '';
    return `
    <div class="news-letter-list">
    <div class="news-letter-item py-2">
        <a href="${fileUrl}" class="text-decoration-none text-color">

            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="25" height="25"
                viewBox="0 0 25 25">
                <defs>
                    <clipPath id="clip-path">
                        <rect id="Rectangle_6580" data-name="Rectangle 6580" width="25" height="25" fill="#fff"></rect>
                    </clipPath>
                </defs>
                <g id="article-svgrepo-com" clip-path="url(#clip-path)">
                    <path id="Path_78321" data-name="Path 78321"
                        d="M5.682,19.73V1.784A.767.767,0,0,1,6.463,1H23.632a.769.769,0,0,1,.78.784v20.29a2.29,2.29,0,0,1-2.341,2.337H3.341S1,24.416,1,21.294V9.588A.767.767,0,0,1,1.78,8.8H3.341m7.024-3.122h3.9M10.365,8.8H19.73m-9.365,3.122H19.73m-9.365,3.122H19.73m-9.365,3.122H19.73"
                        transform="translate(-0.22 -0.22)" fill="none" stroke="var(--head-color)" stroke-linecap="round"
                        stroke-linejoin="round" stroke-width="2"></path>
                </g>
            </svg>


            <span>${title}</span></a>
    </div>


</div>
    `;
  }

  private _attachEventListeners(): void {
    const cards = this.domElement.querySelectorAll('.newsletter-card');

    cards.forEach((card) => {
      card.addEventListener('click', (event) => {
        event.preventDefault();

        const cardElement = event.currentTarget as HTMLElement;
        const fileUrl = cardElement.getAttribute('data-file-url');
        const newsletterId: any = cardElement.getAttribute('data-newsletter-id');

        if (fileUrl) {
          this._downloadNewsletter(fileUrl, newsletterId);
        }
      });
    });

    const viewAllLink = this.domElement.querySelector('.view-all-link');
    if (viewAllLink) {
      viewAllLink.addEventListener('click', (event) => {
        event.preventDefault();
      });
    }
  }

  private _downloadNewsletter(fileUrl: string, newsletterId?: string): void {
    try {

      const fullUrl = fileUrl.startsWith('http')
        ? fileUrl
        : `${this.context.pageContext.site.absoluteUrl}${fileUrl}`;

      window.open(fullUrl, '_blank');

    } catch (error) {
      console.error('Newsletter - Error downloading file:', error);

      const link = document.createElement('a');
      link.href = fileUrl;
      link.target = '_blank';
      link.download = '';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
}
