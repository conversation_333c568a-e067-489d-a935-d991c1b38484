# Featured Deals WebPart - Category Navigation & Cookie Management

## Overview
The Featured Deals WebPart now includes advanced category navigation with cookie-based state management and automatic subsite navigation.

## Features

### 1. Category-Based Subsite Navigation
- When users click on a category, they are automatically navigated to the appropriate subsite
- Category information is stored in cookies for persistence across page loads
- Smart mapping between category names and subsite names

### 2. Cookie Management
The WebPart uses cookies to store:
- `selectedCategoryId`: The ID of the selected category
- `selectedCategoryName`: The name of the selected category

### 3. Subsite Detection
- Automatically detects if the user is in a subsite
- Shows different UI elements based on current location
- Displays selected category information when in a subsite

## Category to Subsite Mapping

The WebPart includes intelligent mapping between categories and subsites:

### English Categories
- Information Technology → `it` subsite
- Human Resources → `hr` subsite  
- Finance/Financial/Accounting → `finance` subsite
- Marketing/Communications → `marketing` subsite
- Administration/Admin → `administration` subsite
- Legal → `legal` subsite
- Security → `security` subsite
- Facilities → `facilities` subsite
- Operations → `operations` subsite

### Arabic Categories
- تقنية المعلومات → `it` subsite
- الموارد البشرية → `hr` subsite
- المالية/المحاسبة → `finance` subsite
- التسويق/الاتصالات → `marketing` subsite
- الإدارة → `administration` subsite
- القانونية → `legal` subsite
- الأمن → `security` subsite
- المرافق → `facilities` subsite
- العمليات → `operations` subsite

## Usage

### For Users
1. **Browse Categories**: View all available categories on the main site
2. **Select Category**: Click on any category card to navigate to its subsite
3. **View Selection**: See selected category information when in a subsite
4. **Clear Selection**: Use the "Clear Selection" button to return to main site

### For Developers

#### Adding New Category Mappings
To add new category to subsite mappings, update the `_categorySubsiteMapping` object:

```typescript
private _categorySubsiteMapping: ICategorySubsiteMapping = {
  "1": "it",
  "2": "hr", 
  "3": "finance",
  // Add more mappings as needed
};
```

#### Customizing Name-Based Mapping
Update the `nameToSubsiteMapping` object in `_getCategorySubsite()` method:

```typescript
const nameToSubsiteMapping: { [key: string]: string } = {
  'new category': 'new-subsite',
  'فئة جديدة': 'new-subsite',
  // Add more mappings
};
```

## API Integration

### Categories API
- **Endpoint**: `https://mazaya-backend.qm.org.qa/api/Categories`
- **Authentication**: Bearer token required
- **Response**: Array of category objects with `id`, `name`, and `icon`

### Featured Deals API  
- **Endpoint**: `https://mazaya-backend.qm.org.qa/api/Offers/GetFeatureDeals/`
- **Authentication**: Bearer token required
- **Response**: Array of deal objects with `id` and `bannerUrl`

## UI Components

### Main Site View
- Full category grid with all available categories
- Hover effects and smooth transitions
- Click to navigate to subsite

### Subsite View
- Selected category information banner
- Highlighted selected category in grid
- "View All Categories" link to return to main site
- "Clear Selection" button

## Technical Implementation

### Cookie Functions
- `_setCookie()`: Store data with expiration
- `_getCookie()`: Retrieve stored data
- `_deleteCookie()`: Remove stored data

### Navigation Functions
- `_navigateToSubsite()`: Handle category selection and navigation
- `_getCategorySubsite()`: Map category to subsite name
- `clearCategorySelection()`: Clear selection and return to main site

### Detection Functions
- `_checkIfInSubsite()`: Detect current location
- `_getCurrentSubsiteName()`: Get current subsite name

## Styling

### CSS Classes
- `.selected-category-info`: Banner for selected category
- `.category-card`: Individual category cards
- `.border-primary`: Highlight selected category
- `.transition-hover`: Smooth hover effects

### Responsive Design
- Grid layout adapts to screen size
- Mobile-friendly touch interactions
- Accessible keyboard navigation

## Configuration

### Subsite URL Pattern
```
/sites/intranet-qm/{subsiteName}/SitePages/Home.aspx
```

### Main Site URL
```
/sites/intranet-qm/SitePages/TopicHome.aspx
```

## Troubleshooting

### Common Issues
1. **Categories not loading**: Check API authentication and network connectivity
2. **Navigation not working**: Verify subsite URLs and permissions
3. **Cookies not persisting**: Check browser settings and domain configuration

### Debug Information
The WebPart logs detailed information to browser console:
- API responses
- Category mappings
- Navigation attempts
- Cookie operations

## Future Enhancements

### Planned Features
1. Category statistics and analytics
2. Personalized category recommendations
3. Advanced filtering and search
4. Integration with SharePoint search
5. Multi-language category descriptions

### API Extensions
1. Category-specific deal filtering
2. Real-time category statistics
3. User preference tracking
4. Category popularity metrics
