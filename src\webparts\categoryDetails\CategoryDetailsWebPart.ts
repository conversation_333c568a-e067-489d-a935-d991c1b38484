import { BaseClientSideWebPart } from "@microsoft/sp-webpart-base";

export interface ICategoryDetailsWebPartProps {
  description: string;
}

export default class CategoryDetailsWebPart extends BaseClientSideWebPart<ICategoryDetailsWebPartProps> {
  private _isArabic: boolean = false;

  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().includes("/ar/");

    // الحصول على البيانات الكاملة من localStorage
    const completeCategoryData = this.getCompleteCategoryData();
    // const allCategoriesData = this.getAllCategoriesData();
    
    this.domElement.innerHTML = `
      <style>
        .category-details {
          padding: 20px;
          background: #f8f9fa;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        .complete-data {
          background: white;
          padding: 20px;
          border-radius: 10px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          margin: 20px 0;
          border: 2px solid #007bff;
        }
        .data-field {
          margin: 10px 0;
          padding: 8px;
          background: #f8f9fa;
          border-radius: 5px;
          border-left: 4px solid #007bff;
        }
        .field-label {
          font-weight: bold;
          color: #495057;
          display: inline-block;
          width: 200px;
        }
        .field-value {
          color: #007bff;
        }
        .array-data {
          background: #e7f3ff;
          padding: 10px;
          border-radius: 5px;
          margin-top: 5px;
        }
        .no-data {
          text-align: center;
          padding: 40px;
          color: #666;
        }
        .btn {
          padding: 10px 20px;
          border-radius: 5px;
          cursor: pointer;
          margin: 5px;
          border: none;
          font-weight: bold;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.8; }
        .category-icon {
          width: 60px;
          height: 60px;
          object-fit: contain;
          margin-right: 15px;
          vertical-align: middle;
        }
        .category-header {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
        }
        .json-display {
          background: #f8f9fa;
          padding: 15px;
          border-radius: 5px;
          font-family: monospace;
          font-size: 12px;
          white-space: pre-wrap;
          max-height: 300px;
          overflow-y: auto;
          border: 1px solid #dee2e6;
        }
      </style>

      <div class="category-details">
        <h2>${this._isArabic ? 'البيانات الكاملة للفئة المحددة' : 'Complete Selected Category Data'}</h2>
        
        ${completeCategoryData ? `
          <div class="complete-data">
            <div class="category-header">
              <img src="${completeCategoryData.icon}" alt="${completeCategoryData.name}" class="category-icon" />
              <div>
                <h3>${completeCategoryData.name}</h3>
                <p>${completeCategoryData.description || ''}</p>
              </div>
            </div>
            
            <div class="data-field">
              <span class="field-label">${this._isArabic ? 'معرف الفئة:' : 'Category ID:'}</span>
              <span class="field-value">${completeCategoryData.id}</span>
            </div>
            
            <div class="data-field">
              <span class="field-label">${this._isArabic ? 'اسم الفئة:' : 'Category Name:'}</span>
              <span class="field-value">${completeCategoryData.name}</span>
            </div>
            
            <div class="data-field">
              <span class="field-label">${this._isArabic ? 'الأولوية:' : 'Priority:'}</span>
              <span class="field-value">${completeCategoryData.priority || 'N/A'}</span>
            </div>
            
            <div class="data-field">
              <span class="field-label">${this._isArabic ? 'الحالة:' : 'Status:'}</span>
              <span class="field-value">${completeCategoryData.categoryStatus || 'N/A'}</span>
            </div>
            
            <div class="data-field">
              <span class="field-label">${this._isArabic ? 'نشط:' : 'Is Active:'}</span>
              <span class="field-value">${completeCategoryData.isActive ? 'Yes' : 'No'}</span>
            </div>
            
            <div class="data-field">
              <span class="field-label">${this._isArabic ? 'اللون:' : 'Color:'}</span>
              <span class="field-value">${completeCategoryData.color || 'N/A'}</span>
            </div>
            
            <div class="data-field">
              <span class="field-label">${this._isArabic ? 'الفئة الأب:' : 'Parent Category:'}</span>
              <span class="field-value">${completeCategoryData.parentCategoryId || 'N/A'}</span>
            </div>
            
            ${completeCategoryData.subCategories && completeCategoryData.subCategories.length > 0 ? `
              <div class="data-field">
                <span class="field-label">${this._isArabic ? 'الفئات الفرعية:' : 'Sub Categories:'}</span>
                <div class="array-data">
                  ${completeCategoryData.subCategories.length} ${this._isArabic ? 'فئة فرعية' : 'sub categories'}
                  <br><small>استخدم "عرض JSON" لرؤية التفاصيل</small>
                </div>
              </div>
            ` : ''}
            
            ${completeCategoryData.subCategorySuppliers && completeCategoryData.subCategorySuppliers.length > 0 ? `
              <div class="data-field">
                <span class="field-label">${this._isArabic ? 'الموردين:' : 'Suppliers:'}</span>
                <div class="array-data">
                  ${completeCategoryData.subCategorySuppliers.length} ${this._isArabic ? 'مورد' : 'suppliers'}
                  <br><small>استخدم "عرض JSON" لرؤية التفاصيل</small>
                </div>
              </div>
            ` : ''}
            
            <div class="mt-3">
              <button class="btn btn-secondary" onclick="window.categoryDetailsWebPart.showCompleteJSON()">
                ${this._isArabic ? 'عرض JSON الكامل' : 'Show Complete JSON'}
              </button>
              <button class="btn btn-danger" onclick="window.categoryDetailsWebPart.clearData()">
                ${this._isArabic ? 'مسح البيانات' : 'Clear Data'}
              </button>
            </div>
          </div>
        ` : `
          <div class="no-data">
            <h3>${this._isArabic ? 'لا توجد فئة محددة' : 'No Category Selected'}</h3>
            <p>${this._isArabic ? 'يرجى اختيار فئة من الصفحة الرئيسية' : 'Please select a category from the main page'}</p>
          </div>
        `}
        
        <div class="mt-4">
          <h4>${this._isArabic ? 'الإجراءات المتاحة:' : 'Available Actions:'}</h4>
          <button class="btn btn-primary" onclick="window.categoryDetailsWebPart.refreshData()">
            ${this._isArabic ? 'تحديث البيانات' : 'Refresh Data'}
          </button>
          <button class="btn btn-secondary" onclick="window.categoryDetailsWebPart.showAllCategories()">
            ${this._isArabic ? 'عرض جميع الفئات' : 'Show All Categories'}
          </button>
        </div>
        
        <div id="jsonDisplay" style="display: none;">
          <h4>${this._isArabic ? 'البيانات الخام (JSON):' : 'Raw Data (JSON):'}</h4>
          <div class="json-display" id="jsonContent"></div>
          <button class="btn btn-secondary" onclick="document.getElementById('jsonDisplay').style.display='none'">
            ${this._isArabic ? 'إخفاء' : 'Hide'}
          </button>
        </div>
      </div>
    `;

    // تعيين مرجع عام للوصول للدوال
    (window as any).categoryDetailsWebPart = this;
  }

  // Get complete category data from localStorage
  private getCompleteCategoryData(): any {
    try {
      const storedData = localStorage.getItem('selectedCategoryComplete');
      if (storedData) {
        return JSON.parse(storedData);
      }
      return null;
    } catch (error) {
      console.error('Error retrieving complete category data:', error);
      return null;
    }
  }

  // Get all categories data from localStorage
  private getAllCategoriesData(): any[] {
    try {
      const allData = localStorage.getItem('allCategoriesRawData');
      if (allData) {
        return JSON.parse(allData);
      }
      return [];
    } catch (error) {
      console.error('Error retrieving all categories data:', error);
      return [];
    }
  }

  // Show complete JSON data
  public showCompleteJSON(): void {
    const completeCategoryData = this.getCompleteCategoryData();
    
    if (completeCategoryData) {
      const jsonContent = JSON.stringify(completeCategoryData, null, 2);
      const jsonElement = document.getElementById('jsonContent');
      const jsonDisplay = document.getElementById('jsonDisplay');
      
      if (jsonElement && jsonDisplay) {
        jsonElement.textContent = jsonContent;
        jsonDisplay.style.display = 'block';
      }
    } else {
      alert(this._isArabic ? 'لا توجد بيانات' : 'No data available');
    }
  }

  // Clear stored data
  public clearData(): void {
    localStorage.removeItem('selectedCategoryComplete');
    
    this.render(); // إعادة تحديث العرض
    
    const message = this._isArabic ? 'تم مسح البيانات' : 'Data cleared';
    alert(message);
  }

  // Refresh data display
  public refreshData(): void {
    this.render();
  }

  // Show all categories summary
  public showAllCategories(): void {
    const allCategories = this.getAllCategoriesData();
    
    if (allCategories.length > 0) {
      const summary = allCategories.map(cat => 
        `${cat.name} (ID: ${cat.id}) - ${cat.categoryStatus || 'N/A'}`
      ).join('\n');
      
      const message = `${this._isArabic ? 'جميع الفئات المتاحة:' : 'All Available Categories:'}\n\n${summary}`;
      alert(message);
    } else {
      alert(this._isArabic ? 'لا توجد فئات متاحة' : 'No categories available');
    }
  }

  protected getPropertyPaneConfiguration(): any {
    return {
      pages: []
    };
  }
}
