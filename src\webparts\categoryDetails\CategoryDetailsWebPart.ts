import { BaseClientSideWebPart } from "@microsoft/sp-webpart-base";

export interface ICategoryDetailsWebPartProps {
  description: string;
}

export default class CategoryDetailsWebPart extends BaseClientSideWebPart<ICategoryDetailsWebPartProps> {
  private _isArabic: boolean = false;

  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().includes("/ar/");

    // الحصول على ID من URL أو query parameters
    const categoryId = this.getCategoryIdFromUrl();

    // الحصول على البيانات الكاملة من localStorage بناءً على ID
    const completeCategoryData = this.getCompleteCategoryDataById(categoryId);
    
    this.domElement.innerHTML = `
      <style>
        .category-details {
          padding: 20px;
          background: #f8f9fa;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        .complete-data {
          background: white;
          padding: 20px;
          border-radius: 10px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          margin: 20px 0;
          border: 2px solid #007bff;
        }
        .data-field {
          margin: 10px 0;
          padding: 8px;
          background: #f8f9fa;
          border-radius: 5px;
          border-left: 4px solid #007bff;
        }
        .field-label {
          font-weight: bold;
          color: #495057;
          display: inline-block;
          width: 200px;
        }
        .field-value {
          color: #007bff;
        }
        .array-data {
          background: #e7f3ff;
          padding: 10px;
          border-radius: 5px;
          margin-top: 5px;
        }
        .no-data {
          text-align: center;
          padding: 40px;
          color: #666;
        }
        .btn {
          padding: 10px 20px;
          border-radius: 5px;
          cursor: pointer;
          margin: 5px;
          border: none;
          font-weight: bold;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.8; }
        .category-icon {
          width: 60px;
          height: 60px;
          object-fit: contain;
          margin-right: 15px;
          vertical-align: middle;
        }
        .category-header {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
        }
        .json-display {
          background: #f8f9fa;
          padding: 15px;
          border-radius: 5px;
          font-family: monospace;
          font-size: 12px;
          white-space: pre-wrap;
          max-height: 300px;
          overflow-y: auto;
          border: 1px solid #dee2e6;
        }
      </style>

      <div class="category-details">
        <h2>${this._isArabic ? 'البيانات الكاملة للفئة المحددة' : 'Complete Selected Category Data'}</h2>
        
        ${completeCategoryData ? `
          <div class="complete-data">
            <div class="category-header">
              <img src="${completeCategoryData.icon}" alt="${completeCategoryData.name}" class="category-icon" />
              <div>
                <h3>${completeCategoryData.name}</h3>
                <p>${completeCategoryData.description || ''}</p>
                ${categoryId ? `<small style="color: #666;">ID from URL: ${categoryId}</small>` : ''}
              </div>
            </div>
            
            <div class="data-field">
              <span class="field-label">${this._isArabic ? 'معرف الفئة:' : 'Category ID:'}</span>
              <span class="field-value">${completeCategoryData.id}</span>
            </div>
            
            <div class="data-field">
              <span class="field-label">${this._isArabic ? 'اسم الفئة:' : 'Category Name:'}</span>
              <span class="field-value">${completeCategoryData.name}</span>
            </div>
            
            <div class="data-field">
              <span class="field-label">${this._isArabic ? 'الأولوية:' : 'Priority:'}</span>
              <span class="field-value">${completeCategoryData.priority || 'N/A'}</span>
            </div>
            
            <div class="data-field">
              <span class="field-label">${this._isArabic ? 'الحالة:' : 'Status:'}</span>
              <span class="field-value">${completeCategoryData.categoryStatus || 'N/A'}</span>
            </div>
            
            <div class="data-field">
              <span class="field-label">${this._isArabic ? 'نشط:' : 'Is Active:'}</span>
              <span class="field-value">${completeCategoryData.isActive ? 'Yes' : 'No'}</span>
            </div>
            
            <div class="data-field">
              <span class="field-label">${this._isArabic ? 'اللون:' : 'Color:'}</span>
              <span class="field-value">${completeCategoryData.color || 'N/A'}</span>
            </div>
            
            <div class="data-field">
              <span class="field-label">${this._isArabic ? 'الفئة الأب:' : 'Parent Category:'}</span>
              <span class="field-value">${completeCategoryData.parentCategoryId || 'N/A'}</span>
            </div>
            
            ${completeCategoryData.subCategories && completeCategoryData.subCategories.length > 0 ? `
              <div class="data-field">
                <span class="field-label">${this._isArabic ? 'الفئات الفرعية:' : 'Sub Categories:'}</span>
                <div class="array-data">
                  ${completeCategoryData.subCategories.length} ${this._isArabic ? 'فئة فرعية' : 'sub categories'}
                  <br><small>استخدم "عرض JSON" لرؤية التفاصيل</small>
                </div>
              </div>
            ` : ''}
            
            ${completeCategoryData.subCategorySuppliers && completeCategoryData.subCategorySuppliers.length > 0 ? `
              <div class="data-field">
                <span class="field-label">${this._isArabic ? 'الموردين:' : 'Suppliers:'}</span>
                <div class="array-data">
                  ${completeCategoryData.subCategorySuppliers.length} ${this._isArabic ? 'مورد' : 'suppliers'}
                  <br><small>استخدم "عرض JSON" لرؤية التفاصيل</small>
                </div>
              </div>
            ` : ''}
            
            <div class="mt-3">
              <button class="btn btn-secondary" onclick="window.categoryDetailsWebPart.showCompleteJSON()">
                ${this._isArabic ? 'عرض JSON الكامل' : 'Show Complete JSON'}
              </button>
              <button class="btn btn-danger" onclick="window.categoryDetailsWebPart.clearData()">
                ${this._isArabic ? 'مسح البيانات' : 'Clear Data'}
              </button>
            </div>
          </div>
        ` : `
          <div class="no-data">
            <h3>${this._isArabic ? 'لا توجد فئة محددة' : 'No Category Selected'}</h3>
            <p>${this._isArabic ? 'يرجى اختيار فئة من الصفحة الرئيسية أو تمرير ID صحيح في URL' : 'Please select a category from the main page or pass a valid ID in URL'}</p>
            ${categoryId ? `<p style="color: #dc3545;">ID المطلوب: ${categoryId} غير موجود</p>` : ''}
            <div style="margin-top: 15px; font-size: 0.9rem; color: #666;">
              <strong>${this._isArabic ? 'طرق تمرير ID:' : 'Ways to pass ID:'}</strong><br>
              • Query: ?categoryId=123<br>
              • Hash: #categoryId=123<br>
              • Path: /CategoryDetails/123
            </div>
          </div>
        `}
        
        <div class="mt-4">
          <h4>${this._isArabic ? 'الإجراءات المتاحة:' : 'Available Actions:'}</h4>
          <button class="btn btn-primary" onclick="window.categoryDetailsWebPart.refreshData()">
            ${this._isArabic ? 'تحديث البيانات' : 'Refresh Data'}
          </button>
          <button class="btn btn-secondary" onclick="window.categoryDetailsWebPart.showAllCategories()">
            ${this._isArabic ? 'عرض جميع الفئات' : 'Show All Categories'}
          </button>
          <button class="btn btn-secondary" onclick="window.categoryDetailsWebPart.showAvailableIds()">
            ${this._isArabic ? 'عرض جميع الـ IDs' : 'Show Available IDs'}
          </button>
        </div>
        
        <div id="jsonDisplay" style="display: none;">
          <h4>${this._isArabic ? 'البيانات الخام (JSON):' : 'Raw Data (JSON):'}</h4>
          <div class="json-display" id="jsonContent"></div>
          <button class="btn btn-secondary" onclick="document.getElementById('jsonDisplay').style.display='none'">
            ${this._isArabic ? 'إخفاء' : 'Hide'}
          </button>
        </div>
      </div>
    `;

    // تعيين مرجع عام للوصول للدوال
    (window as any).categoryDetailsWebPart = this;
  }

  // Get category ID from URL parameters
  private getCategoryIdFromUrl(): string | null {
    try {
      // طريقة 1: من query parameters (?categoryId=123)
      const urlParams = new URLSearchParams(window.location.search);
      const categoryIdFromQuery = urlParams.get('categoryId');

      if (categoryIdFromQuery) {
        return categoryIdFromQuery;
      }

      // طريقة 2: من hash (#categoryId=123)
      const hash = window.location.hash;
      if (hash.includes('categoryId=')) {
        const match = hash.match(/categoryId=([^&]+)/);
        if (match && match[1]) {
          return match[1];
        }
      }

      // طريقة 3: من path (/CategoryDetails/123)
      const pathParts = window.location.pathname.split('/');
      const lastPart = pathParts[pathParts.length - 1];
      if (lastPart && lastPart !== 'CategoryDetails.aspx' && lastPart.length > 0) {
        // تحقق إذا كان رقم أو ID صالح
        if (/^[a-zA-Z0-9-]+$/.test(lastPart)) {
          return lastPart;
        }
      }

      return null;
    } catch (error) {
      console.error('Error getting category ID from URL:', error);
      return null;
    }
  }

  // Get complete category data by ID from localStorage
  private getCompleteCategoryDataById(categoryId: string | null): any {
    try {
      if (!categoryId) {
        // إذا مفيش ID في URL، جرب تجيب آخر فئة محددة
        const storedData = localStorage.getItem('selectedCategoryComplete');
        if (storedData) {
          return JSON.parse(storedData);
        }
        return null;
      }

      // البحث في جميع الفئات المخزنة
      const allCategoriesData = localStorage.getItem('allCategoriesRawData');
      if (allCategoriesData) {
        const allCategories = JSON.parse(allCategoriesData);
        const foundCategory = allCategories.find((cat: any) => cat.id === categoryId);

        if (foundCategory) {
          // حفظ الفئة المختارة كآخر فئة محددة
          localStorage.setItem('selectedCategoryComplete', JSON.stringify(foundCategory));
          return foundCategory;
        }
      }

      return null;
    } catch (error) {
      console.error('Error retrieving category data by ID:', error);
      return null;
    }
  }

  // Get complete category data from localStorage (الطريقة القديمة للتوافق)
  private getCompleteCategoryData(): any {
    try {
      const storedData = localStorage.getItem('selectedCategoryComplete');
      if (storedData) {
        return JSON.parse(storedData);
      }
      return null;
    } catch (error) {
      console.error('Error retrieving complete category data:', error);
      return null;
    }
  }

  // Get all categories data from localStorage
  private getAllCategoriesData(): any[] {
    try {
      const allData = localStorage.getItem('allCategoriesRawData');
      if (allData) {
        return JSON.parse(allData);
      }
      return [];
    } catch (error) {
      console.error('Error retrieving all categories data:', error);
      return [];
    }
  }

  // Show complete JSON data
  public showCompleteJSON(): void {
    const completeCategoryData = this.getCompleteCategoryData();
    
    if (completeCategoryData) {
      const jsonContent = JSON.stringify(completeCategoryData, null, 2);
      const jsonElement = document.getElementById('jsonContent');
      const jsonDisplay = document.getElementById('jsonDisplay');
      
      if (jsonElement && jsonDisplay) {
        jsonElement.textContent = jsonContent;
        jsonDisplay.style.display = 'block';
      }
    } else {
      alert(this._isArabic ? 'لا توجد بيانات' : 'No data available');
    }
  }

  // Clear stored data
  public clearData(): void {
    localStorage.removeItem('selectedCategoryComplete');
    
    this.render(); // إعادة تحديث العرض
    
    const message = this._isArabic ? 'تم مسح البيانات' : 'Data cleared';
    alert(message);
  }

  // Refresh data display
  public refreshData(): void {
    this.render();
  }

  // Show all categories summary
  public showAllCategories(): void {
    const allCategories = this.getAllCategoriesData();

    if (allCategories.length > 0) {
      const summary = allCategories.map(cat =>
        `${cat.name} (ID: ${cat.id}) - ${cat.categoryStatus || 'N/A'}`
      ).join('\n');

      const message = `${this._isArabic ? 'جميع الفئات المتاحة:' : 'All Available Categories:'}\n\n${summary}`;
      alert(message);
    } else {
      alert(this._isArabic ? 'لا توجد فئات متاحة' : 'No categories available');
    }
  }

  // Show available category IDs
  public showAvailableIds(): void {
    const allCategories = this.getAllCategoriesData();

    if (allCategories.length > 0) {
      const ids = allCategories.map(cat => cat.id).join(', ');
      const currentUrl = window.location.origin + window.location.pathname;

      const examples = [
        `${currentUrl}?categoryId=${allCategories[0].id}`,
        `${currentUrl}#categoryId=${allCategories[0].id}`,
        `${currentUrl.replace('.aspx', '')}/${allCategories[0].id}`
      ];

      const message = `${this._isArabic ? 'جميع الـ IDs المتاحة:' : 'All Available IDs:'}\n\n${ids}\n\n${this._isArabic ? 'أمثلة على الاستخدام:' : 'Usage Examples:'}\n${examples.join('\n')}`;
      alert(message);
    } else {
      alert(this._isArabic ? 'لا توجد فئات متاحة' : 'No categories available');
    }
  }

  protected getPropertyPaneConfiguration(): any {
    return {
      pages: []
    };
  }
}
