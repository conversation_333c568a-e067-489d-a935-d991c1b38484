import { BaseClientSideWebPart } from "@microsoft/sp-webpart-base";

export interface ICategoryDetailsWebPartProps {
  description: string;
}

export default class CategoryDetailsWebPart extends BaseClientSideWebPart<ICategoryDetailsWebPartProps> {
  private _isArabic: boolean = false;

  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().includes("/ar/");

    // الحصول على ID من URL أو query parameters
    const categoryId = this.getCategoryIdFromUrl();

    // الحصول على البيانات الكاملة من localStorage بناءً على ID
    const completeCategoryData = this.getCompleteCategoryDataById(categoryId);
    
    this.domElement.innerHTML = `
      <style>
        .mazya-sub-cat {
          margin-bottom: 20px;
        }
        .card-color {
          background: white;
          border: 1px solid #dee2e6;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .view-btns .btn {
          border-radius: 4px;
          padding: 6px 12px;
        }
        .view-btns .btn.active {
          background-color: #007bff;
          border-color: #007bff;
          color: white;
        }
        .text-color {
          color: #6c757d;
          font-weight: 500;
        }
        .vacancy-card {
          border: 1px solid #dee2e6;
          border-radius: 8px;
          transition: all 0.3s ease;
          text-decoration: none;
          color: inherit;
          position: relative;
          overflow: hidden;
        }
        .vacancy-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          text-decoration: none;
          color: inherit;
        }
        .border-accent {
          position: absolute;
          top: 0;
          left: 0;
          width: 4px;
          height: 100%;
          background: linear-gradient(45deg, #007bff, #0056b3);
        }
        .job-title {
          font-size: 1.1rem;
          font-weight: 600;
          margin-bottom: 10px;
        }
        .head-color {
          color: #333;
        }
        .bi-arrow-right-circle-fill {
          font-size: 1.2rem;
          color: #007bff;
        }
        .no-data {
          text-align: center;
          padding: 40px;
          color: #666;
        }
        .category-header {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          padding: 15px;
          background: #f8f9fa;
          border-radius: 8px;
        }
        .category-icon {
          width: 50px;
          height: 50px;
          object-fit: contain;
          margin-right: 15px;
        }
        .category-name {
          font-size: 1.5rem;
          font-weight: bold;
          color: #333;
          margin: 0;
        }
        .category-description {
          color: #666;
          margin: 5px 0 0 0;
        }
      </style>

      <div class="col-lg-12 mazya-sub-cat">
        ${completeCategoryData ? `
          <!-- Category Header -->
          <div class="category-header">
            <img src="${completeCategoryData.icon}" alt="${completeCategoryData.name}" class="category-icon" />
            <div>
              <h3 class="category-name">${completeCategoryData.name}</h3>
              <p class="category-description">${completeCategoryData.description || ''}</p>
              ${categoryId ? `<small style="color: #666;">ID: ${categoryId}</small>` : ''}
            </div>
          </div>

          <!-- Sub Categories Card -->
          <div class="card card-color mb-4">
            <div class="card-body">
              <!-- Title and View Controls -->
              <div class="d-flex justify-content-between align-items-center mb-3 flex-wrap">
                <h4 class="head-color mb-0">${this._isArabic ? 'الفئات الفرعية' : 'Sub Categories'}</h4>
                <div class="d-flex align-items-center gap-2 flex-wrap view-btns">
                  <span class="text-color">${this._isArabic ? 'عرض' : 'View'}</span>
                  <button class="btn btn-outline-secondary btn-sm active" id="listViewBtn" aria-label="listView" data-view="list">
                    <i class="bi bi-list"></i>
                  </button>
                  <button class="btn btn-outline-secondary btn-sm" id="gridViewBtn" aria-label="gridView" data-view="grid">
                    <i class="bi bi-grid-3x3-gap"></i>
                  </button>
                </div>
              </div>

              <hr>

              <!-- Sub Categories Listings -->
              <div class="row g-4" id="subCategoriesContainer">
                ${this.renderSubCategories(completeCategoryData)}
              </div>
            </div>
          </div>
        ` : `
          <div class="col-lg-12 mazya-sub-cat">
            <div class="card card-color mb-4">
              <div class="card-body">
                <div class="no-data">
                  <h3>${this._isArabic ? 'لا توجد فئة محددة' : 'No Category Selected'}</h3>
                  <p>${this._isArabic ? 'يرجى اختيار فئة من الصفحة الرئيسية أو تمرير ID صحيح في URL' : 'Please select a category from the main page or pass a valid ID in URL'}</p>
                  ${categoryId ? `<p style="color: #dc3545;">ID المطلوب: ${categoryId} غير موجود</p>` : ''}
                  <div style="margin-top: 15px; font-size: 0.9rem; color: #666;">
                    <strong>${this._isArabic ? 'طرق تمرير ID:' : 'Ways to pass ID:'}</strong><br>
                    • Query: ?categoryId=123<br>
                    • Hash: #categoryId=123<br>
                    • Path: /CategoryDetails/123
                  </div>
                  <div class="mt-3">
                    <button class="btn btn-primary" onclick="window.categoryDetailsWebPart.showAvailableIds()">
                      ${this._isArabic ? 'عرض جميع الـ IDs المتاحة' : 'Show Available IDs'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        `}
      </div>
    `;

    // تعيين مرجع عام للوصول للدوال
    (window as any).categoryDetailsWebPart = this;

    // إضافة event listeners للـ view buttons
    this.attachViewToggleEvents();
  }

  // Render sub categories
  private renderSubCategories(categoryData: any): string {
    if (!categoryData.subCategories || categoryData.subCategories.length === 0) {
      return `
        <div class="col-12">
          <div class="no-data">
            <p>${this._isArabic ? 'لا توجد فئات فرعية لهذه الفئة' : 'No sub categories available for this category'}</p>
          </div>
        </div>
      `;
    }

    return categoryData.subCategories.map((subCategory: any, index: number) => `
      <div class="col-md-6 col-lg-4">
        <a href="#" class="card vacancy-card h-100 text-decoration-none" onclick="window.categoryDetailsWebPart.selectSubCategory('${subCategory.id || index}', '${subCategory.name || `Sub Category ${index + 1}`}')">
          <div class="border-accent"></div>
          <div class="card-body">
            <div class="job-info">
              <h4 class="job-title head-color">${subCategory.name || `Sub Category ${index + 1}`}</h4>
              ${subCategory.description ? `<p class="text-muted small">${subCategory.description}</p>` : ''}
            </div>
            <div class="d-flex justify-content-end">
              <i class="bi bi-arrow-right-circle-fill text-color"></i>
            </div>
          </div>
        </a>
      </div>
    `).join('');
  }

  // Attach view toggle events
  private attachViewToggleEvents(): void {
    setTimeout(() => {
      const listViewBtn = document.getElementById('listViewBtn');
      const gridViewBtn = document.getElementById('gridViewBtn');
      const container = document.getElementById('subCategoriesContainer');

      if (listViewBtn && gridViewBtn && container) {
        listViewBtn.addEventListener('click', () => {
          listViewBtn.classList.add('active');
          gridViewBtn.classList.remove('active');
          container.className = 'row g-2'; // Smaller gaps for list view
        });

        gridViewBtn.addEventListener('click', () => {
          gridViewBtn.classList.add('active');
          listViewBtn.classList.remove('active');
          container.className = 'row g-4'; // Larger gaps for grid view
        });
      }
    }, 100);
  }

  // Select sub category
  public selectSubCategory(subCategoryId: string, subCategoryName: string): void {
    try {
      // Store selected sub category
      localStorage.setItem('selectedSubCategory', JSON.stringify({
        id: subCategoryId,
        name: subCategoryName,
        parentCategoryId: this.getCategoryIdFromUrl()
      }));

      const message = this._isArabic
        ? `تم اختيار الفئة الفرعية: ${subCategoryName}`
        : `Sub category selected: ${subCategoryName}`;

      alert(message);

      // يمكنك إضافة navigation لصفحة الموردين هنا
      // const suppliersUrl = `/sites/intranet-qm/SitePages/Suppliers.aspx?subCategoryId=${subCategoryId}`;
      // window.location.href = suppliersUrl;

    } catch (error) {
      console.error('Error selecting sub category:', error);
    }
  }

  // Get category ID from URL parameters
  private getCategoryIdFromUrl(): string | null {
    try {
      // طريقة 1: من query parameters (?categoryId=123)
      const urlParams = new URLSearchParams(window.location.search);
      const categoryIdFromQuery = urlParams.get('categoryId');

      if (categoryIdFromQuery) {
        return categoryIdFromQuery;
      }

      // طريقة 2: من hash (#categoryId=123)
      const hash = window.location.hash;
      if (hash.includes('categoryId=')) {
        const match = hash.match(/categoryId=([^&]+)/);
        if (match && match[1]) {
          return match[1];
        }
      }

      // طريقة 3: من path (/CategoryDetails/123)
      const pathParts = window.location.pathname.split('/');
      const lastPart = pathParts[pathParts.length - 1];
      if (lastPart && lastPart !== 'CategoryDetails.aspx' && lastPart.length > 0) {
        // تحقق إذا كان رقم أو ID صالح
        if (/^[a-zA-Z0-9-]+$/.test(lastPart)) {
          return lastPart;
        }
      }

      return null;
    } catch (error) {
      console.error('Error getting category ID from URL:', error);
      return null;
    }
  }

  // Get complete category data by ID from localStorage
  private getCompleteCategoryDataById(categoryId: string | null): any {
    try {
      if (!categoryId) {
        // إذا مفيش ID في URL، جرب تجيب آخر فئة محددة
        const storedData = localStorage.getItem('selectedCategoryComplete');
        if (storedData) {
          return JSON.parse(storedData);
        }
        return null;
      }

      // البحث في جميع الفئات المخزنة
      const allCategoriesData = localStorage.getItem('allCategoriesRawData');
      if (allCategoriesData) {
        const allCategories = JSON.parse(allCategoriesData);
        const foundCategory = allCategories.find((cat: any) => cat.id === categoryId);

        if (foundCategory) {
          // حفظ الفئة المختارة كآخر فئة محددة
          localStorage.setItem('selectedCategoryComplete', JSON.stringify(foundCategory));
          return foundCategory;
        }
      }

      return null;
    } catch (error) {
      console.error('Error retrieving category data by ID:', error);
      return null;
    }
  }

  // Get complete category data from localStorage (الطريقة القديمة للتوافق)
  private getCompleteCategoryData(): any {
    try {
      const storedData = localStorage.getItem('selectedCategoryComplete');
      if (storedData) {
        return JSON.parse(storedData);
      }
      return null;
    } catch (error) {
      console.error('Error retrieving complete category data:', error);
      return null;
    }
  }

  // Get all categories data from localStorage
  private getAllCategoriesData(): any[] {
    try {
      const allData = localStorage.getItem('allCategoriesRawData');
      if (allData) {
        return JSON.parse(allData);
      }
      return [];
    } catch (error) {
      console.error('Error retrieving all categories data:', error);
      return [];
    }
  }

  // Show complete JSON data
  public showCompleteJSON(): void {
    const completeCategoryData = this.getCompleteCategoryData();
    
    if (completeCategoryData) {
      const jsonContent = JSON.stringify(completeCategoryData, null, 2);
      const jsonElement = document.getElementById('jsonContent');
      const jsonDisplay = document.getElementById('jsonDisplay');
      
      if (jsonElement && jsonDisplay) {
        jsonElement.textContent = jsonContent;
        jsonDisplay.style.display = 'block';
      }
    } else {
      alert(this._isArabic ? 'لا توجد بيانات' : 'No data available');
    }
  }

  // Clear stored data
  public clearData(): void {
    localStorage.removeItem('selectedCategoryComplete');
    
    this.render(); // إعادة تحديث العرض
    
    const message = this._isArabic ? 'تم مسح البيانات' : 'Data cleared';
    alert(message);
  }

  // Refresh data display
  public refreshData(): void {
    this.render();
  }

  // Show all categories summary
  public showAllCategories(): void {
    const allCategories = this.getAllCategoriesData();

    if (allCategories.length > 0) {
      const summary = allCategories.map(cat =>
        `${cat.name} (ID: ${cat.id}) - ${cat.categoryStatus || 'N/A'}`
      ).join('\n');

      const message = `${this._isArabic ? 'جميع الفئات المتاحة:' : 'All Available Categories:'}\n\n${summary}`;
      alert(message);
    } else {
      alert(this._isArabic ? 'لا توجد فئات متاحة' : 'No categories available');
    }
  }

  // Show available category IDs
  public showAvailableIds(): void {
    const allCategories = this.getAllCategoriesData();

    if (allCategories.length > 0) {
      const ids = allCategories.map(cat => cat.id).join(', ');
      const currentUrl = window.location.origin + window.location.pathname;

      const examples = [
        `${currentUrl}?categoryId=${allCategories[0].id}`,
        `${currentUrl}#categoryId=${allCategories[0].id}`,
        `${currentUrl.replace('.aspx', '')}/${allCategories[0].id}`
      ];

      const message = `${this._isArabic ? 'جميع الـ IDs المتاحة:' : 'All Available IDs:'}\n\n${ids}\n\n${this._isArabic ? 'أمثلة على الاستخدام:' : 'Usage Examples:'}\n${examples.join('\n')}`;
      alert(message);
    } else {
      alert(this._isArabic ? 'لا توجد فئات متاحة' : 'No categories available');
    }
  }

  protected getPropertyPaneConfiguration(): any {
    return {
      pages: []
    };
  }
}
