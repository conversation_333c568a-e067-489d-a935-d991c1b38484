{"name": "qatar-museums", "version": "0.0.1", "private": true, "engines": {"node": ">=18.17.1 <19.0.0"}, "main": "lib/index.js", "scripts": {"build": "gulp bundle", "clean": "gulp clean", "test": "gulp test"}, "dependencies": {"@fluentui/react": "^8.106.4", "@microsoft/decorators": "1.20.0", "@microsoft/sp-application-base": "1.20.0", "@microsoft/sp-component-base": "1.20.0", "@microsoft/sp-core-library": "1.20.0", "@microsoft/sp-dialog": "1.20.0", "@microsoft/sp-lodash-subset": "1.20.0", "@microsoft/sp-office-ui-fabric-core": "1.20.0", "@microsoft/sp-property-pane": "1.20.0", "@microsoft/sp-webpart-base": "1.20.0", "bootstrap": "^5.3.5", "bootstrap-icons": "^1.11.3", "flatpickr": "^4.6.13", "react": "17.0.1", "react-dom": "17.0.1", "sweetalert2": "^11.22.0", "tslib": "2.3.1"}, "devDependencies": {"@fluentui/react": "^8.106.4", "@microsoft/eslint-config-spfx": "1.20.2", "@microsoft/eslint-plugin-spfx": "1.20.2", "@microsoft/rush-stack-compiler-4.7": "0.1.0", "@microsoft/sp-build-web": "1.20.2", "@microsoft/sp-module-interfaces": "1.20.2", "@rushstack/eslint-config": "4.0.1", "@types/react": "17.0.45", "@types/react-dom": "17.0.17", "@types/webpack-env": "~1.15.2", "ajv": "^6.12.5", "eslint": "8.57.0", "eslint-plugin-react-hooks": "4.3.0", "gulp": "4.0.2", "typescript": "4.7.4"}}