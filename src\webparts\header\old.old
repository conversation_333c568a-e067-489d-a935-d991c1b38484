import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient } from '@microsoft/sp-http';

// import { setLanguage, _isArabic } from '../../utils/i18n';


export interface IHeaderWebPartProps {
  description: string;
}

export default class HeaderWebPart extends BaseClientSideWebPart<IHeaderWebPartProps> {

  private _isArabic: boolean = false;


  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;
    const placeholderText = this._isArabic ? 'ابحث هنا...' : 'Search...';
    const langText = this._isArabic ? 'English' : 'العربية';
    const noResultsText = this._isArabic ? 'لم يتم العثور على نتائج' : 'No results found';


    this.domElement.innerHTML = `
  <style>
    .search-result-item a:hover {
      background-color: #f8f9fa;
      transition: 0.2s;
    }

    .hover-shadow:hover {
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .d-none {
      display: none !important;
    }
  </style>

  <header class="py-2 border-bottom">
    <div class="container-fluid">
      <div class="d-flex align-items-center justify-content-between flex-wrap">
        <!-- Logo -->
        <div class="logo-container d-flex align-items-center">
          <a href="/" class="d-flex align-items-center text-decoration-none">
            <img class="logo-b" src="${require('./assets/logo.svg')}" alt="Qatar Museums Logo" height="50">
            <img class="logo-w" src="${require('./assets/logo-w.svg')}" alt="Qatar Museums Logo" height="50">
          </a>
        </div>




        <nav class="navbar navbar-expand-lg">
          <div class="container-fluid">
            <button class="navbar-toggler d-lg-none" aria-label="menuToggle" id="menuToggle" type="button">
              <i class="bi bi-list fs-2"></i>
            </button>
            <div class="responsive-menu" id="mainMenu">
              <div class="d-flex justify-content-end d-lg-none">
                <button type="button" id="closeMenuBtn" aria-label="closeMenuBtn"
                  class="btn btn-outline-secondary btn-sm mb-3">
                  <i class="bi bi-x-lg"></i>
                </button>
              </div>


              
                <ul class="navbar-nav me-auto mb-2 mb-lg-0"></ul>
              
            </div>
        </nav>

        <!-- Right Side -->
        <div class="d-flex align-items-center wrap-checker">
          <!-- Search Form -->
          <form class="d-flex me-3 position-relative d-none" id="searchForm">
            <input class="form-control" type="search" id="searchInput" placeholder="${placeholderText}"
              aria-label="Search">
            <button class="btn position-absolute end-0 top-0 bottom-0 btn-main" aria-label="Search" type="submit">
              <i class="bi bi-search icon-white"></i>
            </button>
          </form>
          <div id="searchResults" class="bg-white shadow-sm p-3 mt-2 d-none"
            style="position:fixed; z-index:999; width: 300px; max-height: 300px; overflow-y: auto;">
          </div>
  
          <!-- Notifications -->
          <div class="dropdown position-relative me-3 notifi">
            <a href="#" class="text-decoration-none position-relative" data-bs-toggle="dropdown">
              <i class="bi bi-bell btn-main-link icon-md main-color"></i>
              <span class="position-absolute top-0 start-100 translate-top badge rounded-pill bg-danger">2</span>
            </a>
            <div class="dropdown-menu dropdown-menu-end p-3 shadow" style="min-width: 320px; max-width: 360px;">
              <a href="#" class="mb-3 d-flex align-items-start text-decoration-none">
                <div class="me-3 bg-main-30 rounded p-2 d-flex align-items-center justify-content-center">
                  <i class="bi bi-gear-fill mx-1 icon-md main-color"></i>
                </div>
                <div>
                  <strong class="head-color">Happy Birthday</strong><br />
                  <small class="text-color">03/12/2024</small>
                </div>
              </a>
              <hr class="my-2">
              <a href="#" class="mb-3 d-flex align-items-start text-decoration-none">
                <div class="me-3 bg-main-30 rounded p-2 d-flex align-items-center justify-content-center">
                  <i class="bi bi-gear-fill mx-1 icon-md main-color"></i>
                </div>
                <div>
                  <strong class="head-color">The Art of Color and Form:</strong><br />
                  <small class="text-color">Exploring Kelly’s Influence on Fashion Workshop</small><br />
                  <small class="text-color">03/12/2024</small>
                </div>
              </a>
              <hr class="my-2">
              <div class="text-end">
                <a href="#" class="fw-bold text-pink text-decoration-none btn-main-link">View All</a>
              </div>
            </div>
          </div>
  
          <!-- Language Toggle -->
          <div class="lang-toggle">
            <a href="#" id="langToggleBtn" class="btn btn-sm btn-main-link d-flex">
              ${langText} <i class="bi bi-globe icon-md mx-1 main-color"></i>
            </a> 
           

          </div>
        </div>
      </div>
      

    </div>
    
  </header>`;

    const menuToggle = this.domElement.querySelector('#menuToggle');
    const mainMenu = this.domElement.querySelector('#mainMenu');
    const closeBtn = this.domElement.querySelector('#closeMenuBtn');

    menuToggle?.addEventListener('click', (e) => {

      mainMenu?.classList.toggle('active');
    });

    closeBtn?.addEventListener('click', (e) => {

      mainMenu?.classList.remove('active');
    });

    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;

      if (!mainMenu?.contains(target) && !menuToggle?.contains(target)) {
        mainMenu?.classList.remove('active');
      }
    });

    // const langBtn = this.domElement.querySelector('#langToggleBtn');
    // langBtn?.addEventListener('click', () => {
    //   const newLang = _isArabic() === 'en' ? 'ar' : 'en';
    //   setLanguage(newLang);
    //   location.reload(); 
    // });


    const langBtn = this.domElement.querySelector('#langToggleBtn');

    langBtn?.addEventListener('click', (e) => {
      e.preventDefault();
      const path = window.location.pathname;
      const isArabic = path.toLowerCase().includes('/sitepages/ar/');
      const newPath = isArabic
        ? path.replace(/\/sitepages\/ar\//i, '/SitePages/')
        : path.replace(/\/sitepages\//i, '/SitePages/ar/');
      const newUrl = `${window.location.origin}${newPath}${window.location.search}${window.location.hash}`;
      window.location.href = newUrl;
    });

    // Search
    const searchForm = this.domElement.querySelector('#searchForm') as HTMLFormElement;
    const searchInput = this.domElement.querySelector('#searchInput') as HTMLInputElement;
    const searchResults = this.domElement.querySelector('#searchResults') as HTMLDivElement;

    searchForm?.addEventListener('submit', async (e) => {
      e.preventDefault();
      const query = searchInput.value.trim();
      if (!query) return;

      const results = await this.searchSharePoint(query);
      if (results.length > 0) {
        searchResults.classList.remove('d-none');
        searchResults.innerHTML = results.map(result => `
  <div class="search-result-item mb-2">
    <a href="${result.Path}" target="_blank"
      class="d-flex align-items-start text-decoration-none border rounded p-2 hover-shadow">
      <i class="bi bi-file-earmark-text-fill text-primary me-2 fs-4"></i>
      <div>
        <strong>${result.Title}</strong><br />
        <small class="text-muted">${new URL(result.Path).hostname}</small>
      </div>
    </a>
  </div>
  `).join('');
      } else {
        searchResults.classList.remove('d-none');
        searchResults.innerHTML = `<div class="text-muted text-center">${noResultsText}</div>`;
      }
    });

    document.addEventListener('click', (event) => {
      if (!searchResults.contains(event.target as Node) && !searchInput.contains(event.target as Node)) {
        searchResults.classList.add('d-none');
      }
    });

    this.loadMenuFromList();
  }
  private async searchSharePoint(query: string): Promise<any[]> {
    const endpoint =
      `${this.context.pageContext.web.absoluteUrl}/_api/search/query?querytext='${query}'&selectproperties='Title,Path'`;
    const response = await this.context.spHttpClient.get(endpoint, SPHttpClient.configurations.v1);
    const data = await response.json();
    return data.PrimaryQueryResult.RelevantResults.Table.Rows.map((row: any) => {
      const item: any = {};
      row.Cells.forEach((cell: any) => {
        item[cell.Key] = cell.Value;
      });
      return item;
    });
  }
  private async loadMenuFromList(): Promise<void> {

    const endpoint = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('MainMenu')/items`;
    const response = await this.context.spHttpClient.get(endpoint, SPHttpClient.configurations.v1);
    const data = await response.json();

    const items = data.value || [];

    if (!Array.isArray(items) || items.length === 0) {
      console.warn('No items found in the list.');
      return;
    }

    items.sort((a, b) => (a.order0 ?? 0) - (b.order0 ?? 0));

    const parents = items.filter((item: any) => !item.ParentId);
    const children = items.filter((item: any) => item.ParentId);

    const menuContainer = this.domElement.querySelector('#mainMenu ul');
    if (!menuContainer) return;

    // Get the current language
    // const _isArabic = _isArabic();

    // Loop through parent items and display based on the language
    parents.forEach((parent: any) => {
      const hasChildren = children.some((c: any) => c.ParentId === parent.Id);

      const li = document.createElement('li');
      li.className = hasChildren ? 'nav-item dropdown' : 'nav-item';

      const parentTitle = this._isArabic ? parent.Title_AR || '' : parent.Title || '';
      const parentLink = this._isArabic ? parent.LinkUrl_AR || '#' : parent.LinkUrl || '#';

      if (hasChildren) {
        li.innerHTML = `
          <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            ${parentTitle}
          </a>
          <ul class="dropdown-menu"></ul>
        `;

        const dropdown = li.querySelector('.dropdown-menu') as HTMLUListElement;
        children
          .filter((c: any) => c.ParentId === parent.Id)
          .sort((a, b) => (a.order0 ?? 0) - (b.order0 ?? 0))
          .forEach((child: any) => {

            const childTitle = this._isArabic ? child.Title_AR || '' : child.Title || '';
            const childLink = this._isArabic ? child.LinkUrl_AR || '#' : child.LinkUrl || '#';
            const childLi = document.createElement('li');
            childLi.innerHTML = `<a class="dropdown-item" href="${childLink}">${childTitle || ''}</a>`;
            dropdown.appendChild(childLi);
          });
      } else {
        li.innerHTML = `<a class="nav-link" href="${parentLink}">${parentTitle || ''}</a>`;
      }

      menuContainer.appendChild(li);
    });
  }









}