import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';

import Swal from 'sweetalert2';



export interface INewsDetailsWebPartProps {
  description: string;
  selectedVivaGroup: string;
  enableVivaSharing: boolean;
}
export interface IBreadcrumbItem {
  title: string;
  url?: string;
  isCurrent?: boolean;
}
export default class NewsDetailsWebPart extends BaseClientSideWebPart<INewsDetailsWebPartProps> {
  _isArabic: boolean = false;
  private _userGroups: Array<{key: string, text: string}> = [];
  newsItems: {
    ID: number;
    Title: string;
    Title_AR: string;
    NewsDate: string;
    ViewCount: number;
    Image: string;
    Content: string;
    Content_AR: string;
    attachments: string[];
  }[] = [];

  currentNews: {
    ID: number;
    Title: string;
    Title_AR: string;
    NewsDate: string;
    ViewCount: number;
    Image: string;
    Content: string;
    Content_AR: string;
    alt_EN: string;
    alt_AR: string,
  } | null = null;

  attachments: string[] = [];

  private getListEndpoint(listTitle: string, selectFields?: string, expandFields?: string, filterQuery?: string, orderQuery?: string, topQuery?: string, forceMainSite?: boolean): string {
    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    const siteUrl = this.context.pageContext.site.absoluteUrl;
    const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    const baseUrl = forceMainSite
      ? `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
      : (isSubSite
        ? `${currentWebUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
        : `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`);

    const queryParams: string[] = [];

    if (selectFields) {
      queryParams.push(`$select=${selectFields}`);
    }

    if (expandFields) {
      queryParams.push(`$expand=${expandFields}`);
    }

    if (filterQuery) {
      queryParams.push(filterQuery);
    }

    if (orderQuery) {
      queryParams.push(orderQuery);
    }

    if (topQuery) {
      queryParams.push(topQuery);
    }

    

    return queryParams.length > 0
      ? `${baseUrl}?${queryParams.join('&')}`
      : baseUrl;
  }

  fetchRecentNews(): Promise<void> {

    const listName = 'News Articles';
    const selectFields = '*';
    const orderQuery = '$orderby=NewsDate desc';
    const topQuery = '$top=3';

    const url = this.getListEndpoint(listName, selectFields, undefined, undefined, orderQuery, topQuery);

   
    return this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => response.json())
      .then((data) => {
        this.newsItems = data.value.map((item: any) => ({
          ID: item.ID,
          Title: item.Title,
          Title_AR: item.Title_AR,
          NewsDate: item.NewsDate,
          ViewCount: item.ViewCount || 0,
          Image: item.Image || '', 
          Content: item.NewsBody_EN || '',
          Content_AR: item.NewsBody_AR || '',
          alt_EN: item.alt_EN || '',
          alt_AR: item.alt_AR || '',
          attachments: [] 
        }));
        return Promise.all(this.newsItems.map(item => this.getAttachments(item.ID)));
      }).then((attachmentsArray) => {
        this.newsItems = this.newsItems.map((item, index) => {
          item.attachments = attachmentsArray[index]; 
          return item;
        });
        this.render(); 
      }).catch(error => {
        console.error('Error fetching recent news:', error);
      });
  }

  getAttachments(itemId: number): Promise<string[]> {
    const listName = "News Articles";

    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    const siteUrl = this.context.pageContext.site.absoluteUrl;
    const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    const baseUrl = isSubSite
      ? `${currentWebUrl}/_api/web/lists/getbytitle('${listName}')/items(${itemId})/AttachmentFiles`
      : `${siteUrl}/_api/web/lists/getbytitle('${listName}')/items(${itemId})/AttachmentFiles`;


    return this.context.spHttpClient.get(baseUrl, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        return data.value.map((file: any) => {
          return `${file.ServerRelativeUrl}`;
        });
      })
      .catch(error => {
        console.error('Error fetching attachments:', error);
        return [];
      });
  }

  fetchNewsDetails(newsId: number): Promise<void> {

    const listName = 'News Articles';
    const selectFields = '*';
    const filterQuery = `$filter=ID eq ${newsId}`;

    const url = this.getListEndpoint(listName, selectFields, undefined, filterQuery);

    
    return this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => response.json())
      .then((data) => {
        if (data.value.length > 0) {
          const news = data.value[0];
          return this.getAttachments(news.ID).then((attachments) => {
            this.attachments = attachments;

            let imageUrl = '';
            const bannerImageData = news?.Image ? JSON.parse(news.Image) : null;

            if (bannerImageData?.serverUrl && bannerImageData?.serverRelativeUrl) {
              imageUrl = bannerImageData.serverUrl + bannerImageData.serverRelativeUrl;
            } else if (this.attachments.length > 0) {
              imageUrl = this.attachments[0];
            } else {
              imageUrl = require('./assets/img.jpg');
            }

            this.currentNews = {
              ID: news.ID,
              Title: news.Title,
              Title_AR: news.Title_AR,
              NewsDate: news.NewsDate,
              ViewCount: news.ViewCount || 0,
              Image: imageUrl,
              Content: news.NewsBody_EN || '',
              Content_AR: news.NewsBody_AR || '',
              alt_EN: news.alt_EN || '',
              alt_AR: news.alt_AR || '',
            };
            this.render();
          });
        }
      }).catch(error => {
        console.error('Error fetching news details:', error);
      });
  }

  async onInit(): Promise<void> {
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;

    const urlParams = new URLSearchParams(window.location.search);
    const newsId = urlParams.get('newsid');

    try {
      // تحميل بيانات الأخبار
      await Promise.all([
        this.fetchNewsDetails(Number(newsId)),
        this.fetchRecentNews()
      ]);

      // زيادة عداد المشاهدات مرة واحدة فقط لكل session
      if (newsId) {
        this.incrementViewCountOnce(Number(newsId));
      }

      this.render();
    } catch (error) {
      console.error('Error in onInit:', error);
    }
  }

  async loadUserGroups(): Promise<void> {
    try {
      // إظهار loading state
      this._userGroups = [
        { key: '', text: this._isArabic ? 'جاري تحميل المجتمعات...' : 'Loading communities...' }
      ];

      const graphClient = await this.context.msGraphClientFactory.getClient('3');
      console.log('Loading user communities...');

      let allCommunities: any[] = [];

      // جلب Microsoft 365 Groups مع معلومات Yammer
      try {
        console.log('Trying to get Groups with Yammer info...');

        const groupsResponse = await graphClient
          .api('/me/memberOf')
          .select('*')
          .get();

        if (groupsResponse?.value?.length > 0) {
          // فلترة Groups اللي عندها Yammer
          const yammerGroups = groupsResponse.value.filter((group: any) =>
            group.groupTypes?.includes('Unified') &&
            group.mailEnabled &&
            group.resourceProvisioningOptions?.includes('Team')
          );

          if (yammerGroups.length > 0) {
            // جلب معلومات Yammer لكل group
            for (const group of yammerGroups) {
              try {
                // محاولة جلب Yammer group info
                const yammerInfo = await graphClient
                  .api(`/groups/${group.id}`)
                  .select('id,displayName,description,externalId')
                  .get();

                allCommunities.push({
                  id: group.id,
                  displayName: group.displayName,
                  description: group.description,
                  type: 'yammer-group',
                  externalId: yammerInfo.externalId || group.id
                });
              } catch (yammerInfoError) {
                // إذا فشل، استخدم الـ group عادي
                allCommunities.push({
                  ...group,
                  type: 'group'
                });
              }
            }
            console.log('Groups with Yammer loaded:', yammerGroups.length);
          }
        }
      } catch (groupsError) {
        console.log('Groups API failed:', groupsError);
      }

      // جلب Teams أولاً
      try {
        const teamsResponse = await graphClient
          .api('/me/joinedTeams')
          .select('id,displayName,description')
          .get();

        if (teamsResponse?.value?.length > 0) {
          allCommunities.push(...teamsResponse.value.map((team: any) => ({
            ...team,
            type: 'team'
          })));
          console.log('Teams loaded:', teamsResponse.value.length);
        }
      } catch (teamsError) {
        console.log('Teams API failed:', teamsError);
      }

      // جلب Microsoft 365 Groups كـ fallback
      try {
        const groupsResponse = await graphClient
          .api('/me/memberOf')
          .select('*')
          .get();

        if (groupsResponse?.value?.length > 0) {
          const unifiedGroups = groupsResponse.value.filter((group: any) =>
            group.groupTypes?.includes('Unified') && group.mailEnabled
          );

          if (unifiedGroups.length > 0) {
            allCommunities.push(...unifiedGroups.map((group: any) => ({
              ...group,
              type: 'group'
            })));
            console.log('Groups loaded:', unifiedGroups.length);
          }
        }
      } catch (groupsError) {
        console.log('Groups API failed:', groupsError);
      }

      if (allCommunities.length > 0) {
        // إزالة المكررات بناءً على الـ ID
        const uniqueCommunities = allCommunities.filter((community, index, self) =>
          index === self.findIndex(c => c.id === community.id)
        );

        this._userGroups = uniqueCommunities
          .sort((a: any, b: any) => a.displayName.localeCompare(b.displayName))
          .map((community: any) => ({
            key: community.id,
            text: `${community.displayName} (${community.type === 'team' ? 'Teams' : 'Group'})`
          }));

        console.log('Communities loaded:', this._userGroups.length);
      } else {
        this._userGroups = [
          { key: '', text: this._isArabic ? 'لا توجد مجتمعات متاحة' : 'No communities available' }
        ];
      }

      // تحديث الـ property pane
      if (this.context.propertyPane.isPropertyPaneOpen()) {
        this.context.propertyPane.refresh();
      }
    } catch (error) {
      console.error('Error loading communities:', error);
      this._userGroups = [
        { key: '', text: this._isArabic ? 'خطأ في تحميل المجتمعات' : 'Error loading communities' }
      ];
    }
  }

  incrementViewCountOnce(newsId: number): void {
    const sessionKey = `news_viewed_${newsId}`;
    const hasViewed = sessionStorage.getItem(sessionKey);

    if (!hasViewed) {
      sessionStorage.setItem(sessionKey, 'true');
      this.incrementViewCount(newsId);
      console.log(`First view in this session for news ID: ${newsId}`);
    } else {
      console.log(`News ID ${newsId} already viewed in this session - not incrementing`);
    }
  }

  incrementViewCount(newsId: number): Promise<void> {
    const listName = 'News Articles';

    const getCurrentViewCountUrl = this.getListEndpoint(
      listName,
      'ViewCount',
      undefined,
      `$filter=ID eq ${newsId}`
    );

    return this.context.spHttpClient.get(getCurrentViewCountUrl, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
      })
      .then((data: any) => {
        const currentViewCount = data.value && data.value.length > 0 ? (data.value[0].ViewCount || 0) : 0;
        const newViewCount = currentViewCount + 1;

        const updateUrl = this.getListEndpoint(listName) + `(${newsId})`;

        const updateBody = JSON.stringify({
          'ViewCount': newViewCount
        });

        return this.context.spHttpClient.post(updateUrl, SPHttpClient.configurations.v1, {
          headers: {
            'Accept': 'application/json;odata=nometadata',
            'Content-Type': 'application/json;odata=nometadata',
            'odata-version': '',
            'IF-MATCH': '*',
            'X-HTTP-Method': 'MERGE'
          },
          body: updateBody
        }).then((updateResponse: SPHttpClientResponse) => {
          if (updateResponse.ok || updateResponse.status === 204) {
            if (this.currentNews) {
              this.currentNews.ViewCount = newViewCount;
              const viewCountElement = this.domElement.querySelector('.view-count-display');
              if (viewCountElement) {
                viewCountElement.textContent = newViewCount.toString();
              }
            }
            console.log(`View count updated to ${newViewCount} for news ID: ${newsId}`);
          } else {
            console.error(`Failed to update view count: ${updateResponse.status} ${updateResponse.statusText}`);
          }
        });
      })
      .catch(error => {
        console.error('Error incrementing view count:', error);
      });
  }

  render(): void {

    if (this.currentNews) {
      const referrer = document.referrer;
      const isArabic = window.location.pathname.includes('/ar/');
      const backText = isArabic ? 'رجوع' : 'Back';

      const breadcrumbHtml = `
      <ol class="breadcrumb m-0">
        <li class="breadcrumb-item"><a class="btn-main-link fw-bold" href="/sites/intranet-qm/">Home</a></li>
        <li class="breadcrumb-item"><a class="btn-main-link fw-bold" href="/sites/intranet-qm/SitePages/News.aspx">News</a></li>
        <li class="breadcrumb-item active" aria-current="page">News Details</li>
      </ol>
      `;

      if (referrer && referrer !== window.location.href) {
        const refUrl = new URL(referrer);
        const serverRelative = refUrl.pathname;

        if (serverRelative !== '/') {
          this.getPageTitle(serverRelative).then(title => {
            const referrerNameElement = this.domElement.querySelector('#referrerName');
            if (referrerNameElement) {
              referrerNameElement.textContent = title;
            }
          });
        }
      }


      this.domElement.innerHTML = `
      <section class="bread-cramp mt-3 mb-3">
        <div class="card card-color">
          <div class="card-body d-flex justify-content-between align-items-center flex-wrap">
            <nav aria-label="breadcrumb">
              <ol class="breadcrumb breadcrumb-container m-0">
                ${breadcrumbHtml}
              </ol>
            </nav>
            <div class="d-flex align-items-center gap-3">
              


            <button class="btn btn-outline-dark d-flex align-items-center gap-1" onclick="history.back()">
              <i class="bi bi-arrow-left"></i> ${backText}
            </button>
            

            </div>
          </div>
        </div>
      </section>
      
        <div class="row news-details">
    <div class="col-lg-8">
        <article class="news-article card card-color section-card mx-2 p-4 rounded-3 shadow-sm">
            <header class="mb-4">
                <h2 class="head-color">${this._isArabic ? this.currentNews.Title_AR : this.currentNews.Title}</h2>
                <hr>
                <div class="meta d-flex align-items-center gap-3 text-color">
                    <div>
                        <span><i class="bi bi-calendar main-color"></i>
                            ${this.formatDateSimple(this.currentNews.NewsDate)}</span>
                        <span><i class="bi bi-eye main-color"></i> <span class="view-count-display">${this.currentNews.ViewCount}</span></span>
                    </div>
                    <div class="dropdown ms-auto">
                        <a class="text-decoration-none text-color d-flex align-items-center" href="#" id="shareDropdown"
                            role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-share main-color me-1"></i> ${this._isArabic ? 'مشاركة' : 'Share'}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="shareDropdown">
                            <li>
                                <a class="dropdown-item d-flex align-items-center gap-2" href="#" id="sendEmail">
                                    <i class="bi bi-send main-color"></i> ${this._isArabic ? 'إرسال' : 'Send'}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center gap-2" href="#" id="copyLink">
                                    <i class="bi bi-clipboard main-color"></i> ${this._isArabic ? 'نسخ الرابط' : 'Copy Link'}
                                </a>
                            </li>
                            ${this.properties.enableVivaSharing && this.properties.selectedVivaGroup ? `
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center gap-2" href="#" id="shareToViva">
                                    <i class="bi bi-people main-color"></i> ${this._isArabic ? 'مشاركة في المجتمع' : 'Share to Community'}
                                </a>
                            </li>
                            ` : ''}
                        </ul>
                    </div>
                </div>

            </header>

            <!-- Image / Carousel for Attachments -->
            <figure class="mb-4">
                ${this.attachments.length > 1 ? `
                <div id="newsCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        ${this.attachments.map((img, i) => `
                        <div class="carousel-item ${i === 0 ? 'active' : ''}">
                            <img src="${img}" class="d-block w-100 rounded" alt="News image ${i + 1}">
                        </div>
                        `).join('')}
                    </div>
                    <button class="carousel-control-prev" type="button" data-bs-target="#newsCarousel"
                        data-bs-slide="prev">
                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Previous</span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#newsCarousel"
                        data-bs-slide="next">
                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Next</span>
                    </button>
                </div>
                ` : `
                <img src="${this.currentNews.Image}" alt="${this._isArabic ? this.currentNews.alt_AR || '' : this.currentNews.alt_EN || ''}" class="img-fluid rounded">
                `}
            </figure>

            <div class="article-content text-color">
                <p>${this._isArabic ? this.currentNews.Content_AR : this.currentNews.Content}</p>
            </div>
        </article>
    </div>

    <!-- Recent News Section -->
    <div class="col-lg-4 d-none">
        <aside class="p-4 rounded-3 shadow-sm section-card card card-color mx-2">
            <h2 class="head-color h5 mb-4 fw-bold tittle head-color ">${this._isArabic ? 'أخبار حديثة' : 'Recent News'}</h2>
            <div class="recent-news">
                ${this.newsItems.map(item => `
                <a href="/sites/intranet-qm/SitePages/NewsDetails.aspx?newsid=${item.ID}"
                    class="text-decoration-none mb-3 d-flex align-items-start gap-3">
                    <!-- Check if there are attachments for each news item -->
                    <img src="${item.attachments.length > 0 ? item.attachments[0] : require('./assets/img.jpg')}"
                        alt="News thumbnail" class="rounded" width="80">
                    <div>
                        <h3 class="h6 head-color">${this._isArabic ? item.Title_AR : item.Title}</h3>
                        <small class="text-color">${this.formatDateSimple(item.NewsDate)}</small>
                    </div>
                </a>
                `).join('')}
            </div>
        </aside>
    </div>
</div>
      `;

      this.addEventListeners();
      this.addViewCountListeners();
    } else {
      this.domElement.innerHTML = `<div>خبر غير موجود</div>`;
    }
  }

  formatDateSimple(date: string): string {
    const parsedDate = new Date(date);
    const year = parsedDate.getFullYear();
    const month = (parsedDate.getMonth() + 1).toString();
    const day = parsedDate.getDate().toString();
    return `${year}-${month}-${day}`;
  }

  addEventListeners(): void {
    const sendEmailBtn = this.domElement.querySelector('#sendEmail');
    const copyLinkBtn = this.domElement.querySelector('#copyLink');
    const shareToVivaBtn = this.domElement.querySelector('#shareToViva');

    if (sendEmailBtn) {
      sendEmailBtn.addEventListener('click', () => this.sendEmail(window.location.href));
    }

    if (copyLinkBtn) {
      copyLinkBtn.addEventListener('click', () => this.copyLink(window.location.href));
    }

    if (shareToVivaBtn) {
      shareToVivaBtn.addEventListener('click', () => this.shareToVivaCommunity());
    }
  }

  addViewCountListeners(): void {
    console.log('View count tracking initialized - will increment once per page load');
  }

  copyLink(url: string): void {
    navigator.clipboard.writeText(url).then(() => {
    }).catch((error) => {
      console.error('Error copying link:', error);
    });
  }

  sendEmail(url: string): void {
    const subject = "Check this out!";
    const body = `Here is the link to the news article: ${url}`;
    window.location.href = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  }

  async shareToVivaCommunity(): Promise<void> {
    try {
      

      // تم حذف التحقق من المجتمع المحدد لأننا نستخدم الجروب الرئيسي مباشرة

      if (!this.currentNews) {
        Swal.fire({
          icon: 'warning',
          title: this._isArabic ? 'تحذير' : 'Warning',
          text: this._isArabic ? 'لا يوجد خبر للمشاركة' : 'No news to share',
          confirmButtonText: this._isArabic ? 'موافق' : 'OK'
        });
        return;
      }

      // إنشاء محتوى المشاركة
      const newsTitle = this._isArabic ? this.currentNews.Title_AR : this.currentNews.Title;
      const newsUrl = window.location.href;

      // استخدام Microsoft Graph API لإنشاء post
      // const graphClient = await this.context.msGraphClientFactory.getClient('3');

      console.log('Sharing to community:', this.properties.selectedVivaGroup);

      // فتح المجتمع ونسخ النص للمشاركة اليدوية
      try {
        console.log('Opening manual sharing window...');

        // إنشاء محتوى جذاب للمشاركة
        const shareText = this._isArabic ?
          `🔥 خبر عاجل من متاحف قطر! 🔥\n\n📰 ${newsTitle}\n\n\n\n🔗 اقرأ التفاصيل كاملة:\n${newsUrl}\n\n#متاحف_قطر` :
          `🔥 Breaking News from Qatar Museums! 🔥\n\n📰 ${newsTitle}\n\n\n\n🔗 Read full details:\n${newsUrl}\n\n#QatarMuseums`;

        console.log('Selected group ID:', this.properties.selectedVivaGroup);

        const engageUrl = 'https://engage.cloud.microsoft/main/groups/eyJfdHlwZSI6Ikdyb3VwIiwiaWQiOiI4ODk1NDYwMTQ3MiJ9/all';

        if (navigator.clipboard) {
          await navigator.clipboard.writeText(shareText);
        }

        

        const groupName = this._isArabic ? 'مجتمع متاحف قطر الرئيسي' : 'Qatar Museums Main Community';

        if (navigator.clipboard) {
          navigator.clipboard.writeText(shareText);
          Swal.fire({
            icon: 'success',
            title: this._isArabic ? 'تم نسخ النص!' : 'Text Copied!',
            html: this._isArabic ?
              `<div style="text-align: right; direction: rtl; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                <div style="background: #e3f2fd; border-left: 4px solid #2196f3; padding: 15px; border-radius: 5px; margin: 15px 0;">
                  <p style="margin: 0 0 10px 0; color: #1976d2; font-weight: bold;">📝 خطوات المشاركة:</p>
                  <ol style="margin: 0; padding-right: 20px; color: #333;">
                    <li>اضغط "فتح Viva Engage" أدناه</li>
                    <li>ابحث عن المجتمع المطلوب</li>
                    <li>اضغط على "إنشاء منشور جديد"</li>
                    <li>الصق النص (Ctrl+V) واضغط "نشر"</li>
                  </ol>
                </div>
                <p style="text-align: center; margin: 20px 0; font-size: 16px; color: #495057;">🚀 هل تريد المتابعة لمشاركة هذا الخبر؟</p>
              </div>` :
              `<div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                
                <div style="background: #e3f2fd; border-left: 4px solid #2196f3; padding: 15px; border-radius: 5px; margin: 15px 0;">
                  <p style="margin: 0 0 10px 0; color: #1976d2; font-weight: bold;">📝 Sharing Steps:</p>
                  <ol style="margin: 0; padding-left: 20px; color: #333;">
                    <li>Click "Open Viva Engage" below</li>
                    <li>Search for the desired community</li>
                    <li>Click "Create new post"</li>
                    <li>Paste the text (Ctrl+V) and click "Post"</li>
                  </ol>
                </div>
              </div>`,
            confirmButtonText: this._isArabic ? 'فتح Viva Engage' : 'Open Viva Engage',
            cancelButtonText: this._isArabic ? 'إلغاء' : 'Cancel',
            showCancelButton: true,
            allowOutsideClick: false
          }).then((result) => {
            if (result.isConfirmed) {
              window.open(engageUrl, '_blank');
            }
          });
        } else {
          Swal.fire({
            icon: 'info',
            title: this._isArabic ? 'انسخ النص' : 'Copy Text',
            html: this._isArabic ?
              `<div style="text-align: right; direction: rtl; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center;">
                  <h3 style="margin: 0; font-size: 24px;">📢 مشاركة خبر جديد</h3>
                  <p style="margin: 10px 0 0 0; opacity: 0.9;">من موقع متاحف قطر</p>
                </div>
               
                <div style="background: #e3f2fd; border-left: 4px solid #2196f3; padding: 15px; border-radius: 5px; margin: 15px 0;">
                  <p style="margin: 0; color: #1976d2; font-weight: bold;">💡 تعليمات المشاركة:</p>
                  <p style="margin: 5px 0 0 0; color: #333;">انسخ النص أعلاه، ثم اضغط "فتح Viva Engage" للمشاركة في المجتمع</p>
                </div>
                <p style="text-align: center; margin: 20px 0; font-size: 16px; color: #495057;">🚀 هل تريد المتابعة لمشاركة هذا الخبر؟</p>
              </div>` :
              `<div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center;">
                  <h3 style="margin: 0; font-size: 24px;">📢 Share Breaking News</h3>
                  <p style="margin: 10px 0 0 0; opacity: 0.9;">From Qatar Museums Website</p>
                </div>
                <div style="background: #f8f9fa; border: 2px dashed #007bff; border-radius: 10px; padding: 15px; margin: 15px 0;">
                  <p style="margin: 0 0 10px 0; font-weight: bold; color: #007bff;">📋 Text to share in "${groupName}":</p>
                  <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; font-family: 'Courier New', monospace; font-size: 14px; line-height: 1.6; max-height: 150px; overflow-y: auto; cursor: pointer;" onclick="this.select()">${shareText}</div>
                </div>
                <div style="background: #e3f2fd; border-left: 4px solid #2196f3; padding: 15px; border-radius: 5px; margin: 15px 0;">
                  <p style="margin: 0; color: #1976d2; font-weight: bold;">💡 Sharing Instructions:</p>
                  <p style="margin: 5px 0 0 0; color: #333;">Copy the text above, then click "Open Viva Engage" to share in the community</p>
                </div>
                <p style="text-align: center; margin: 20px 0; font-size: 16px; color: #495057;">🚀 Ready to share this exciting news?</p>
              </div>`,
            confirmButtonText: this._isArabic ? 'فتح Viva Engage' : 'Open Viva Engage',
            cancelButtonText: this._isArabic ? 'إلغاء' : 'Cancel',
            showCancelButton: true,
            allowOutsideClick: false,
            width: 600
          }).then((result) => {
            if (result.isConfirmed) {
              window.open(engageUrl, '_blank');
            }
          });
        }

        console.log('Manual sharing completed successfully');
      } catch (error) {
        console.error('Error in manual sharing:', error);

        // fallback أخير: عرض النص ثم فتح Viva Engage
        const shareText = this._isArabic ?
          `🔥 خبر عاجل من متاحف قطر! 🔥\n\n📰 ${newsTitle}\n\n\n\n🔗 اقرأ التفاصيل كاملة:\n${newsUrl}\n\n#متاحف_قطر` :
          `🔥 Breaking News from Qatar Museums! 🔥\n\n📰 ${newsTitle}\n\n\n\n🔗 Read full details:\n${newsUrl}\n\n#QatarMuseums`;
        const groupName = this._isArabic ? 'مجتمع متاحف قطر الرئيسي' : 'Qatar Museums Main Community';

        Swal.fire({
          icon: 'info',
          title: this._isArabic ? 'انسخ النص' : 'Copy Text',
          html: this._isArabic ?
            `<div style="text-align: right; direction: rtl;">
              <p>انسخ النص التالي للمشاركة في "<strong>${groupName}</strong>":</p>
              <textarea readonly onclick="this.select()" style="width: 100%; height: 120px; margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-family: Arial;">${shareText}</textarea>
              <p>هل تريد فتح Viva Engage للمشاركة؟</p>
            </div>` :
            `<div>
              <p>Copy the following text to share in "<strong>${groupName}</strong>":</p>
              <textarea readonly onclick="this.select()" style="width: 100%; height: 120px; margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-family: Arial;">${shareText}</textarea>
              <p>Do you want to open Viva Engage to share?</p>
            </div>`,
          confirmButtonText: this._isArabic ? 'فتح Viva Engage' : 'Open Viva Engage',
          cancelButtonText: this._isArabic ? 'إلغاء' : 'Cancel',
          showCancelButton: true,
          allowOutsideClick: false,
          width: 600
        }).then((result) => {
          if (result.isConfirmed) {
            window.open('https://engage.cloud.microsoft/main/groups/eyJfdHlwZSI6Ikdyb3VwIiwiaWQiOiI4ODk1NDYwMTQ3MiJ9/all', '_blank');
          }
        });
      }

    } catch (error) {
      console.error('Error sharing to Viva Community:', error);
      Swal.fire({
        icon: 'error',
        title: this._isArabic ? 'خطأ' : 'Error',
        text: this._isArabic ? 'حدث خطأ أثناء المشاركة' : 'Error occurred while sharing',
        confirmButtonText: this._isArabic ? 'موافق' : 'OK'
      });
    }
  }
  async getPageTitle(serverRelativeUrl: string): Promise<string> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/getfilebyserverrelativeurl('${serverRelativeUrl}')/ListItemAllFields?$select=Title`;

    const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
    const data = await response.json();

    return data?.Title || '';
  }
  generateDynamicBreadcrumb(): IBreadcrumbItem[] {
    const path = window.location.pathname;
    const segments = path.split('/').filter(seg => seg); 
    const breadcrumbItems: IBreadcrumbItem[] = [];

    let accumulatedUrl = '';

    segments.forEach((segment, index) => {
      accumulatedUrl += `/${segment}`;

      const skipList = ['sitepages', 'pages', 'sites'];
      if (skipList.indexOf(segment.toLowerCase()) !== -1) return;

      const nameMap: { [key: string]: string } = {
        'news': 'News',
        'newsdetails': 'News Details',
      };

      const formattedTitle = nameMap[segment.toLowerCase()] ||
        decodeURIComponent(segment)
          .replace(/[-_]/g, ' ')
          .replace(/\.aspx/i, '')
          .replace(/\b\w/g, c => c.toUpperCase());

      if (index === segments.length - 1) {
        breadcrumbItems.push({
          title: formattedTitle,
          isCurrent: true
        });
      } else {
        breadcrumbItems.push({
          title: formattedTitle,
          url: accumulatedUrl
        });
      }
    });




    return breadcrumbItems;
  }
  generateBreadcrumbHtml(items: IBreadcrumbItem[]): string {
    return `
      <ol class="breadcrumb m-0">
        ${items
        .map(item => {
          if (item.isCurrent) {
            return `<li class="breadcrumb-item active" aria-current="page">${item.title}</li>`;
          } else {
            return `<li class="breadcrumb-item"><a href="${item.url}" class="btn-main-link fw-bold">${item.title}</a></li>`;
          }
        })
        .join('')}
      </ol>
    `;
  }

  


}
