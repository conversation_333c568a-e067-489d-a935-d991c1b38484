import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';
import { IPropertyPaneConfiguration, PropertyPaneDropdown, IPropertyPaneDropdownOption } from '@microsoft/sp-property-pane';
import Swal from 'sweetalert2';

export interface INewsDetailsWebPartProps {
  description: string;
  selectedVivaGroup: string;
  enableVivaSharing: boolean;
  listName: string; // New property for list name
}

export interface IBreadcrumbItem {
  title: string;
  url?: string;
  isCurrent?: boolean;
}

export default class NewsDetailsWebPart extends BaseClientSideWebPart<INewsDetailsWebPartProps> {
  private _isArabic: boolean = false;
  private _userGroups: Array<{ key: string, text: string }> = [];
   newsItems: {
    ID: number;
    Title: string;
    Title_AR: string;
    NewsDate: string;
    ViewCount: number;
    Image: string;
    Content: string;
    Content_AR: string;
    attachments: string[];
  }[] = [];

  private currentNews: {
    ID: number;
    Title: string;
    Title_AR: string;
    NewsDate: string;
    ViewCount: number;
    Image: string;
    Content: string;
    Content_AR: string;
    alt_EN: string;
    alt_AR: string;
    LikeCount: number;
    Video:{
      Url: string;
    };
    Writer?: {
      Title: string;
      EMail: string;
    };
  } | null = null;

  private attachments: string[] = [];
  private listOptions: IPropertyPaneDropdownOption[] = [];
  private listsLoaded: boolean = false;

  // Fetch available lists from the current site (Main Site or Subsite)
  private async getAvailableLists(): Promise<IPropertyPaneDropdownOption[]> {
    try {
      const currentWebUrl = this.context.pageContext.web.absoluteUrl;
      const siteUrl = this.context.pageContext.site.absoluteUrl;
      const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();
      const baseUrl = isSubSite ? currentWebUrl : siteUrl;

      const url = `${baseUrl}/_api/web/lists?$filter=Hidden eq false and BaseTemplate eq 100`;
      console.log(`Fetching lists from: ${url}`);
      const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}, Message: ${response.statusText}`);
      }
      const data = await response.json();
      const lists = data.value.map((list: any) => ({
        key: list.Title,
        text: list.Title
      }));
      console.log('Available lists:', lists);
      if (lists.length === 0) {
        console.warn('No custom lists found in the site.');
      }
      return lists;
    } catch (error) {
      console.error('Error fetching available lists:', error);
      return [{ key: '', text: this._isArabic ? 'لا توجد قوائم متاحة' : 'No lists available' }];
    }
  }

  // Property Pane Configuration
  protected getPropertyPaneConfiguration(): IPropertyPaneConfiguration {
    return {
      pages: [
        {
          header: { description: this._isArabic ? 'إعدادات الـ Web Part' : 'Web Part Settings' },
          groups: [
            {
              groupName: this._isArabic ? 'إعدادات القائمة' : 'List Settings',
              groupFields: [
                PropertyPaneDropdown('listName', {
                  label: this._isArabic ? 'اختر القائمة' : 'Select List',
                  options: this.listsLoaded ? this.listOptions : [{ key: '', text: this._isArabic ? 'جارٍ تحميل القوائم...' : 'Loading lists...' }],
                  selectedKey: this.properties.listName || ''
                }),
                
                
              ]
            }
          ]
        }
      ]
    };
  }

  private getListEndpoint(listTitle: string, selectFields?: string, expandFields?: string, filterQuery?: string, orderQuery?: string, topQuery?: string, forceMainSite?: boolean): string {
    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    const siteUrl = this.context.pageContext.site.absoluteUrl;
    const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    const baseUrl = forceMainSite
      ? `${siteUrl}/_api/web/lists/getbytitle('${encodeURIComponent(listTitle)}')/items`
      : (isSubSite
        ? `${currentWebUrl}/_api/web/lists/getbytitle('${encodeURIComponent(listTitle)}')/items`
        : `${siteUrl}/_api/web/lists/getbytitle('${encodeURIComponent(listTitle)}')/items`);

    const queryParams: string[] = [];
    if (selectFields) queryParams.push(`$select=${selectFields}`);
    if (expandFields) queryParams.push(`$expand=${expandFields}`);
    if (filterQuery) queryParams.push(filterQuery);
    if (orderQuery) queryParams.push(orderQuery);
    if (topQuery) queryParams.push(topQuery);

    return queryParams.length > 0 ? `${baseUrl}?${queryParams.join('&')}` : baseUrl;
  }

  private getAttachments(itemId: number): Promise<string[]> {
    const listName = this.properties.listName?.trim() || 'News Articles';
    if (!listName) {
      console.error('List name is empty for attachments.');
      return Promise.resolve([]);
    }

    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    const siteUrl = this.context.pageContext.site.absoluteUrl;
    const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    const baseUrl = isSubSite
      ? `${currentWebUrl}/_api/web/lists/getbytitle('${encodeURIComponent(listName)}')/items(${itemId})/AttachmentFiles`
      : `${siteUrl}/_api/web/lists/getbytitle('${encodeURIComponent(listName)}')/items(${itemId})/AttachmentFiles`;

    return this.context.spHttpClient.get(baseUrl, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => {
        if (!response.ok) {
          console.warn(`No attachments for item ${itemId}: HTTP ${response.status}`);
          return { value: [] };
        }
        return response.json();
      })
      .then((data) => {
        return data.value.map((file: any) => file.ServerRelativeUrl);
      })
      .catch(error => {
        console.error(`Error fetching attachments for item ${itemId}:`, error);
        return [];
      });
  }

  private fetchNewsDetails(newsId: number): Promise<void> {
    const listName = this.properties.listName?.trim() || 'News Articles';
    if (!listName) {
      console.error('List name is empty or invalid.');
      this.domElement.innerHTML = `<p>${this._isArabic ? 'خطأ: يرجى تحديد قائمة من إعدادات الـ Web Part.' : 'Error: Please select a list from Web Part settings.'}</p>`;
      return Promise.resolve();
    }

    const selectFields = '*,Writer/Title,Writer/EMail';
    const filterQuery = `$filter=ID eq ${newsId}`;
    const expandFields = 'Writer';

    const url = this.getListEndpoint(listName, selectFields, expandFields, filterQuery);

    return this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}, URL: ${url}`);
        }
        return response.json();
      })
      .then((data) => {
        if (data.value.length > 0) {
          const news = data.value[0];
          return this.getAttachments(news.ID).then((attachments) => {
            this.attachments = attachments;

            let imageUrl = '';
            try {
              const bannerImageData = news?.Image ? JSON.parse(news.Image) : null;
              if (bannerImageData?.serverRelativeUrl) {
                imageUrl = bannerImageData.serverRelativeUrl.startsWith('http')
                  ? bannerImageData.serverRelativeUrl
                  : `${this.context.pageContext.web.absoluteUrl}${bannerImageData.serverRelativeUrl}`;
              } else if (this.attachments.length > 0) {
                imageUrl = this.attachments[0];
              } else {
                imageUrl = require('./assets/img.jpg');
              }
            } catch (e) {
              console.error(`Error parsing Image for item ${news.ID}:`, e);
              imageUrl = this.attachments.length > 0 ? this.attachments[0] : require('./assets/img.jpg');
            }

            this.currentNews = {
              ID: news.ID,
              Video : news.Video?.Url || '',
              Title: news.Title || '',
              Title_AR: news.Title_AR || '',
              NewsDate: news.NewsDate || '',
              ViewCount: news.ViewCount || 0,
              Image: imageUrl,
              Content: news.NewsBody_EN || '',
              Content_AR: news.NewsBody_AR || '',
              alt_EN: news.alt_EN || '',
              alt_AR: news.alt_AR || '',
              LikeCount: news.Like || 0,
              Writer: {
                Title: news.Writer?.Title || '',
                EMail: news.Writer?.EMail || ''
              }
            };
            this.render();
          });
        } else {
          this.currentNews = null;
          this.domElement.innerHTML = `<p>${this._isArabic ? 'الخبر غير موجود' : 'News not found'}</p>`;
        }
      })
      .catch(error => {
        console.error(`Error fetching news details from list '${listName}':`, error);
        this.domElement.innerHTML = `<p>${this._isArabic ? `خطأ: فشل تحميل تفاصيل الخبر من القائمة '${listName}'.` : `Error: Failed to load news details from list '${listName}'.`}</p>`;
      });
  }

  private async loadUserGroups(): Promise<void> {
    try {
      this._userGroups = [
        { key: '', text: this._isArabic ? 'جاري تحميل المجتمعات...' : 'Loading communities...' }
      ];

      const graphClient = await this.context.msGraphClientFactory.getClient('3');
      console.log('Loading user communities...');

      let allCommunities: any[] = [];

      try {
        console.log('Trying to get Groups with Yammer info...');
        const groupsResponse = await graphClient
          .api('/me/memberOf')
          .select('*')
          .get();

        if (groupsResponse?.value?.length > 0) {
          const yammerGroups = groupsResponse.value.filter((group: any) =>
            group.groupTypes?.includes('Unified') &&
            group.mailEnabled &&
            group.resourceProvisioningOptions?.includes('Team')
          );

          if (yammerGroups.length > 0) {
            for (const group of yammerGroups) {
              try {
                const yammerInfo = await graphClient
                  .api(`/groups/${group.id}`)
                  .select('id,displayName,description,externalId')
                  .get();

                allCommunities.push({
                  id: group.id,
                  displayName: group.displayName,
                  description: group.description,
                  type: 'yammer-group',
                  externalId: yammerInfo.externalId || group.id
                });
              } catch (yammerInfoError) {
                allCommunities.push({
                  ...group,
                  type: 'group'
                });
              }
            }
            console.log('Groups with Yammer loaded:', yammerGroups.length);
          }
        }
      } catch (groupsError) {
        console.log('Groups API failed:', groupsError);
      }

      try {
        const teamsResponse = await graphClient
          .api('/me/joinedTeams')
          .select('id,displayName,description')
          .get();

        if (teamsResponse?.value?.length > 0) {
          allCommunities.push(...teamsResponse.value.map((team: any) => ({
            ...team,
            type: 'team'
          })));
          console.log('Teams loaded:', teamsResponse.value.length);
        }
      } catch (teamsError) {
        console.log('Teams API failed:', teamsError);
      }

      try {
        const groupsResponse = await graphClient
          .api('/me/memberOf')
          .select('*')
          .get();

        if (groupsResponse?.value?.length > 0) {
          const unifiedGroups = groupsResponse.value.filter((group: any) =>
            group.groupTypes?.includes('Unified') && group.mailEnabled
          );

          if (unifiedGroups.length > 0) {
            allCommunities.push(...unifiedGroups.map((group: any) => ({
              ...group,
              type: 'group'
            })));
            console.log('Groups loaded:', unifiedGroups.length);
          }
        }
      } catch (groupsError) {
        console.log('Groups API failed:', groupsError);
      }

      if (allCommunities.length > 0) {
        const uniqueCommunities = allCommunities.filter((community, index, self) =>
          index === self.findIndex(c => c.id === community.id)
        );

        this._userGroups = uniqueCommunities
          .sort((a: any, b: any) => a.displayName.localeCompare(b.displayName))
          .map((community: any) => ({
            key: community.id,
            text: `${community.displayName} (${community.type === 'team' ? 'Teams' : 'Group'})`
          }));

        console.log('Communities loaded:', this._userGroups.length);
      } else {
        this._userGroups = [
          { key: '', text: this._isArabic ? 'لا توجد مجتمعات متاحة' : 'No communities available' }
        ];
      }

      if (this.context.propertyPane.isPropertyPaneOpen()) {
        this.context.propertyPane.refresh();
      }
    } catch (error) {
      console.error('Error loading communities:', error);
      this._userGroups = [
        { key: '', text: this._isArabic ? 'خطأ في تحميل المجتمعات' : 'Error loading communities' }
      ];
    }
  }

  private incrementViewCountOnce(newsId: number): void {
    const sessionKey = `news_viewed_${newsId}`;
    const hasViewed = sessionStorage.getItem(sessionKey);

    if (!hasViewed) {
      sessionStorage.setItem(sessionKey, 'true');
      this.incrementViewCount(newsId);
      console.log(`First view in this session for news ID: ${newsId}`);
    } else {
      console.log(`News ID ${newsId} already viewed in this session - not incrementing`);
    }
  }

  private incrementViewCount(newsId: number): Promise<void> {
    const listName = this.properties.listName?.trim() || 'News Articles';
    if (!listName) {
      console.error('List name is empty or invalid.');
      return Promise.resolve();
    }

    const getCurrentViewCountUrl = this.getListEndpoint(
      listName,
      'ViewCount',
      undefined,
      `$filter=ID eq ${newsId}`
    );

    return this.context.spHttpClient.get(getCurrentViewCountUrl, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
      })
      .then((data: any) => {
        const currentViewCount = data.value && data.value.length > 0 ? (data.value[0].ViewCount || 0) : 0;
        const newViewCount = currentViewCount + 1;

        const updateUrl = this.getListEndpoint(listName) + `(${newsId})`;

        const updateBody = JSON.stringify({
          'ViewCount': newViewCount
        });

        return this.context.spHttpClient.post(updateUrl, SPHttpClient.configurations.v1, {
          headers: {
            'Accept': 'application/json;odata=nometadata',
            'Content-Type': 'application/json;odata=nometadata',
            'odata-version': '',
            'IF-MATCH': '*',
            'X-HTTP-Method': 'MERGE'
          },
          body: updateBody
        }).then((updateResponse: SPHttpClientResponse) => {
          if (updateResponse.ok || updateResponse.status === 204) {
            if (this.currentNews) {
              this.currentNews.ViewCount = newViewCount;
              const viewCountElement = this.domElement.querySelector('.view-count-display');
              if (viewCountElement) {
                viewCountElement.textContent = newViewCount.toString();
              }
            }
            console.log(`View count updated to ${newViewCount} for news ID: ${newsId}`);
          } else {
            console.error(`Failed to update view count: ${updateResponse.status} ${updateResponse.statusText}`);
          }
        });
      })
      .catch(error => {
        console.error(`Error incrementing view count for list '${listName}':`, error);
      });
  }

  private async toggleLike(newsId: number): Promise<void> {
    const listName = this.properties.listName?.trim() || 'News Articles';
    if (!listName) {
      console.error('List name is empty or invalid.');
      return Promise.resolve();
    }

    const sessionKey = `news_liked_${newsId}`;
    const hasLiked = sessionStorage.getItem(sessionKey);

    const getCurrentLikeCountUrl = this.getListEndpoint(
      listName,
      'Like',
      undefined,
      `$filter=ID eq ${newsId}`
    );

    return this.context.spHttpClient.get(getCurrentLikeCountUrl, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
      })
      .then((data: any) => {
        const currentLikeCount = data.value && data.value.length > 0 ? (data.value[0].Like || 0) : 0;
        let newLikeCount = currentLikeCount;

        if (hasLiked) {
          newLikeCount = Math.max(currentLikeCount - 1, 0);
          sessionStorage.removeItem(sessionKey);
          console.log(`Unliked news ID: ${newsId}, new like count: ${newLikeCount}`);
        } else {
          newLikeCount = currentLikeCount + 1;
          sessionStorage.setItem(sessionKey, 'true');
          console.log(`Liked news ID: ${newsId}, new like count: ${newLikeCount}`);
        }

        const updateUrl = this.getListEndpoint(listName) + `(${newsId})`;

        const updateBody = JSON.stringify({
          'Like': newLikeCount
        });

        return this.context.spHttpClient.post(updateUrl, SPHttpClient.configurations.v1, {
          headers: {
            'Accept': 'application/json;odata=nometadata',
            'Content-Type': 'application/json;odata=nometadata',
            'odata-version': '',
            'IF-MATCH': '*',
            'X-HTTP-Method': 'MERGE'
          },
          body: updateBody
        }).then((updateResponse: SPHttpClientResponse) => {
          if (updateResponse.ok || updateResponse.status === 204) {
            if (this.currentNews) {
              this.currentNews.LikeCount = newLikeCount;
              this.render();
            }
            console.log(`Like count updated to ${newLikeCount} for news ID: ${newsId}`);
          } else {
            console.error(`Failed to update like count: ${updateResponse.status} ${updateResponse.statusText}`);
          }
        });
      })
      .catch(error => {
        console.error(`Error toggling like count for list '${listName}':`, error);
      });
  }

  protected async onInit(): Promise<void> {
    try {
      this._isArabic = window.location.pathname.toLowerCase().includes('/ar/');
      this.listOptions = await this.getAvailableLists();
      this.listsLoaded = true;
      await this.loadUserGroups();

      const urlParams = new URLSearchParams(window.location.search);
      const newsId = urlParams.get('newsid');

      await Promise.all([
        this.fetchNewsDetails(Number(newsId))
      ]);

      if (newsId) {
        this.incrementViewCountOnce(Number(newsId));
      }

      this.render();
    } catch (error) {
      console.error('Error in onInit:', error);
      this.domElement.innerHTML = `<p>${this._isArabic ? 'خطأ: فشل تهيئة الـ Web Part.' : 'Error: Failed to initialize Web Part.'}</p>`;
    }
  }

  public render(): void {
    if (this.currentNews) {
      const isArabic = this._isArabic;
      const sessionKey = `news_liked_${this.currentNews.ID}`;
      const hasLiked = !!sessionStorage.getItem(sessionKey);
      const isNewsDetailsPage = window.location.pathname.toLowerCase().endsWith('/sites/intranet-qm/sitepages/newsdetails.aspx'); 
      this.domElement.innerHTML = `
        
        <div class="row news-details">
          <div class="col-lg-12">
            <article class="news-article card card-color section-card mx-2 p-4 rounded-3 shadow-sm">
              <header class="mb-4">
                <h2 class="head-color">${isArabic ? this.currentNews.Title_AR : this.currentNews.Title}</h2>
                <hr>
                <div class="meta d-flex align-items-center gap-3 text-color">
                  ${isNewsDetailsPage ? `` : `<small>
                    <div class="dropdown d-inline-block">
                      <a class="text-decoration-none text-color" href="#" id="authorDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                       ${this.currentNews.Writer?.Title ? `
                        Author By ${this.currentNews.Writer?.Title || ''}`
                        : ``}
                       
                        
                      </a>
                      <ul class="dropdown-menu shadow" aria-labelledby="authorDropdown">
                        <li>
                          <a class="dropdown-item d-flex align-items-center gap-2" href="#" id="emailAuthor">
                            <i class="bi bi-envelope main-color"></i> ${isArabic ? 'إرسال بريد إلكتروني' : 'Send Email'}
                          </a>
                        </li>
                        <li>
                          <a class="dropdown-item d-flex align-items-center gap-2" href="#" id="teamsAuthor">
                            <i class="bi bi-microsoft-teams main-color"></i> ${isArabic ? 'مراسلة عبر Teams' : 'Message on Teams'}
                          </a>
                        </li>
                      </ul>
                    </div>
                  </small>`}
                  <div>
                    <span><i class="bi bi-calendar main-color"></i> ${this.formatDateSimple(this.currentNews.NewsDate)}</span>
                    <span><i class="bi bi-eye main-color"></i> <span class="view-count-display"> ${this.currentNews.ViewCount}</span></span>
                  </div>
                  <div class="dropdown ms-auto d-flex">
                    <span class="like-container" style="cursor: pointer;" id="likeButton">
                      <i class="bi ${hasLiked ? 'bi-heart-fill' : 'bi-heart'} main-color me-1"></i>
                      <span class="like-count-display">${this.currentNews.LikeCount}</span>
                    </span>
                    <a class="ms-2 text-decoration-none text-color d-flex align-items-center" href="#" id="shareDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                      <i class="bi bi-share main-color me-1"></i> ${isArabic ? 'مشاركة' : 'Share'}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="shareDropdown">
                      <li>
                        <a class="dropdown-item d-flex align-items-center gap-2" href="#" id="sendEmail">
                          <i class="bi bi-send main-color"></i> ${isArabic ? 'إرسال' : 'Send'}
                        </a>
                      </li>
                      <li>
                        <a class="dropdown-item d-flex align-items-center gap-2" href="#" id="copyLink">
                          <i class="bi bi-clipboard main-color"></i> ${isArabic ? 'نسخ الرابط' : 'Copy Link'}
                        </a>
                      </li>
                      
                      <li><hr class="dropdown-divider"></li>
                      <li>
                        <a class="dropdown-item d-flex align-items-center gap-2" href="#" id="shareToViva">
                          <i class="bi bi-people main-color"></i> ${isArabic ? 'مشاركة في المجتمع' : 'Share to Community'}
                        </a>
                      </li>
                      
                    </ul>
                  </div>
                </div>
              </header>
              <div class="article-content text-color">
              
              <figure class="">
                ${this.attachments.length > 1 ? `
                <div id="newsCarousel" class="carousel slide news-details-img" data-bs-ride="carousel">
                  <div class="carousel-inner">
                    ${this.attachments.map((img, i) => `
                    <div class="carousel-item ${i === 0 ? 'active' : ''}">
                      <img src="${img}" class="d-block w-100 rounded" alt="${isArabic ? this.currentNews?.alt_AR || `News image ${i + 1}` : this.currentNews?.alt_EN || `News image ${i + 1}`}">
                    </div>
                    `).join('')}
                  </div>
                  <button class="carousel-control-prev" type="button" data-bs-target="#newsCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">${isArabic ? 'السابق' : 'Previous'}</span>
                  </button>
                  <button class="carousel-control-next" type="button" data-bs-target="#newsCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">${isArabic ? 'التالي' : 'Next'}</span>
                  </button>
                </div>
                ` : `
                <img src="${this.currentNews.Image}" alt="${isArabic ? this.currentNews.alt_AR || '' : this.currentNews.alt_EN || ''}" class="img-fluid rounded news-details-img">
                `}
              </figure>
              
                <p>${isArabic ? this.currentNews.Content_AR : this.currentNews.Content}</p>
                ${this.currentNews.Video?.Url ? `
                  <video id="video" class="video" controls loop>
                   <source src="${this.currentNews.Video?.Url}" type="video/mp4">
                   Your browser does not support the video tag.
                   </video>
              
                ` : ''} 
                </div>
            </article>
          </div>
        </div>
      `;

      this.addEventListeners();
      this.addViewCountListeners();
    } else {
      this.domElement.innerHTML = `<div>${this._isArabic ? 'الخبر غير موجود' : 'News not found'}</div>`;
    }
  }

  private formatDateSimple(date: string): string {
    try {
      if (!date) return '';
      const parsedDate = new Date(date);
      if (isNaN(parsedDate.getTime())) return '';
      const year = parsedDate.getFullYear();
      const month = (parsedDate.getMonth() + 1).toString().padStart(2, '0');
      const day = parsedDate.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  }

  private addEventListeners(): void {
    const sendEmailBtn = this.domElement.querySelector('#sendEmail');
    const copyLinkBtn = this.domElement.querySelector('#copyLink');
    const shareToVivaBtn = this.domElement.querySelector('#shareToViva');
    const emailAuthorBtn = this.domElement.querySelector('#emailAuthor');
    const teamsAuthorBtn = this.domElement.querySelector('#teamsAuthor');
    const likeButton = this.domElement.querySelector('#likeButton');

    if (sendEmailBtn) {
      sendEmailBtn.addEventListener('click', () => this.sendEmail(window.location.href));
    }

    if (copyLinkBtn) {
      copyLinkBtn.addEventListener('click', () => this.copyLink(window.location.href));
    }

    if (shareToVivaBtn) {
      shareToVivaBtn.addEventListener('click', () => this.shareToVivaCommunity());
    }

    if (emailAuthorBtn && this.currentNews?.Writer?.EMail) {
      emailAuthorBtn.addEventListener('click', () => this.sendEmailToAuthor(this.currentNews!.Writer!.EMail));
    }

    if (teamsAuthorBtn && this.currentNews?.Writer?.EMail) {
      teamsAuthorBtn.addEventListener('click', () => this.openTeamsChat(this.currentNews!.Writer!.EMail));
    }

    if (likeButton && this.currentNews) {
      likeButton.addEventListener('click', () => this.toggleLike(this.currentNews!.ID));
    }
  }

  private addViewCountListeners(): void {
    console.log('View count tracking initialized - will increment once per page load');
  }

  private copyLink(url: string): void {
    navigator.clipboard.writeText(url).then(() => {
      console.log('Link copied to clipboard');
    }).catch((error) => {
      console.error('Error copying link:', error);
    });
  }

  private sendEmail(url: string): void {
    const subject = this._isArabic ? 'تحقق من هذا الخبر!' : 'Check this out!';
    const body = this._isArabic ? `إليك رابط مقال الخبر: ${url}` : `Here is the link to the news article: ${url}`;
    window.location.href = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  }

  private sendEmailToAuthor(email: string): void {
    const subject = this._isArabic ? 'سؤال حول مقالة الأخبار' : 'Question about the news article';
    const body = this._isArabic
      ? `مرحبًا،\n\nلدي سؤال حول مقالة الأخبار: ${window.location.href}`
      : `Hello,\n\nI have a question about the news article: ${window.location.href}`;
    window.location.href = `mailto:${encodeURIComponent(email)}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  }

  private openTeamsChat(email: string): void {
    const teamsUrl = `https://teams.microsoft.com/l/chat/0/0?users=${encodeURIComponent(email)}`;
    window.open(teamsUrl, '_blank');
  }

  private async shareToVivaCommunity(): Promise<void> {
    try {
      if (!this.currentNews) {
        Swal.fire({
          icon: 'warning',
          title: this._isArabic ? 'تحذير' : 'Warning',
          text: this._isArabic ? 'لا يوجد خبر للمشاركة' : 'No news to share',
          confirmButtonText: this._isArabic ? 'موافق' : 'OK'
        });
        return;
      }

      const newsTitle = this._isArabic ? this.currentNews.Title_AR : this.currentNews.Title;
      const newsUrl = window.location.href;
      const shareText = this._isArabic
        ? `🔥 خبر عاجل من متاحف قطر! 🔥\n\n📰 ${newsTitle}\n\n\n\n🔗 اقرأ التفاصيل كاملة:\n${newsUrl}\n\n#متاحف_قطر`
        : `🔥 Breaking News from Qatar Museums! 🔥\n\n📰 ${newsTitle}\n\n\n\n🔗 Read full details:\n${newsUrl}\n\n#QatarMuseums`;

      const groupName = this._isArabic ? 'مجتمع متاحف قطر الرئيسي' : 'Qatar Museums Main Community';
      const engageUrl = 'https://engage.cloud.microsoft/main/groups/eyJfdHlwZSI6Ikdyb3VwIiwiaWQiOiI4ODk1NDYwMTQ3MiJ9/all';

      if (navigator.clipboard) {
        await navigator.clipboard.writeText(shareText);
        Swal.fire({
          icon: 'success',
          title: this._isArabic ? 'تم نسخ النص!' : 'Text Copied!',
          html: this._isArabic
            ? `<div style="text-align: right; direction: rtl; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                <div style="background: #e3f2fd; border-left: 4px solid #2196f3; padding: 15px; border-radius: 5px; margin: 15px 0;">
                  <p style="margin: 0 0 10px 0; color: #1976d2; font-weight: bold;">📝 خطوات المشاركة:</p>
                  <ol style="margin: 0; padding-right: 20px; color: #333;">
                    <li>اضغط "فتح Viva Engage" أدناه</li>
                    <li>ابحث عن المجتمع المطلوب</li>
                    <li>اضغط على "إنشاء منشور جديد"</li>
                    <li>الصق النص (Ctrl+V) واضغط "نشر"</li>
                  </ol>
                </div>
                <p style="text-align: center; margin: 20px 0; font-size: 16px; color: #495057;">🚀 هل تريد المتابعة لمشاركة هذا الخبر؟</p>
              </div>`
            : `<div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                <div style="background: #e3f2fd; border-left: 4px solid #2196f3; padding: 15px; border-radius: 5px; margin: 15px 0;">
                  <p style="margin: 0 0 10px 0; color: #1976d2; font-weight: bold;">📝 Sharing Steps:</p>
                  <ol style="margin: 0; padding-left: 20px; color: #333;">
                    <li>Click "Open Viva Engage" below</li>
                    <li>Search for the desired community</li>
                    <li>Click "Create new post"</li>
                    <li>Paste the text (Ctrl+V) and click "Post"</li>
                  </ol>
                </div>
                <p style="text-align: center; margin: 20px 0; font-size: 16px; color: #495057;">🚀 Ready to share this exciting news?</p>
              </div>`,
          confirmButtonText: this._isArabic ? 'فتح Viva Engage' : 'Open Viva Engage',
          cancelButtonText: this._isArabic ? 'إلغاء' : 'Cancel',
          showCancelButton: true,
          allowOutsideClick: false
        }).then((result) => {
          if (result.isConfirmed) {
            window.open(engageUrl, '_blank');
          }
        });
      } else {
        Swal.fire({
          icon: 'info',
          title: this._isArabic ? 'انسخ النص' : 'Copy Text',
          html: this._isArabic
            ? `<div style="text-align: right; direction: rtl; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center;">
                  <h3 style="margin: 0; font-size: 24px;">📢 مشاركة خبر جديد</h3>
                  <p style="margin: 10px 0 0 0; opacity: 0.9;">من موقع متاحف قطر</p>
                </div>
                <div style="background: #f8f9fa; border: 2px dashed #007bff; border-radius: 10px; padding: 15px; margin: 15px 0;">
                  <p style="margin: 0 0 10px 0; font-weight: bold; color: #007bff;">📋 النص للمشاركة في "${groupName}":</p>
                  <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; font-family: 'Courier New', monospace; font-size: 14px; line-height: 1.6; max-height: 150px; overflow-y: auto; cursor: pointer;" onclick="this.select()">${shareText}</div>
                </div>
                <div style="background: #e3f2fd; border-left: 4px solid #2196f3; padding: 15px; border-radius: 5px; margin: 15px 0;">
                  <p style="margin: 0; color: #1976d2; font-weight: bold;">💡 تعليمات المشاركة:</p>
                  <p style="margin: 5px 0 0 0; color: #333;">انسخ النص أعلاه، ثم اضغط "فتح Viva Engage" للمشاركة في المجتمع</p>
                </div>
                <p style="text-align: center; margin: 20px 0; font-size: 16px; color: #495057;">🚀 هل تريد المتابعة لمشاركة هذا الخبر؟</p>
              </div>`
            : `<div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center;">
                  <h3 style="margin: 0; font-size: 24px;">📢 Share Breaking News</h3>
                  <p style="margin: 10px 0 0 0; opacity: 0.9;">From Qatar Museums Website</p>
                </div>
                <div style="background: #f8f9fa; border: 2px dashed #007bff; border-radius: 10px; padding: 15px; margin: 15px 0;">
                  <p style="margin: 0 0 10px 0; font-weight: bold; color: #007bff;">📋 Text to share in "${groupName}":</p>
                  <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; font-family: 'Courier New', monospace; font-size: 14px; line-height: 1.6; max-height: 150px; overflow-y: auto; cursor: pointer;" onclick="this.select()">${shareText}</div>
                </div>
                <div style="background: #e3f2fd; border-left: 4px solid #2196f3; padding: 15px; border-radius: 5px; margin: 15px 0;">
                  <p style="margin: 0; color: #1976d2; font-weight: bold;">💡 Sharing Instructions:</p>
                  <p style="margin: 5px 0 0 0; color: #333;">Copy the text above, then click "Open Viva Engage" to share in the community</p>
                </div>
                <p style="text-align: center; margin: 20px 0; font-size: 16px; color: #495057;">🚀 Ready to share this exciting news?</p>
              </div>`,
          confirmButtonText: this._isArabic ? 'فتح Viva Engage' : 'Open Viva Engage',
          cancelButtonText: this._isArabic ? 'إلغاء' : 'Cancel',
          showCancelButton: true,
          allowOutsideClick: false,
          width: 600
        }).then((result) => {
          if (result.isConfirmed) {
            window.open(engageUrl, '_blank');
          }
        });
      }
    } catch (error) {
      console.error('Error sharing to Viva Community:', error);
      Swal.fire({
        icon: 'error',
        title: this._isArabic ? 'خطأ' : 'Error',
        text: this._isArabic ? 'حدث خطأ أثناء المشاركة' : 'Error occurred while sharing',
        confirmButtonText: this._isArabic ? 'موافق' : 'OK'
      });
    }
  }

 
}