import { override } from '@microsoft/decorators';
import { Log } from '@microsoft/sp-core-library';
import {
  BaseApplicationCustomizer
} from '@microsoft/sp-application-base';

const LOG_SOURCE: string = 'GlobalCustomizerApplicationCustomizer';

export interface IGlobalCustomizerApplicationCustomizerProperties {}

export default class GlobalCustomizerApplicationCustomizer
  extends BaseApplicationCustomizer<IGlobalCustomizerApplicationCustomizerProperties> {

  private _initialized = false;

  @override
  public onInit(): Promise<void> {
    this.context.application.navigatedEvent.add(this, () => {
      const currentPath = window.location.pathname.toLowerCase();
      const siteRoot = "/sites/intranet-qm/";
      const redirectPath = "/sites/intranet-qm/SitePages/TopicHome.aspx";
    
      if (currentPath === siteRoot || currentPath === siteRoot.slice(0, -1)) {
        window.location.href = `${window.location.origin}${redirectPath}`;
      }
    });
    
    Log.info(LOG_SOURCE, `Initialized GlobalCustomizer`);

    this._handleRouting(window.location.pathname.toLowerCase());

    this.context.application.navigatedEvent.add(this, () => {
      const newPath = window.location.pathname.toLowerCase();
      this._handleRouting(newPath);
    });

    return Promise.resolve();
  }

  private _handleRouting(currentPath: string): void {
    if (this._initialized) return; // prevent duplicate injection
    this._initialized = true;

    const siteRoot = "/sites/intranet-qm/";
    const redirectPath = "/sites/intranet-qm/SitePages/TopicHome.aspx";

    if (
      currentPath === siteRoot ||
      currentPath === siteRoot.slice(0, -1)
    ) {
      window.location.href = `${window.location.origin}${redirectPath}`;
      return;
    }

    if (!currentPath.includes('/ar/') && currentPath.includes('/sitepages/')) {
      const userLang = navigator.language.toLowerCase();
      const isArabic = userLang.startsWith('ar');
      if (isArabic) {
        const newPath = currentPath.replace('/sitepages/', '/SitePages/ar/');
        window.location.href = `${window.location.origin}${newPath}`;
        return;
      }
    }

    const prefersDark = window.matchMedia &&
      window.matchMedia('(prefers-color-scheme: dark)').matches;
    document.body.classList.add(prefersDark ? 'dark-mode' : 'light-mode');

    const cssLinks: string[] = [
      'https://8bzfr2.sharepoint.com/sites/intranet-qm/SiteAssets/GlobalStyles/style.css',
      'https://8bzfr2.sharepoint.com/sites/intranet-qm/SiteAssets/GlobalStyles/canvas.css',
      'https://8bzfr2.sharepoint.com/sites/intranet-qm/SiteAssets/GlobalStyles/responsive.css',
      'https://8bzfr2.sharepoint.com/sites/intranet-qm/SiteAssets/GlobalStyles/custom-icons.css',
      'https://8bzfr2.sharepoint.com/sites/intranet-qm/SiteAssets/GlobalStyles/custom-style.css',
    ];

    const isArabic = currentPath.includes('/ar/');
    if (isArabic) {
      cssLinks.push('https://8bzfr2.sharepoint.com/sites/intranet-qm/SiteAssets/GlobalStyles/rtl.style.css');
    }

    cssLinks.forEach(linkUrl => {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.type = 'text/css';
      link.href = linkUrl;
      document.head.appendChild(link);
    });

    const jsLinks: string[] = [
      'https://8bzfr2.sharepoint.com/sites/intranet-qm/SiteAssets/GlobalScripts/main.js',
    ];

    jsLinks.forEach(scriptUrl => {
      const script = document.createElement('script');
      script.src = scriptUrl;
      script.type = 'text/javascript';
      script.async = true;
      document.head.appendChild(script);
    });

    const footerHtml = `
      <footer class="mt-4 container-fluid text-center py-3 bg-dark text-white">
        <span>
          © <a class="text-decoration-none footer-text" href="https://qm.org.qa/en/" target="_blank">Qatar Museums</a>
          <span id="currentYear">${new Date().getFullYear()}</span>
        </span>
        <span class="mx-4">
          <a href="#" class="text-decoration-none text-white">Disclaimer</a>
        </span>
      </footer>
    `;

    const footerContainer = document.createElement('div');
    footerContainer.innerHTML = footerHtml;
    document.body.appendChild(footerContainer);
  }
}
