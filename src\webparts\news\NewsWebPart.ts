import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';



export interface INewsWebPartProps {
  description: string;
}

export default class NewsWebPart extends BaseClientSideWebPart<INewsWebPartProps> {
  newsItems: {
    Id: number;
    Title: string;
    Title_AR: string;
    NewsDate: string;
    ViewCount: number;
    Image: string;
    ShowOnHomepage: boolean;
    IsFeatured: boolean;
  }[] = [];
  _isArabic: boolean = false;

  render(): void {
    const secondaryItems = this.newsItems;


    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;




    // Secondary news items HTML
    const secondaryHTML = secondaryItems.length > 0 ? `
      <div id="newsVerticalCarousel" class="vertical-carousel">
        <div class="vertical-carousel-inner">
          ${secondaryItems.map((item, index) => `
            <div class="vertical-carousel-item ${index === 0 ? 'active' : ''}">
              <div class="news-hero-item news-overlay d-block">
                <img src="${item.Image || ''}" alt="${item.Title || ''}">
                <div class="news-hero-caption">
                  <a href="/sites/intranet-qm/SitePages/NewsDetails.aspx?newsid=${item.Id}"
                    class="text-decoration-none text-white main-color-hover">
                    <h2>${this._isArabic ? item.Title_AR : item.Title}</h2>
                  </a>
                </div>
              </div>
            </div>
          `).join('')}
        </div>
  
        <div class="vertical-carousel-arrows">
          <button class="carousel-arrow-up" data-slide="prev" aria-label="Previous slide">
            <i class="bi bi-arrow-up-circle"></i>
          </button>
          <button class="carousel-arrow-down" data-slide="next" aria-label="Next slide">
            <i class="bi bi-arrow-down-circle"></i>
          </button>
        </div>
      </div>

    ` : '';


    this.domElement.innerHTML = `
      <section class="news-section f4-news press-releases">
        <div class="card section-card card-color p-3">
          <div class="container-fluid">
            <h2 class="fw-bold tittle head-color">${this._isArabic ? 'البيانات الصحفية' : 'Press Releases'}</h2>
  
            <section class=" mt-3">
              <div class="news-hero">
                ${secondaryHTML}
              </div>
            </section>
            </div>
            <hr>
          <div class="text-end mx-3">
            <a href="/sites/intranet-qm/SitePages/${this._isArabic ? 'ar/' : ''}News.aspx"
              class="text-decoration-none fw-bold btn-main-link">
              ${this._isArabic ? 'عرض الكل <i class="bi bi-arrow-left-circle"></i>' : 'View All <i class="bi bi-arrow-right-circle"></i>'} 
            </a>

          </div>
        </div>
      </section>
    `;

    this.initCarousel();
  }



  private getListEndpoint(listTitle: string, selectFields?: string, expandFields?: string, filterQuery?: string, orderQuery?: string, topQuery?: string, forceMainSite?: boolean): string {
    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    const siteUrl = this.context.pageContext.site.absoluteUrl;
    const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    // للأخبار: نقرأ من main site والـ subsites
    const baseUrl = forceMainSite
      ? `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
      : (isSubSite
        ? `${currentWebUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
        : `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`);

    const queryParams: string[] = [];

    if (selectFields) {
      queryParams.push(`$select=${selectFields}`);
    }

    if (expandFields) {
      queryParams.push(`$expand=${expandFields}`);
    }

    if (filterQuery) {
      queryParams.push(filterQuery);
    }

    if (orderQuery) {
      queryParams.push(orderQuery);
    }

    if (topQuery) {
      queryParams.push(topQuery);
    }


    return queryParams.length > 0
      ? `${baseUrl}?${queryParams.join('&')}`
      : baseUrl;
  }

  fetchNewsData(): Promise<void> {

    const listName = 'News Articles';
    const selectFields = 'Id,Title,Title_AR,NewsDate,ViewCount,Image,ShowOnHomepage,IsFeatured';
    const orderQuery = '$orderby=NewsDate desc';
    const topQuery = '$top=10';

    const url = this.getListEndpoint(listName, selectFields, undefined, undefined, orderQuery, topQuery);


    return this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => response.json())
      .then((data) => {
        return Promise.all(data.value.map((item: any) => {
          return this.getAttachments(item.Id).then((attachments) => {
            let ImageNews = '';
            const bannerImageData = item?.Image ? JSON.parse(item?.Image) : null;

            if (bannerImageData?.serverUrl && bannerImageData?.serverRelativeUrl) {
              ImageNews = bannerImageData.serverRelativeUrl;
            } else if (attachments.length > 0) {
              ImageNews = attachments[0];
            } else {
              ImageNews = require('./assets/img.jpg');
            }

            return {
              Id: item.Id,
              Image: ImageNews,
              Title: item.Title || '',
              Title_AR: item.Title_AR || '',
              NewsDate: item.NewsDate,
              ViewCount: item.ViewCount || 0,
              ShowOnHomepage: item.ShowOnHomepage,
              IsFeatured: item.IsFeatured
            };
          });
        }));
      })
      .then((newsItemsWithImages) => {
        const homepageItems = newsItemsWithImages
          .filter(item => item.ShowOnHomepage)
          .sort((a, b) => new Date(b.NewsDate).getTime() - new Date(a.NewsDate).getTime())
          .slice(0, 3);

        this.newsItems = homepageItems;


        this.render();
      })

      .catch(error => {
        console.error('Error fetching news data or attachments:', error);
      });
  }


  onInit(): Promise<void> {
    return Promise.all([
      this.fetchNewsData(),

    ]).then(() => { });
  }


  formatDateSimple(date: string): string {
    const parsedDate = new Date(date);
    const year = parsedDate.getFullYear();
    const month = (parsedDate.getMonth() + 1).toString();
    const day = parsedDate.getDate().toString();
    return `${year}-${month}-${day}`;
  }

  initCarousel(): void {
    const carousel = document.querySelector('.vertical-carousel');
    if (!carousel) return;

    const slides = carousel.querySelectorAll('.vertical-carousel-item') as NodeListOf<HTMLElement>;
    const totalSlides = slides.length;
    let currentSlide = 0;

    // Initialize slide positions
    function initSlides() {
      slides.forEach((slide, index) => {
        slide.style.transform = `translateY(${index * 100}%)`;
      });
    }

    // Go to specific slide
    function goToSlide(index: any) {
      if (index < 0) index = totalSlides - 1;
      if (index >= totalSlides) index = 0;

      slides.forEach((slide, i) => {
        slide.style.transform = `translateY(${(i - index) * 100}%)`;
        slide.classList.toggle('active', i === index);
      });

      currentSlide = index;
    }

    // Add arrow button click handlers
    const upArrow = carousel.querySelector('.carousel-arrow-up');
    const downArrow = carousel.querySelector('.carousel-arrow-down');
    if (upArrow) {
      upArrow.addEventListener('click', () => {
        goToSlide(currentSlide - 1);
      });
    }
    if (downArrow) {
      downArrow.addEventListener('click', () => {
        goToSlide(currentSlide + 1);
      });
    }

    // Initialize the carousel
    initSlides();
    goToSlide(0);
  }
  getAttachments(itemId: number): Promise<string[]> {
    const listName = "News Articles";

    // استخدام نفس الـ pattern للقراءة من main site والـ subsites
    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    const siteUrl = this.context.pageContext.site.absoluteUrl;
    const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    const baseUrl = isSubSite
      ? `${currentWebUrl}/_api/web/lists/getbytitle('${listName}')/items(${itemId})/AttachmentFiles`
      : `${siteUrl}/_api/web/lists/getbytitle('${listName}')/items(${itemId})/AttachmentFiles`;


    return this.context.spHttpClient.get(baseUrl, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        const attachments: string[] = data.value.map((file: any) => {
          return `${file.ServerRelativeUrl}`;
        });
        return attachments;
      })

  }

}
