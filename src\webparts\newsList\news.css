:root {
  --pink: #ed3b88;
  --orange: #FFC200;
  --blue: #1292E5;
  --black: #000;
  --white: #fff;
  --gray: #cfcdcd;
  --main-color: var(--pink);
  --bg-color: #F7F7F7;
  --text-color: #333;
  --border-color: var(--gray);
  --light-border: #f1f1f1;
  --head-color: var(--black);
  --card-color: white;
  --selection-border: #333;
  --wb-color: #333;
  --bw-color: #fff;
  /* --bs-btn-active-bg: var(--main-color); */
  --bs-border-color: var(--border-color);
  --bs-nav-tabs-link-active-border-color: var(--border-color);

}

.text-primary,
.main-color {
  color: var(--main-color) !important;
}

.news-overlay {
  position: relative;
  display: block;
  height: 100%;
}

.news-overlay img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.news-hero-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px;
  background: rgba(0, 0, 0, 0.5);
  color: white;

}

.news-hero-caption h2 {
  font-size: 16px;

}

.news-col {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 15px;
}

.news-right-top {
  flex: 1;
}

.news-right-bottom {
  flex: 1;
  display: flex;
  gap: 15px;
}

.news-right-bottom .news-hero-item {
  flex: 1;
}

.news-wrapper.view-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
}

.news-wrapper.view-list {
  display: flex;
  flex-direction: column;
}

:root {
  --pink: #ed3b88;
  --orange: #FFC200;
  --blue: #1292E5;
  --black: #000;
  --white: #fff;
  --gray: #cfcdcd;
  --main-color: var(--pink);
  --bg-color: #F7F7F7;
  --text-color: #333;
  --border-color: var(--gray);
  --light-border: #f1f1f1;
  --head-color: var(--black);
  --card-color: white;
  --selection-border: #333;
  --wb-color: #333;
  --bw-color: #fff;
  /* --bs-btn-active-bg: var(--main-color); */
  --bs-border-color: var(--border-color);
  --bs-nav-tabs-link-active-border-color: var(--border-color);

}

header {
  background-color: var(--card-color);
}

.head-color {
  color: var(--black);
}

.card {
  border-color: var(--border-color) !important;
}

.card-color {
  background-color: var(--card-color);
}

.main-color-hover:hover {
  color: var(--main-color);
}

.footer-text {
  color: var(--main-color);
}

.border-color {
  border-color: var(--border-color) !important;
}

.dark-theme {
  --bg-color: #1B1C1E;
  --text-color: #71737F;
  --border-color: #71737F;
  --head-color: var(--white);
  --card-color: #292A2C;
  --selection-border: var(--white);
  --wb-color: #fff;
  --bw-color: #333;

  .head-color {
    color: var(--white);
    --card-color: white
  }

}

h3 {
  font-size: 1.1rem;
}

.btn-outline-primary {
  --bs-btn-color: var(--main-color);
  --bs-btn-border-color: var(--main-color);
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: var(--main-color);
  --bs-btn-hover-border-color: var(--main-color);
  --bs-btn-focus-shadow-rgb: 13, 110, 253;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: var(--main-color);
  --bs-btn-active-border-color: var(--main-color);
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: var(--main-color);
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: var(--main-color);

}

.side-news,
.maing-news {
  overflow: hidden;
}

.side-news:hover img {
  transform: scale(1.1);
  transition: all 0.3s ease;
  transform-origin: center;
}

.main-news {
  height: 285px;

}

.main-news img {
  height: 285px;
  object-fit: cover;
  width: 100%;
}

.main-news:hover img {
  transform: scale(1.1);
  transition: all 0.3s ease;
  transform-origin: center;
}

.btn-outline-secondary.selected {
  background-color: var(--wb-color) !important;
  color: var(--bw-color) !important;
  border-color: var(--bw-color) !important;
}

.btn-outline-dark {
  color: var(--wb-color) !important;
  border-color: var(--wb-color) !important;
  background-color: transparent !important;
  transition: all 0.3s ease;

  &:hover {
    background-color: var(--wb-color) !important;
    color: var(--bw-color) !important;
    border-color: var(--bw-color) !important;
    border-radius: 5px;
  }
}

.btn-outline-dark.selected {
  background-color: var(--wb-color) !important;
  color: var(--bw-color) !important;
  border-color: var(--bw-color) !important;
}

.bg-main {
  background-color: var(--main-color) !important;
}

.bg-main-50 {

  background-color: color-mix(in srgb, var(--main-color) 50%, transparent) !important;

}

.bg-main-30 {

  background-color: color-mix(in srgb, var(--main-color) 30%, transparent) !important;

}

.text-primary,
.main-color {
  color: var(--main-color) !important;
}

hr {

  /* border-top: var(--bs-border-width) solid; */
  border-color: var(--selection-border);
}

#themeBtn {
  position: fixed;

  top: 20%;
  left: 0;
  z-index: 999;
  cursor: pointer;
  display: flex;
  justify-content: center;
  padding: 10px;
}

.teams-btn {
  position: fixed;
  bottom: 5%;
  right: 0;
  z-index: 999;
  cursor: pointer;
  display: flex;
  justify-content: center;
  padding: 10px;
}



.text-color {
  color: var(--text-color);
}

.setting-section {
  .dropdown-panel {
    position: fixed;
    bottom: 72px;
    left: 0;
    z-index: 1055;
    /* Higher than Bootstrap carousel (which uses 1050) */
    width: 100%;
    transition: transform 0.3s ease-in-out;

  }

  .dropdown-panel .panel-container {


    border-radius: 5px;
    padding: 10px;

  }

  .dropdown-panel {
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 320px;
    background-color: var(--card-color);
    z-index: 1055;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.2);
  }

  .dropdown-panel.show {
    transform: translateX(0);
  }

  .color-circle {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    /* border: 2px solid #fff; */
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
  }
}



.bg-pink {
  background-color: var(--pink);
}

.bg-orange {
  background-color: var(--orange);
}

.bg-blue {
  background-color: var(--blue);
}



.theme-choice {
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 6px;
  border: 1px solid transparent;
  transition: all 0.2s;
}

.theme-choice.selected {
  border-color: var(--main-color);
  background-color: var(--main-color);
  color: #fff;
}

/* ===== */




.black-hover:hover {
  background-color: var(--black);
  color: var(--white);
}



.translate-top {
  transform: translate(-50%, -100%) !important;
}

.translate-bottom {
  transform: translateY(50%);
}


.btn-main {
  color: var(--white);
  background-color: var(--main-color) !important;
  transition: opacity 0.3s ease;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;

}


.bg-main:hover {
  opacity: 0.7;
  /* adjust as needed */
}

.btn-main:active {
  background-color: var(--dark-gray);
  color: var(--light-gray);
}

.btn-main:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--light-gray);
}

.btn-main-link {
  text-decoration: none;
  color: var(--main-color);
  font-weight: bold;

}

.btn-main-link:hover {
  color: var(--head-color);
}

.btn-main-link:focus {
  outline: none;
  text-decoration: none;
}

.btn-main-link:active {
  border: none;
}

.btn-check:checked+.btn,
.btn.active,
.btn.show,
.btn:first-child:active,
:not(.btn-check)+.btn:active {
  color: color-mix(in srgb, var(--main-color) 50%, transparent) !important;
}

.btn {

  --bs-btn-font-family: 'din', sans-serif;
}


/* Icon Cheat Sheet Styles */
.icon-cheat-sheet {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  overflow-y: auto;
  z-index: 1050;
}

.icon-demo-box {
  transition: all 0.2s ease;
}

.icon-demo-box:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.icon-demo-box i {
  transition: transform 0.3s ease;
}

.icon-demo-box:hover i {
  transform: scale(1.2);
}



#fireworks-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1039;
  /* Behind the modal but above everything else */
}

@keyframes explode {
  0% {
    transform: scale(0.5);
    opacity: 1;
  }

  70% {
    transform: scale(2);
    opacity: 0.7;
  }

  100% {
    transform: scale(3);
    opacity: 0;
  }
}


.bg-gray {
  background-color: var(--black);
  color: var(--gray);
}

/* canvas */
.canvas {
  #closeButton {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 8px 16px;
    background-color: rgba(0, 0, 0, 0.51);
    color: var(--white);
    border: none;
    border-radius: 5px;
    cursor: pointer;
    z-index: 1040;
    transition: background-color 0.3s ease, transform 0.2s ease;
  }

  #closeButton:hover {
    background-color: var(--pink);
    transform: scale(1.05);
  }

  #closeButton:active {
    background-color: rgba(0, 0, 0, 0.33);
    transform: scale(0.95);
  }

  #startbtn {
    position: fixed;
    top: 35%;
    right: 0;
    background-color: var(--pink);
    z-index: 1040;
    cursor: pointer;
    color: var(--white) !important;
    display: flex;
  }

  #startbtn:hover {
    background-color: var(--bg-color);
  }

  h1 {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    color: var(--white);
    text-align: center;
    z-index: 1050;
    font-size: 4rem;
    font-weight: bold;
  }

  h1 span.text-center {
    color: var(--white);
  }

  canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    z-index: 1038;
    pointer-events: none;
    opacity: 0.8;
  }

}

/* ===================== 
 End Canvas Happy Birthday
  ====================== */
/* ===================== 
       Header Start
  ====================== */
.responsive-menu {
  display: flex;
}

.nav-link {
  color: var(--head-color);
  font-weight: 700;
}

.nav-link:focus,
.nav-link:hover,
.nav-link.active,
.nav-link.show {
  color: var(--main-color);
}

.navbar-nav .nav-link.active,
.navbar-nav .nav-link.show {
  color: var(--main-color);
}

.logo-w {
  display: none;
}

.dark-theme .logo-b {
  display: none;
}

.dark-theme .logo-w {
  display: block;
}

/* ===================== 
Home Slider
  ====================== */
.slider-over {
  right: 5%;
  left: auto;
  bottom: 50%;
  transform: translateY(50%);
  background-color: #ffffffe0;
  border-top-left-radius: 20%;
  max-width: 500px;
  padding: 44px;
}

.dark-theme .slider-over {

  background-color: #000000e0;
}

.carousel-control-next,
.carousel-control-prev {

  width: 5%;
  opacity: .6;
}

.carousel-control-next-icon,
.carousel-control-prev-icon {
  background-color: var(--black);
  padding: 10px;
  border-radius: 5px;
}

/* ===================== 
Home Slider
  ====================== */
/* ===================== 
           Footer
  ====================== */
footer {
  background-color: var(--black);
  color: var(--white);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

#darkOption {

  color: var(--head-color);
}

.color-circle.selected {
  border: 3px solid var(--border-color) !important;
}

/* ===================== 
        end   Footer
  ====================== */
.form-control {

  color: var(--text-color);
  background-color: var(--card-color);
  background-clip: padding-box;

  border-color: var(--border-color);
}

.form-control::placeholder {
  color: var(--text-color);
  opacity: 1;
}

.form-control:focus {
  color: var(--text-color);
  background-color: var(--card-color);
  border-color: var(--main-bg);
}

.dropdown-item {

  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);

  color: var(--text-color);

  background-color: var(--card-color);
  ;
}

.dropdown-item:hover {
  background-color: var(--main-color);
  color: var(--white);
}

.dropdown-menu {

  --bs-dropdown-bg: var(--card-color);
}


/* =======
     news
     ======= */
.news-section {
  .news-title {
    font-weight: bold;
    font-size: 1rem;
  }

  .overlay-text {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    width: 100%;
  }

  .category-label {

    font-weight: bold;
    margin-top: 0.5rem;
  }

  .main-news img {
    height: 285px;
    object-fit: cover;
    width: 100%;
  }

  .side-news img {
    height: 90px;
    object-fit: cover;
    width: 100%;
  }
}

.section-card {
  .tittle {
    font-weight: bold;
    font-size: 1.3rem;

    margin-top: .5rem;
    padding-bottom: .5rem;
    border-bottom: solid 1px var(--border-color);
  }
}

.card-footer {

  background-color: var(--card-color);
  border-top: var(--bs-card-border-width) solid var(--border-color);
}

/* =========
    Events Section
    */
.events-section {
  .nav-link.active {
    color: var(--white);
    background-color: var(--main-color) !important;

  }
}

.no-data img {

  height: 100px;

}

/* =======
    Our people
 ========*/
.our-people {
  img {
    height: 175px;
  }

  a:hover img {
    transform: scale(1.1);
    transition: all 0.3s ease;
  }
}

/* ======
Quick Access
 ======= */
.q-section {
  .img-container {
    width: 80px;
    height: 80px;
    display: flex;
    border-radius: 50%;
    background-color: color-mix(in srgb, var(--main-color) 50%, transparent) !important;

    justify-content: center;
    align-items: center;
  }

  .img-container img {
    width: 100%;
    height: 38px;

  }

  a:has(.img-container):hover img {
    transform: scale(1.1);
    transition: all 0.5s ease;
    filter: brightness(0) invert(1);
  }

  a:has(.img-container):hover .img-container {
    transform: scale(1.1);
    transition: all 0.3s ease;
    background-color: var(--main-color) !important;


  }

  .img-container:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
  }

  h6 {
    font-size: 15px;
    font-weight: 600;
  }

  a:has(.img-container) {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 0 0 30%;
    margin: 10px 0;
  }

  .q-box {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    align-items: center;
    height: 300px;
    overflow: auto;
  }
}

/* ======
 benefits-section
 ==========*/
.benefits-section {
  img {
    width: 70px;
    height: auto;
  }

  a:hover img {
    transform: scale(1.1);
    transition: all 0.3s ease;
  }
}

.dark-theme .mazya {
  filter: brightness(0) invert(1);
}

/* ========
 the collections 
 =============*/
.collection-section {

  /* Hover effect for overlay */
  .carousel-item:hover .overlay-hover {
    display: block !important;
  }

  .overlay-hover {
    display: none;
    transition: all 0.3s ease-in-out;
  }

  /* Container: maintain height for images */
  #collectionCarousel .carousel-item img {
    height: 258px;
    /* Adjust as needed */
    object-fit: cover;
    width: 100%;
  }

  /* Overlay only on bottom */
  .overlay-hover {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: none;
    background: rgba(0, 0, 0, 0.6);
    padding: 15px;
    transition: all 0.3s ease;
  }

  /* Show overlay on hover */
  .carousel-item:hover .overlay-hover {
    display: block !important;
  }

  /* Responsive behavior: remove fixed height below 992px */
  @media (max-width: 991.98px) {
    #collectionCarousel .carousel-item img {
      height: 400px;
    }
  }

}


.inspire-section {

  #quoteCarousel .carousel-item {
    height: 175px;
    /* Adjust as needed */

  }
}

.dropdown-menu {
  border-color: var(--border-color);
}

button.carousel-control-prev,
button.carousel-control-next,
a.carousel-item.active.position-relative {
  padding: 12px 20px;
  min-width: 44px;
  min-height: 44px;
  font-size: 18px;
  margin: 0 10px;
}

.carousel-control-prev,
.carousel-control-next {
  margin: 0 15px;
}

.skip-link {
  position: absolute;
  top: -50px;
  left: 0;
  background-color: #000;
  color: #fff;
  padding: 10px;
  z-index: 1000;
  text-decoration: none;
}

.skip-link:focus {
  top: 10px;
}

.vertical-carousel-dots button {
  min-width: 44px;
  min-height: 44px;
  padding: 10px 20px;
  margin: 8px;
}

a.text-decoration-none.fw-bold.btn-main-link,
a.carousel-item.active.position-relative {
  display: inline-block;
  padding: 12px 20px;
  min-width: 44px;
  min-height: 44px;
  cursor: pointer;
}


.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  background: var(--main-color) !important;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #fff;
  border-color: var(--main-color) !important;
}

.flatpickr-day.today {
  border-color: var(--main-color) !important;
}

.input-group-text {
  background-color: transparent !important;
}

.news-page {

  /* News Hero Section Styles */

  nav[aria-label="Page navigation"] {
    width: 100%;
    margin-top: 2rem;
  }

  .pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    flex-wrap: wrap;
    width: 100%;
  }

  .news-hero {
    display: flex;

    grid-gap: 20px;

  }

  .news-hero-main {
    position: relative;
    height: 500px;
    overflow: hidden;
    border-radius: 8px;
    flex: 1 1 70%;
  }

  .news-overlay:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    background: linear-gradient(to bottom, transparent 0%, transparent 39%, rgba(0, 0, 0, .95) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#db000000', GradientType=0);
    z-index: 1;
  }

  .news-overlay img:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .15);
    z-index: 1;
    opacity: 1;
  }

  .news-overlay:hover img {
    transform: scale(1.05);
    transition: all 0.5s ease;
  }

  .news-hero-secondary {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1 1 30%;
  }

  .news-hero-item {
    position: relative;
    height: 240px;
    overflow: hidden;
    border-radius: 8px;
  }

  .news-hero-main img,
  .news-hero-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .news-hero-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    /* background: linear-gradient(to top, rgba(0,0,0,0.8), transparent); */
    color: white;
    padding: 20px;
    z-index: 2;
  }

  .news-hero-caption h2 {
    font-size: 1rem;
    margin-bottom: 10px;
  }

  .news-hero-main .news-hero-caption h2 {
    font-size: 1.3rem;
  }

  .news-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 14px;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .news-hero {
      grid-template-columns: 1fr;
    }

    .news-hero-main,
    .news-hero-item {
      height: 300px;
    }
  }

  /* Remove native calendar icon in input[type="date"] for webkit browsers */
  input[type="date"].no-native-calendar::-webkit-calendar-picker-indicator {
    display: none;
  }

  /* Fully hide default date icon */
  input[type="date"].no-native-calendar {
    position: relative;
    z-index: 2;
    background-color: transparent;
  }

  input[type="date"].no-native-calendar::-webkit-inner-spin-button,
  input[type="date"].no-native-calendar::-webkit-calendar-picker-indicator {
    opacity: 0;
    position: absolute;
    right: 0;
    z-index: -1;
  }

  input[type="date"].no-native-calendar::-moz-calendar-picker-indicator {
    opacity: 0;
  }

  .news-wrapper.view-grid .news-item .card {
    display: grid;
    grid-template-columns: repeat(3, 1fr);


  }

  .news-wrapper.view-list .news-item .card {
    display: flex;
    flex-direction: column;
  }

  .news-wrapper.view-grid .news-item .card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }

  .news-wrapper.view-list .news-item {
    margin-bottom: 10px;
  }

  .news-wrapper.view-list .news-item .card img {
    width: 180px;
    height: 140px;
    object-fit: cover;
    flex-shrink: 0;
    align-self: flex-start;
  }

  /* Responsive grid layout for news items */
  .news-wrapper.view-grid {
    display: grid;
    flex-wrap: wrap;
    margin: 0 -0.5rem;
  }

  

  .news-item span {
    color: var(--text-color);
  }

  @media (min-width: 768px) {
    .news-wrapper.view-grid .news-item {
      width: 50%;
    }
  }

  @media (min-width: 992px) {
    .news-wrapper.view-grid .news-item {
      width: 33.3333%;
    }
  }

  .news-wrapper .news-item h5 {

    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--head-color);
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .news-wrapper .news-item p {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    color: var(--text-color);
  }

  /* Custom pagination styling */
  .pagination .page-link {
    color: var(--text-color);
    border: 1px solid var(--border-color);
    background-color: var(--card-color);
    border-radius: 5px !important;
    padding: 6px 12px;
    transition: all 0.3s ease;
  }

  .pagination .page-item {
    margin: 0 5px;
  }

  .pagination .page-link:hover {
    background-color: var(--main-color);
    color: var(--white);
    border-color: var(--main-color);
  }

  .pagination .page-item.active .page-link {
    background-color: var(--main-color);
    border-color: var(--main-color);
    color: var(--white);
  }

  .pagination .page-item.disabled .page-link {
    opacity: 0.5;
    pointer-events: none;
  }

  /* Sorting Button Custom Style */
  .dropdown-toggle {
    background-color: var(--bw-color) !important;
    color: var(--main-color) !important;
    border: none !important;
    font-weight: bold;
    padding: 6px 16px;
    border-radius: 6px;
    transition: background-color 0.3s ease;
  }

  .dropdown-toggle:focus,
  .dropdown-toggle:hover {
    background-color: var(--main-color) !important;
    color: var(--white) !important;
  }

  .dropdown-menu .dropdown-item {
    font-weight: bold;
    padding: 10px 16px;
    color: var(--text-color);
  }

  .news-wrapper .news-item img {
    width: 100%;
    max-width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
    padding: 10px;
  }

  .dropdown-menu .dropdown-item:hover,
  .dropdown-menu .dropdown-item:focus {
    background-color: var(--main-color);
    color: var(--white);
  }
}

/* =======
    end news
 ======== */

/* =======
    4 News Section (Index Page)
  ======== */


.f4-news {
  /* Reuse news-page styles */


  .news-hero {
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-gap: 20px;
  }

  .news-hero-main {
    position: relative;
    height: 249px;
    overflow: hidden;
    border-radius: 8px;
  }

  .news-overlay:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, transparent 0%, transparent 39%, rgba(0, 0, 0, .95) 100%);
    z-index: 1;
  }

  .news-overlay:hover img {
    transform: scale(1.05);
    transition: all 0.5s ease;
  }

  .news-hero {
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-gap: 20px;
    height: 249px;
  }

  .news-hero-secondary {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    gap: 20px;
    max-height: 100%;
    height: 100%;
    overflow-y: hidden;
  }




  .news-hero-item {
    position: relative;
    height: 249px;
    overflow: hidden;
    border-radius: 8px;
  }

  .f4-news .news-hero-item {
    position: relative;
    height: 120px;
    min-height: 120px;
    flex-shrink: 0;
    overflow: hidden;
    border-radius: 8px;
  }

  .f4-news .news-hero-main {
    position: relative;
    height: 249px;
    overflow: hidden;
    border-radius: 8px;
  }

  .news-hero-main img,
  .news-hero-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .news-hero-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    color: white;
    padding: 20px;
    z-index: 2;
  }

  .news-hero-caption h2 {
    font-size: 1rem;
    margin-bottom: 10px;
  }

  .news-hero-main .news-hero-caption h2 {
    font-size: 1.3rem;
  }

  .news-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 14px;
  }



  .custom-vertical-carousel {
    position: relative;
    height: 100%;
    overflow: hidden;
  }

  .vertical-carousel-inner {
    display: flex;
    flex-direction: column;
    transition: transform 0.6s ease-in-out;
    height: 100%;
  }

  .vertical-carousel-item {
    position: absolute;
    width: 100%;
    transition: transform 0.3s ease-in-out;
  }

  .vertical-carousel-item {
    flex: 0 0 100%;
    height: 100%;
  }

  .vertical-carousel-arrows {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 3;
  }

  .vertical-carousel-arrows button {
    background: var(--white);
    border: none;
    border-radius: 5px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .vertical-carousel-arrows button:hover {
    background: var(--main-color);
    color: white;
  }

  .vertical-carousel-arrows button:hover i {

    color: white;
  }

  .vertical-carousel-arrows i {
    font-size: 16px;
    z-index: 2;
    color: var(--main-color);
  }

  .vertical-carousel-dots button {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: none;
    background-color: #fff;
    opacity: 0.5;
    cursor: pointer;
  }

  .vertical-carousel-dots button.active {
    opacity: 1;
  }

}

.news-article figure img {
  height: 300px;
  /* existing height remains */
  width: 100%;
  object-fit: cover;
}

.recent-news img {
  height: 100px;
  /* adjust as desired specifically for recent-news */
  width: 100px;
  object-fit: cover;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .news-article figure img {
    height: 150px;
    /* Slightly smaller for tablets */
  }

  .recent-news img {
    height: 130px;
  }
}

@media (max-width: 576px) {
  .news-article figure img {
    height: 120px;
    /* Even smaller for mobile screens */
  }

  .recent-news img {
    height: 100px;
  }
}

.dropdown-item:hover i {
  color: var(--dark-color) !important;
}

.dropdown-item {
  border: var(--light-border) solid 1px !important;
  padding: 10px;
}

.mt--40 {
  margin-top: -40px;
}

.page-wallpaper {
  position: relative;
}

.page-wallpaper .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  /* Adjust opacity as needed */
  z-index: 1;
}

.page-wallpaper img {
  position: relative;
  z-index: 0;
}

.bread-cramp {
  z-index: 2;
  position: relative;
}

/* Events Page Styles */
.events-page {
  .filters {
    .btn-outline-secondary {

      padding: 0.375rem 0.75rem;
      font-size: 0.875rem;
    }
  }
}

.border-left-pink {
  border-left: 6px solid var(--pink) !important;
}

.border-left-blue {
  border-left: 6px solid var(--blue) !important;
}

.border-left-orange {
  border-left: 6px solid var(--orange) !important;
}

.border-left-blue.selected {
  border-left: 6px solid var(--blue) !important;
}

.border-left-orange.selected {
  border-left: 6px solid var(--orange) !important;
}

.border-left-pink.selcted {
  border-left: 6px solid var(--pink) !important;
}

.flatpickr-calendar {
  box-shadow: none !important;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flatpickr-calendar.inline {
  background-color: var(--card-color) !important;
}