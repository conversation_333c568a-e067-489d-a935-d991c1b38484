import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';



export interface IStoriesWebPartProps {
  description: string;
}

export default class StoriesWebPart extends BaseClientSideWebPart<IStoriesWebPartProps> {
  private _isArabic: boolean = false;
  private currentPage: number = 1;
  private itemsPerPage: number = 10;
  private allStories: any[] = [];
  private filteredStories: any[] = [];
  private totalItems: number = 0;

  public render(): void {
   this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;
    this.domElement.innerHTML = `
      <div class="col-lg-12">
        <section class="stories-section h-100">
          <div class="card section-card card-color h-100">
            <div class="container-fluid">
              <h2 class="fw-bold tittle head-color ${this._isArabic ? '' : ''}">
                ${this._isArabic ? "قصصنا" : "Our Stories"}
              </h2>
              <div class="d-flex flex-column" id="stories-container">
                <p>${this._isArabic ? "جارٍ تحميل القصص..." : "Loading stories..."}</p>
              </div>
              <div id="pagination-container"></div>
            </div>
          </div>
        </section>
      </div>
    `;

    this.fetchAllStories();
  }

  private fetchAllStories(): void {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Our Stories')/items?$filter=Active eq 1&$orderby=Created desc`;

    this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => response.json())
      .then((data: any) => {
        this.allStories = data.value || [];
        this.totalItems = this.allStories.length;
        this.applyPagination();
      })
      .catch(error => {
        console.error("Error fetching stories:", error);
        const container = this.domElement.querySelector('#stories-container');
        if (container) {
          container.innerHTML = `<p class="text-danger">${this._isArabic ? "فشل في تحميل القصص." : "Failed to load stories."}</p>`;
        }
      });
  }

  private applyPagination(): void {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.filteredStories = this.allStories.slice(startIndex, endIndex);
    
    this.renderStories();
    this.renderPagination();
  }

  private renderStories(): void {
    const container = this.domElement.querySelector('#stories-container');
    if (!container) return;

    if (this.filteredStories.length === 0) {
      container.innerHTML = `<p class="text-muted">${this._isArabic ? "لا توجد قصص لعرضها." : "No stories to display."}</p>`;
      return;
    }

    let html = '';
    this.filteredStories.forEach((item: any) => {
      const title = this._isArabic ? (item.Title_AR || item.Title || '') : item.Title || '';
      const link = this._isArabic ? 
        (item.Link_AR && item.Link_AR.Url ? item.Link_AR.Url : '#') : 
        (item.Link_EN && item.Link_EN.Url ? item.Link_EN.Url : '#');

      html += `
        <a href="${link}" class="text-decoration-none my-2 ${this._isArabic ? 'text-end' : ''}">
          <span class="head-color fw-bold mt-3 main-color-hover">${title}</span>
        </a>
      `;
    });

    container.innerHTML = html;
  }

  private renderPagination(): void {
    const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
    if (totalPages <= 1) {
      const container = this.domElement.querySelector('#pagination-container');
      if (container) container.innerHTML = '';
      return;
    }

    const prevText = this._isArabic ? "السابق" : "Previous";
    const nextText = this._isArabic ? "التالي" : "Next";
    const dir = this._isArabic ? 'flex-row-reverse' : '';

    let pageNumbers = '';
    for (let i = 1; i <= totalPages; i++) {
      pageNumbers += `
        <li class="page-item ${this.currentPage === i ? 'active' : ''}">
          <a class="page-link" href="#" data-page="${i}">${i}</a>
        </li>
      `;
    }

    const paginationHtml = `
      <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center flex-wrap ${dir} gap-1">
          <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link rounded-pill px-3" href="#" data-nav="prev">
              <i class="bi bi-arrow-${this._isArabic ? 'right' : 'left'}"></i> ${prevText}
            </a>
          </li>
          ${pageNumbers}
          <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link rounded-pill px-3" href="#" data-nav="next">
              ${nextText} <i class="bi bi-arrow-${this._isArabic ? 'left' : 'right'}"></i>
            </a>
          </li>
        </ul>
      </nav>
    `;

    const container = this.domElement.querySelector('#pagination-container');
    if (container) {
      container.innerHTML = paginationHtml;
      this.addPaginationEventListeners();
    }
  }

  private addPaginationEventListeners(): void {
    const pageLinks = this.domElement.querySelectorAll('.page-link');

    pageLinks.forEach((link: Element) => {
      link.addEventListener('click', (event: Event) => {
        event.preventDefault();
        const target = event.currentTarget as HTMLElement;
        const navType = target.getAttribute('data-nav');
        const pageNumber = target.getAttribute('data-page');

        if (navType === 'prev' && this.currentPage > 1) {
          this.changePage(this.currentPage - 1);
        } else if (navType === 'next' && this.currentPage < Math.ceil(this.totalItems / this.itemsPerPage)) {
          this.changePage(this.currentPage + 1);
        } else if (pageNumber) {
          const page = parseInt(pageNumber);
          if (!isNaN(page) && page !== this.currentPage) {
            this.changePage(page);
          }
        }
      });
    });
  }

  private changePage(newPage: number): void {
    if (newPage >= 1 && newPage <= Math.ceil(this.totalItems / this.itemsPerPage)) {
      this.currentPage = newPage;
      this.applyPagination();
      this.domElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

  protected onInit(): Promise<void> {
    return Promise.resolve();
  }
}