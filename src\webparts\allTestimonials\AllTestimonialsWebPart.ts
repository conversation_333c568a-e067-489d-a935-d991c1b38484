import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient } from '@microsoft/sp-http';

export interface IAllTestimonialsWebPartProps {
  description: string;
}

export default class AllTestimonialsWebPart extends BaseClientSideWebPart<IAllTestimonialsWebPartProps> {
  private currentPage: number = 1;
  private totalItems: number = 0;
  private itemsPerPage: number = 9;
  private totalPages: number = 0;
  private _isArabic: boolean = false;
  private allEmployees: any[] = [];
  private searchTerm: string = '';

  public render(): void {
    console.log('Bootstrap available:', (window as any).bootstrap); // Debug
    console.log('Bootstrap Modal available:', (window as any).bootstrap?.Modal); // Debug
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;

    this.domElement.innerHTML = `
    <style>
      .profile-card {
        border: 1px solid var(--border-color);
        border-radius: 10px;
        padding: 20px;
        text-align: left;
        background-color: var(--card-color);
        transition: all 0.3s ease-in-out;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
      .profile-card:hover,
      .profile-card.active {
        border-color: var(--border-color);
        box-shadow: 0 6px 16px rgba(214, 51, 132, 0.3);
        transform: scale(1.02);
        cursor: pointer;
      }
      .profile-img {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border-radius: 50%;
      }
      .profile-name {
        font-weight: 600;
        font-size: 14px;
        margin-bottom: 0;
      }
      .profile-title {
        color: var(--primary-color);
        font-size: 13px;
      }
      .dept-label {
        font-size: 13px;
        color: #666;
      }
      .dept-name {
        font-weight: bold;
      }
      .icons img {
        width: 24px;
        margin-right: 5px;
      }
      .date {
        font-size: 13px;
        color: #666;
      }
      .date-icon {
        margin-right: 5px;
      }
      .video-modal .modal-content {
        background-color: var(--card-color);
      }
      .video-modal .modal-body {
        padding: 0;
      }
      .video-modal iframe {
        width: 100%;
        height: 400px;
      }
      .video-modal {
        z-index: 10000 !important;
      }
    </style>
    <div id="employeeCards" class="container mt-4">
      <div class="d-flex flex-column flex-md-row justify-content-between align-items-center mb-3 gap-2">
        <div class="search-wrapper position-relative" style="width: 300px;">
          <input type="text" id="searchInput" class="form-control pe-5" placeholder="Name" />
          <i class="bi bi-search position-absolute top-50 end-0 translate-middle-y me-3 text-secondary"></i>
        </div>
        <div class="dropdown ms-2">
          <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="sortDropdown"
            data-bs-toggle="dropdown" aria-expanded="false">
            Sort by: Publish Data (Newest)
          </button>
          <ul class="dropdown-menu" aria-labelledby="sortDropdown">
            <li><a class="dropdown-item" href="#" data-sort="Name">Name</a></li>
            <li><a class="dropdown-item" href="#" data-sort="Department">Department</a></li>
            <li><a class="dropdown-item" href="#" data-sort="Nationality">Nationality</a></li>
            <li><a class="dropdown-item" href="#" data-sort="Publish Data (Oldest)">Publish Data (Oldest)</a></li>
            <li><a class="dropdown-item" href="#" data-sort="Publish Data (Newest)">Publish Data (Newest)</a></li>
          </ul>
        </div>
      </div>
      <div id="employeesList" class="row row-cols-1 row-cols-md-4 g-4"></div>
      <nav id="paginationNav" aria-label="Page navigation example" class="mt-4">
        <ul class="pagination justify-content-center"></ul>
      </nav>
      <!-- Video Modal -->
      <div id="customVideoModal" class="custom-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; align-items: center; justify-content: center;">
    <div style="background: var(--card-color); width: 80%; max-width: 800px; border-radius: 10px; padding: 20px;">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
        <h5>${this._isArabic ? 'فيديو الشهادة' : 'Testimonial Video'}</h5>
        <button onclick="document.getElementById('customVideoModal').style.display='none'; document.getElementById('videoFrame').src='';" style="border: none; background: none; font-size: 20px;">×</button>
      </div>
      <iframe id="videoFrame" src="" frameborder="0" allowfullscreen style="width: 100%; height: 400px;"></iframe>
    </div>
  </div>
    `;

    this.fetchAllEmployees();
    this.setupSearchListener();
    this.setupSortListener();
  }

  private async fetchAllEmployees(): Promise<void> {
    try {
      const response = await this.context.spHttpClient.get(
        `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Testimonials')/items?$select=*,Author/Created,Author/Title,Author/JobTitle,Author/Department,Author/EMail,VideoLink&$expand=Author`,
        SPHttpClient.configurations.v1
      );

      const data = await response.json();
      this.allEmployees = data.value;
      this.totalItems = this.allEmployees.length;
      this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
      this.displayCurrentPage();
    } catch (error) {
      console.error('Error fetching testimonials:', error);
    }
  }

  private async displayCurrentPage(usersToDisplay?: any[]): Promise<void> {
    const employees = usersToDisplay ?? this.allEmployees;
    const client = await this.context.msGraphClientFactory.getClient('3');

    this.totalItems = employees.length;
    this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);

    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = Math.min(startIndex + this.itemsPerPage, this.totalItems);
    const pageEmployees = employees.slice(startIndex, endIndex);

    const employeeCards = await Promise.all(pageEmployees.map(async (item: any) => {
      const name = item.Author?.Title || '';
      const jobTitle = item.JobTitle || item.Author?.JobTitle || 'No title';
      const department = item.Department || item.Author?.Department || '';
      const email = item.Author?.EMail || '';
      const outlookLink = `mailto:${email}`;
      const teamsLink = `https://teams.microsoft.com/l/chat/0/0?users=${email}`;
      const createdDate = item.Author?.Created
        ? new Date(item.Author.Created).toLocaleDateString('en-GB')
        : '';
      let videoLink = item.VideoLink?.Url || '';
      console.log('Original VideoLink:', item.VideoLink?.Url, 'Transformed VideoLink:', videoLink); // Debug
      if (videoLink.includes('youtube.com/watch?v=')) {
        const videoId = videoLink.split('v=')[1]?.split('&')[0];
        videoLink = videoId ? `https://www.youtube.com/embed/${videoId}` : '';
      } else if (videoLink.includes('youtu.be/')) {
        const videoId = videoLink.split('youtu.be/')[1]?.split('?')[0];
        videoLink = videoId ? `https://www.youtube.com/embed/${videoId}` : '';
      }
      if (!videoLink || !videoLink.includes('embed') && !videoLink.includes('player.vimeo.com')) {
        videoLink = ''; // Skip invalid URLs
      }

      const photoUrl = email ? await this.getUserPhoto(client, email) : require('./assets/img.jpg');
      return `
        <div class="col d-flex">
          <div class="profile-card w-100" onclick="location.href='/sites/intranet-qm/SitePages/Testimonials.aspx?empId=${item.Id}'">
            <div class="d-flex align-items-center mb-3">
              <img src="${photoUrl}" class="profile-img me-3" />
              <div>
                <p class="profile-name mb-1">${name}</p>
                <p class="profile-title">${jobTitle}</p>
              </div>
            </div>
            <p class="dept-label mb-1">${this._isArabic ? 'القسم:' : 'Department:'} 
              <span class="dept-name">${department}</span></p>
            <div class="d-flex justify-content-between align-items-center mt-3">
              <div class="icons">
                <a href="${outlookLink}" target="_blank" class="text-decoration-none">
<svg xmlns="http://www.w3.org/2000/svg" id="Message" width="26.291" height="25.249" viewBox="0 0 26.291 25.249">
                    <path id="Stroke_1" data-name="Stroke 1" d="M7.366,6.1c-1.484,0-3.279-.909-5.335-2.7A26.173,26.173,0,0,1-.524.828.96.96,0,1,1,.944-.409C2.44,1.365,5.508,4.18,7.366,4.18S12.261,1.368,13.74-.4A.96.96,0,0,1,15.215.824,25.818,25.818,0,0,1,12.682,3.4C10.64,5.189,8.852,6.1,7.366,6.1Z" transform="translate(5.794 8.641)" fill="#ed3c8a"></path>
                    <path id="Stroke_3" data-name="Stroke 3" d="M12.4-.75c5.007,0,8.148.869,10.184,2.818s2.962,4.985,2.962,9.807-.913,7.845-2.962,9.806S17.4,24.5,12.4,24.5,4.248,23.63,2.212,21.681-.75,16.7-.75,11.875.163,4.029,2.212,2.068,7.388-.75,12.4-.75Zm0,23.33c8.6,0,11.226-2.5,11.226-10.705S21,1.169,12.4,1.169,1.169,3.671,1.169,11.875,3.792,22.58,12.4,22.58Z" transform="translate(0.75 0.75)" fill="#ed3c8a"></path>
                  </svg>                </a>
                <a href="${teamsLink}" target="_blank" class="text-decoration-none">
<svg xmlns="http://www.w3.org/2000/svg" width="26.722" height="26.721" viewBox="0 0 26.722 26.721">
                    <g id="chat-ro" transform="translate(1 1)">
                      <path id="Path_77853" data-name="Path 77853" d="M18.2,12.937h-.011a.969.969,0,1,1,0-1.937H18.2a.969.969,0,0,1,0,1.937Z" transform="translate(-0.887 0.392)" fill="#ee3c8a"></path>
                      <path id="Path_77853-2" data-name="Path 77853" d="M18.2,12.937h-.011a.969.969,0,1,1,0-1.937H18.2a.969.969,0,0,1,0,1.937Z" transform="translate(-5.729 0.392)" fill="#ee3c8a"></path>
                      <path id="Path_77853-3" data-name="Path 77853" d="M18.2,12.937h-.011a.969.969,0,1,1,0-1.937H18.2a.969.969,0,0,1,0,1.937Z" transform="translate(-10.572 0.392)" fill="#ee3c8a"></path>
                      <path id="Path_77854" data-name="Path 77854" d="M14.361,27.721a13.226,13.226,0,0,1-5.949-1.395,1.068,1.068,0,0,0-.741-.1l-2.759.738A2.568,2.568,0,0,1,2.2,26.043a2.557,2.557,0,0,1-.443-2.234l.738-2.759a1.059,1.059,0,0,0-.1-.741A13.363,13.363,0,0,1,14.361,1a13.361,13.361,0,0,1,9.447,22.808A13.273,13.273,0,0,1,14.361,27.721ZM7.9,24.263a3.1,3.1,0,0,1,1.373.33,11.426,11.426,0,0,0,16.51-10.232,11.424,11.424,0,0,0-19.5-8.078A11.433,11.433,0,0,0,4.128,19.446a2.991,2.991,0,0,1,.234,2.1L3.625,24.31a.626.626,0,0,0,.108.554.644.644,0,0,0,.505.256.672.672,0,0,0,.173-.024l2.758-.738A2.831,2.831,0,0,1,7.9,24.263Z" transform="translate(-2 -2)" fill="#ee3c8a"></path>
                    </g>
                  </svg>                </a>
                ${videoLink ? `
                  <a href="#" class="text-decoration-none video-link" data-video="${videoLink}">
<svg xmlns="http://www.w3.org/2000/svg" width="20.462" height="15.346" viewBox="0 0 20.462 15.346">
  <g id="Video" transform="translate(0.75 0.75)">
    <path id="Stroke_1" data-name="Stroke 1" d="M4.5,10.411a4.648,4.648,0,0,1-1.93-.653A21.571,21.571,0,0,1-.461,7.772.75.75,0,0,1-.592,6.72a.75.75,0,0,1,1.053-.13,15.034,15.034,0,0,0,3.8,2.279,20.406,20.406,0,0,0,.311-4.026A18.563,18.563,0,0,0,4.268.784,6.1,6.1,0,0,0,2.913,1.4,19.646,19.646,0,0,0,.466,3.054.75.75,0,0,1-.588,2.932.75.75,0,0,1-.466,1.879,20.083,20.083,0,0,1,2.553-.115c.954-.5,2.131-.979,2.769-.289.218.235.464.675.62,2.154a30.2,30.2,0,0,1,.133,3.12c0,1.139-.058,2.241-.152,3.1-.158,1.455-.383,1.875-.6,2.11A1.061,1.061,0,0,1,4.5,10.411Z" transform="translate(13.637 2.092)" fill="#fd5675"/>
    <path id="Stroke_3" data-name="Stroke 3" d="M6.9-.75c2.9,0,4.726.535,5.922,1.735s1.728,3.03,1.728,5.938-.533,4.738-1.728,5.938S9.8,14.6,6.9,14.6,2.174,14.061.978,12.861-.75,9.83-.75,6.923-.217,2.184.978.985,4-.75,6.9-.75ZM6.9,13.1c2.469,0,3.968-.4,4.859-1.294s1.291-2.4,1.291-4.879-.4-3.984-1.29-4.879S9.37.75,6.9.75s-3.968.4-4.86,1.294S.75,4.444.75,6.923s.4,3.983,1.291,4.879S4.432,13.1,6.9,13.1Z" transform="translate(0 0)" fill="#fd5675"/>
  </g>
</svg>                  </a>
                ` : ''}
              </div>
              <div class="date d-flex align-items-center">
                <i class="bi bi-calendar-date mx-2 text-color"></i>
                <span class="date-icon">${createdDate}</span>
              </div>
            </div>
          </div>
        </div>
      `;
    }));

    const listElement = this.domElement.querySelector('#employeesList');
    if (listElement) {
      listElement.innerHTML = employeeCards.length > 0
        ? employeeCards.join('')
        : `<div class="text-center w-100 text-muted">${this._isArabic ? 'لا يوجد نتائج' : 'No results found.'}</div>`;
      this.addCardClickEvents();
      this.addVideoLinkEvents();
    }

    this.updatePagination();
  }

  private addCardClickEvents(): void {
    const cards = this.domElement.querySelectorAll('.profile-card');
    cards.forEach(card => {
      card.addEventListener('click', (event) => {
        if (!(event.target as HTMLElement).closest('.video-link')) {
          cards.forEach(c => c.classList.remove('active'));
          card.classList.add('active');
        }
      });
    });
  }

  private addVideoLinkEvents(): void {
  const videoLinks = this.domElement.querySelectorAll('.video-link');
  console.log('Video links found:', videoLinks.length); // تصحيح
  videoLinks.forEach(link => {
    link.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();
      const videoUrl = link.getAttribute('data-video');
      console.log('Video URL:', videoUrl); // تصحيح
      const videoFrame = this.domElement.querySelector('#videoFrame') as HTMLIFrameElement;
      const modal = this.domElement.querySelector('#customVideoModal') as HTMLElement;
      if (videoUrl && videoFrame && modal) {
        videoFrame.src = videoUrl;
        modal.style.display = 'flex';
      } else {
        console.error('Modal or video frame not found, or invalid video URL:', { videoUrl, videoFrame, modal });
        alert('لم يتم العثور على المودل أو إطار الفيديو، أو رابط الفيديو غير صالح.');
      }
    });
  });

  // إغلاق المودل عند النقر خارج المحتوى
  const modal = this.domElement.querySelector('#customVideoModal') as HTMLElement;
  modal?.addEventListener('click', (event) => {
    if (event.target === modal) {
      modal.style.display = 'none';
      const videoFrame = this.domElement.querySelector('#videoFrame') as HTMLIFrameElement;
      if (videoFrame) {
        videoFrame.src = '';
      }
    }
  });
}

  private async getUserPhoto(client: any, userId: string): Promise<string> {
    try {
      const photoResponse = await client
        .api(`/users/${userId}/photo/$value`)
        .responseType('blob')
        .get();

      if (!photoResponse || photoResponse.size === 0) {
        console.warn(`No photo found for user ${userId}`);
        return require('./assets/img.jpg');
      }

      const photoBlob = new Blob([photoResponse], { type: 'image/jpeg' });
      return URL.createObjectURL(photoBlob);
    } catch (error) {
      console.error('Error fetching user photo:', error);
      return require('./assets/img.jpg');
    }
  }

  private updatePagination(): void {
    const paginationElement = this.domElement.querySelector('.pagination');
    if (!paginationElement) return;

    let pageNumbers = '';
    const maxVisiblePages = 5;
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(this.totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    pageNumbers += `
      <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
        <a class="page-link rounded-pill px-3" href="#" aria-label="Previous" data-page="prev">
          <i class="bi bi-arrow-left"></i> ${this._isArabic ? 'السابق' : 'Previous'}
        </a>
      </li>
    `;

    if (startPage > 1) {
      pageNumbers += `
        <li class="page-item">
          <a class="page-link" href="#" data-page="1">1</a>
        </li>
        ${startPage > 2 ? '<li class="page-item disabled"><span class="page-link">...</span></li>' : ''}
      `;
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers += `
        <li class="page-item ${this.currentPage === i ? 'active' : ''}">
          <a class="page-link" href="#" data-page="${i}">${i}</a>
        </li>
      `;
    }

    if (endPage < this.totalPages) {
      pageNumbers += `
        ${endPage < this.totalPages - 1 ? '<li class="page-item disabled"><span class="page-link">...</span></li>' : ''}
        <li class="page-item">
          <a class="page-link" href="#" data-page="${this.totalPages}">${this.totalPages}</a>
        </li>
      `;
    }

    pageNumbers += `
      <li class="page-item ${this.currentPage === this.totalPages ? 'disabled' : ''}">
        <a class="page-link px-3" href="#" aria-label="Next" data-page="next">
          ${this._isArabic ? 'التالي' : 'Next'} <i class="bi bi-arrow-right"></i>
        </a>
      </li>
    `;

    paginationElement.innerHTML = pageNumbers;
    this.addPaginationEventListeners();
  }

  private addPaginationEventListeners(): void {
    const paginationItems = this.domElement.querySelectorAll('.page-link');
    paginationItems.forEach(item => {
      item.addEventListener('click', this.handlePaginationClick.bind(this));
    });
  }

  private handlePaginationClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    const page = target.getAttribute('data-page');
    if (page === 'prev' && this.currentPage > 1) {
      this.currentPage--;
    } else if (page === 'next' && this.currentPage < this.totalPages) {
      this.currentPage++;
    } else if (page !== 'prev' && page !== 'next') {
      this.currentPage = parseInt(page as string);
    }

    this.displayCurrentPage();
  }

  private setupSearchListener(): void {
    const searchInput = this.domElement.querySelector('#searchInput') as HTMLInputElement;
    searchInput.addEventListener('input', (event) => {
      this.searchTerm = (event.target as HTMLInputElement).value;
      this.searchEmployees();
    });
  }

  private async searchEmployees(): Promise<void> {
    const filteredEmployees = this.allEmployees.filter(employee =>
      employee.Author?.Title.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
    this.totalItems = filteredEmployees.length;
    this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
    this.currentPage = 1;
    this.displayCurrentPage(filteredEmployees);
  }

  private setupSortListener(): void {
    const sortItems = this.domElement.querySelectorAll('.dropdown-item');
    sortItems.forEach(item => {
      item.addEventListener('click', (event) => {
        const sortBy: any = (event.target as HTMLElement).getAttribute('data-sort');
        this.fetchSortedEmployees(sortBy);
      });
    });
  }

  private async fetchSortedEmployees(sortBy: string): Promise<void> {
    try {
      let orderByField = 'Created desc'; // default
  
      switch (sortBy) {
        case 'Name':
          orderByField = 'Author/Title asc';
          break;
        case 'Department':
          orderByField = 'Author/Department asc';
          break;
        case 'Nationality':
          orderByField = 'Nationality asc';
          break;
        case 'Publish Data (Oldest)':
          orderByField = 'Created asc';
          break;
        case 'Publish Data (Newest)':
          orderByField = 'Created desc';
          break;
      }
  
      const response = await this.context.spHttpClient.get(
        `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Testimonials')/items?$select=*,Author/Title,Author/JobTitle,Author/Department,Author/EMail,VideoLink,Created&$expand=Author&$orderby=${orderByField}`,
        SPHttpClient.configurations.v1
      );
  
      const data = await response.json();
      this.allEmployees = data.value;
      this.totalItems = this.allEmployees.length;
      this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
      this.currentPage = 1;
      this.displayCurrentPage();
    } catch (error) {
      console.error('Error fetching sorted employees:', error);
    }
  }
}