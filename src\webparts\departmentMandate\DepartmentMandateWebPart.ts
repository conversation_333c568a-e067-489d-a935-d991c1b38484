
import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';

export interface IDepartmentMandateItem {
  Id: number;
  Title: string;
  Title_AR: string;

}

export interface IDepartmentMandateWebPartProps {
  description: string;
}

export default class DepartmentMandateWebPart extends BaseClientSideWebPart<IDepartmentMandateWebPartProps> {

  private _mandateItems: IDepartmentMandateItem[] = [];
  private _isArabic: boolean = false;

  public async onInit(): Promise<void> {
    await super.onInit();

    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;

    await this._loadMandateItems();
  }

  public render(): void {
    const loadingText = this._isArabic ? 'جاري التحميل...' : 'Loading...';
    const noItemsText = this._isArabic ? 'لا توجد عناصر للعرض' : 'No items to display';
    const errorText = this._isArabic ? 'حدث خطأ أثناء تحميل البيانات' : 'Error loading data';

    this.domElement.innerHTML = `
      

      <div class="card department-mandate-container">
        ${this._renderMandateSection(loadingText, noItemsText, errorText)}
      </div>
    `;
  }

  private getListEndpoint(listTitle: string): string {
    const currentWebUrl = this.context.pageContext.web.absoluteUrl;
    const siteUrl = this.context.pageContext.site.absoluteUrl;
    const isSubSite = currentWebUrl.toLowerCase() !== siteUrl.toLowerCase();

    const baseUrl = isSubSite
      ? `${currentWebUrl}/_api/web/lists/getbytitle('${listTitle}')/items`
      : `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/items`;

    const queryParams: string[] = [];
    return queryParams.length > 0
      ? `${baseUrl}?${queryParams.join('&')}`
      : baseUrl;
  }

  private async _loadMandateItems(): Promise<void> {
    try {

      const listName = 'Department%20Mandate';


      const apiUrl = this.getListEndpoint(listName);


      const response: SPHttpClientResponse = await this.context.spHttpClient.get(
        apiUrl,
        SPHttpClient.configurations.v1
      );

      if (response.ok) {
        const data = await response.json();
        this._mandateItems = data.value || [];


        this.render();
      } else {
        console.error('Department Mandate - Failed to load items:', response.status, response.statusText);
        this._createSampleItems();
        this.render();
      }
    } catch (error) {
      console.error('Department Mandate - Error loading items:', error);
      this._createSampleItems();
      this.render();
    }
  }

  private _createSampleItems(): void {
    this._mandateItems = []

  }

  private _renderMandateSection(loadingText: string, noItemsText: string, errorText: string): string {
    if (!this._mandateItems || this._mandateItems.length === 0) {
      return `
        <div class="text-center py-5">
          <i class="bi bi-list-ul icon-lg text-muted"></i>
          <p class="text-muted mt-3">${noItemsText}</p>
          ${errorText ? `<small class="text-danger">${errorText}</small>` : ''}
        </div>
      `;
    }

    return `
      <div class="section-card bg-remover mandate  card-color pt-2 px-3 section-card bg-remover">
        <h2 class="fw-bold head-color my-2 tittle">${this._isArabic ? 'مهمة القسم' : 'Department Mandate'}</h2>
        
        
          <ul class="text-color">
            ${this._mandateItems.map(item => this._renderMandateItem(item)).join('')}
          </ul>
        
      </div>
    `;
  }

  private _renderMandateItem(item: IDepartmentMandateItem): string {

    return `
      <li>
        ${this._isArabic ? item.Title_AR || item.Title : item.Title || ''}
        </li>
    `;
  }
}