
import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';

export interface IVolunteerWebPartProps {
  description: string;
  title: string;
}

interface IVolunteerRequest {
  Title: string;
  Department: string;
  Requester: string;
  Email: string;
  Phone: string;
  ActivityTime: string;
  NoVolunteersRequired: string;
  TimesNeeded: number;
  OrientationTime: string;
  AgeRequirement: string;
  GenderRequirement: number;
  Nationality: string;
  ActivityDate: string;
  DatesNeeded: number;
  OrientationDate: string;
  LanguageRequirement: string;
  DescVoulnteerActivityTXT: string;
  SkillsTXT: string;
  DutiesTXT: string;
  SUN: number;
  MON: number;
  TUE: number;
  WED: number;
  THU: number;
  FRI: number;
  SAT: number;
  DateNeededFrom: number;
  DateNeededTo: number;
}

export default class VolunteerWebPart extends BaseClientSideWebPart<IVolunteerWebPartProps> {

  public render(): void {
    this.domElement.innerHTML = `
    
                    <form id="volunteerRequestForm">
                      <!-- GENERAL GUIDELINES -->
                      <div class="mb-4">
                        <h6 class="fw-bold head-color">GENERAL GUIDELINES:</h6>
                        <ul class="text-color small mb-2">
                          <li>Requests of less than 50 volunteers must be received at least 4 weeks prior to the date of the 1st orientation/induction.</li>
                          <li>Requests of over 50 volunteers must be received at least 6 weeks before the date of the 1st orientation.</li>
                          <li>Volunteers will be managed and supervised by the requester.</li>
                          <li>Requester must meet with the volunteers at least 10 days prior to the event.</li>
                        </ul>
                        <div class="alert alert-warning py-2 px-3 small mb-2 card-color text-color">
                          <span class="fw-semibold">Please note:</span> It’s the responsibility of the requester to provide <a href="#" class="btn-main-link ">food and beverages for the volunteers</a>.
                          <ul class="mb-0 mt-2">
                            <li><input class="form-check-input me-1" type="checkbox" checked  > 2 to 3 hours, each volunteer should be provided with sufficient beverages (water, juices).</li>
                            <li><input class="form-check-input me-1" type="checkbox"  > 3 to 6 hours, each volunteer should be provided with a decent meal (lunch box) & sufficient beverages (water, juices).</li>
                            <li><input class="form-check-input me-1" type="checkbox"  > 6+ hours, each volunteer should be provided with TWO (2) decent meals (lunch box) & sufficient beverages (water, juices).</li>
                          </ul>
                        </div>
                        <div class="text-danger small fw-semibold mb-2">Failure to provide meal and beverage, the Community Service regrets to decline your Volunteer Request form.</div>
                      </div>

                      <!-- REQUESTER DETAILS -->
                      <div class="mb-4">
                        <div class="bg-secondary bg-opacity-10 py-2 px-3 mb-3 rounded"><span class="fw-bold head-color">REQUESTER DETAILS</span></div>
                        <div class="row g-3 align-items-end">
                          <div class="col-md-4">
                            <label class="form-label text-color">Museum/Department/Section <span class="text-danger">*</span></label>
                            <input type="text" name="department" class="form-control" required>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Requester Name <span class="text-danger">*</span></label>
                            <input type="text" name="requester" class="form-control" required>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Email <span class="text-danger">*</span></label>
                            <input type="email" name="email" class="form-control" required>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Phone <span class="text-danger">*</span></label>
                            <input type="tel" name="phone" class="form-control" required>
                          </div>
                        </div>
                      </div>

                      <!-- ACTIVITY DETAILS -->
                      <div class="mb-4">
                        <div class="bg-secondary bg-opacity-10 py-2 px-3 mb-3 rounded"><span class="fw-bold head-color">ACTIVITY DETAILS</span></div>
                        <div class="mb-3">
                          <label class="form-label text-color">Please describe this volunteering activity with as much details as possible, information will be shared with the volunteers. Please attach any document(s) related to your project or activity. <span class="text-danger">*</span></label>
                          <textarea name="activityDesc" class="form-control" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                          <label class="form-label text-color">Attachments: <span class="text-danger">*</span></label>
                          <input type="file" class="form-control" required>
                        </div>
                        <div class="row g-3">
                          <div class="col-md-4">
                            <label class="form-label text-color">Location of the Event: <span class="text-danger">*</span></label>
                            <input type="text" name="location" class="form-control" required>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Activity Date(s): <span class="text-danger">*</span></label>
                            <input type="text" name="activityDate" class="form-control datepicker" required>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Activity Time(s): <span class="text-danger">*</span></label>
                            <input type="text" name="activityTime" class="form-control" required>
                          </div>
                        </div>
                      </div>

                      <!-- VOLUNTEER REQUIREMENT -->
                      <div class="mb-4">
                        <div class="bg-secondary bg-opacity-10 py-2 px-3 mb-3 rounded"><span class="fw-bold head-color">VOLUNTEER REQUIREMENT</span></div>
                        <div class="row g-3 mb-3">
                          <div class="col-md-4">
                            <label class="form-label text-color">No. of Volunteers Needed: <span class="text-danger">*</span></label>
                            <input type="number" name="noVolunteers" class="form-control" min="1" required>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Dates Needed From: <span class="text-danger">*</span></label>
                            <input type="text" name="dateFrom" class="form-control datepicker" required>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Dates Needed To: <span class="text-danger">*</span></label>
                            <input type="text" name="dateTo" class="form-control datepicker" required>
                          </div>
                        </div>
                        <div class="mb-3">
                          <label class="form-label text-color">Days Needed:</label>
                          <div class="d-flex flex-wrap gap-2">
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" name="sun" id="daySun">
                              <label class="form-check-label text-color" for="daySun">SUN</label>
                            </div>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" name="mon" id="dayMon">
                              <label class="form-check-label text-color" for="dayMon">MON</label>
                            </div>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" name="tue" id="dayTue">
                              <label class="form-check-label text-color" for="dayTue">TUE</label>
                            </div>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" name="wed" id="dayWed">
                              <label class="form-check-label text-color" for="dayWed">WED</label>
                            </div>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" name="thu" id="dayThu">
                              <label class="form-check-label text-color" for="dayThu">THU</label>
                            </div>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" name="fri" id="dayFri">
                              <label class="form-check-label text-color" for="dayFri">FRI</label>
                            </div>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" name="sat" id="daySat">
                              <label class="form-check-label text-color" for="daySat">SAT</label>
                            </div>
                          </div>
                        </div>
                        <div class="row g-3 mb-3">
                          <div class="col-md-6">
                            <label class="form-label text-color">Morning Shift <span class="small">(Specify the timings Start time and End time)</span></label>
                            <div class="row g-2">
                              <div class="col">
                                <input type="text" class="form-control" placeholder="From">
                              </div>
                              <div class="col">
                                <input type="text" class="form-control" placeholder="To">
                              </div>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <label class="form-label text-color">Afternoon Shift <span class="text-danger">*</span> <span class="small">(Specify the timings Start time and End time)</span></label>
                            <div class="row g-2">
                              <div class="col">
                                <input type="text" class="form-control" placeholder="From" required>
                              </div>
                              <div class="col">
                                <input type="text" class="form-control" placeholder="To" required>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="row g-3 mb-3">
                          <div class="col-md-4">
                            <label class="form-label text-color">Orientation Date:</label>
                            <input type="text" class="form-control datepicker">
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Orientation Time:</label>
                            <input type="text" class="form-control">
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Nationality: <span class="small">(if required)</span></label>
                            <input type="text" class="form-control">
                          </div>
                        </div>
                        <div class="row g-3 mb-3">
                          <div class="col-md-4">
                            <label class="form-label text-color">Age Requirement: <span class="small">(if required)</span></label>
                            <input type="text" class="form-control">
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Gender Requirement: <span class="small">(if required)</span></label>
                            <div class="d-flex gap-3 align-items-center mt-1">
                              <div class="form-check">
                                <input class="form-check-input" type="radio" name="genderReq" id="genderMale" value="male">
                                <label class="form-check-label text-color" for="genderMale">Male</label>
                              </div>
                              <div class="form-check">
                                <input class="form-check-input" type="radio" name="genderReq" id="genderFemale" value="female">
                                <label class="form-check-label text-color" for="genderFemale">Female</label>
                              </div>
                              <div class="form-check">
                                <input class="form-check-input" type="radio" name="genderReq" id="genderNA" value="na" checked>
                                <label class="form-check-label text-color" for="genderNA">N/A</label>
                              </div>
                            </div>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Language Requirement: <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" required>
                          </div>
                        </div>
                      </div>

                      <!-- Skills and Duties -->
                      <div class="mb-3">
                        <label class="form-label text-color">Please list the skills and abilities required: <span class="text-danger">*</span></label>
                        <textarea name="skills" class="form-control" rows="2" required></textarea>
                      </div>
                      <div class="mb-3">
                        <label class="form-label text-color">Please list the duties volunteers would do, be as specific as possible: <span class="text-danger">*</span></label>
                        <textarea name="duties" class="form-control" rows="2" required></textarea>
                      </div>

                      <div class="text-danger small fw-semibold mb-3">*The Volunteer Programs reserves the right to return the request to the concerned department in case of missing or unclear information.</div>

                      <div class="d-flex justify-content-center gap-3 mt-4">
                        <button type="submit" class="btn btn-main text-white px-4">SUBMIT</button>
                        <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">CLOSE</button>
                      </div>
                    </form>
                 `;

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    const form = this.domElement.querySelector('#volunteerRequestForm') as HTMLFormElement;
    if (form) {
      form.addEventListener('submit', this.handleFormSubmit.bind(this));
    }
  }

  private async handleFormSubmit(event: Event): Promise<void> {
    event.preventDefault();

    const form = event.target as HTMLFormElement;
    const formData = new FormData(form);

    // Validate required fields
    if (!this.validateForm(form)) {
      this.showAlert('error', 'Validation Error', 'Please fill in all required fields.');
      return;
    }

    // Show confirmation dialog
    const result = await this.showConfirmDialog();
    if (!result.isConfirmed) {
      return;
    }

    try {
      // Prepare data for SharePoint
      const volunteerRequest = this.prepareFormData(formData);

      // Submit to SharePoint
      await this.submitToSharePoint(volunteerRequest);

      // Show success message
      this.showAlert('success', 'Success!', 'Your volunteer request has been submitted successfully.');

      // Reset form
      form.reset();

    } catch (error) {
      console.error('Error submitting form:', error);
      this.showAlert('error', 'Error', 'Failed to submit your request. Please try again.');
    }
  }

  private validateForm(form: HTMLFormElement): boolean {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
      const input = field as HTMLInputElement | HTMLTextAreaElement;
      if (!input.value.trim()) {
        input.classList.add('is-invalid');
        isValid = false;
      } else {
        input.classList.remove('is-invalid');
      }
    });

    return isValid;
  }

  private prepareFormData(formData: FormData): IVolunteerRequest {
    console.log('Form data entries:');
    // Debug form data
    console.log('Title:', formData.get('title'));
    console.log('Department:', formData.get('department'));
    console.log('Requester:', formData.get('requester'));

    return {
      Title: formData.get('title') as string || 'Volunteer Request',
      Department: formData.get('department') as string || '',
      Requester: formData.get('requester') as string || '',
      Email: formData.get('email') as string || '',
      Phone: formData.get('phone') as string || '',
      ActivityTime: formData.get('activityTime') as string || '',
      NoVolunteersRequired: formData.get('noVolunteers') as string || '',
      TimesNeeded: 1, // Default value
      OrientationTime: '', // Not in current form
      AgeRequirement: '', // Not in current form
      GenderRequirement: this.getGenderRequirement(formData.get('genderReq') as string),
      Nationality: '', // Not in current form
      ActivityDate: formData.get('activityDate') as string || '',
      DatesNeeded: 1, // Default value
      OrientationDate: '', // Not in current form
      LanguageRequirement: '', // Not in current form
      DescVoulnteerActivityTXT: formData.get('activityDesc') as string || '',
      SkillsTXT: formData.get('skills') as string || '',
      DutiesTXT: formData.get('duties') as string || '',
      SUN: formData.has('sun') ? 1 : 0,
      MON: formData.has('mon') ? 1 : 0,
      TUE: formData.has('tue') ? 1 : 0,
      WED: formData.has('wed') ? 1 : 0,
      THU: formData.has('thu') ? 1 : 0,
      FRI: formData.has('fri') ? 1 : 0,
      SAT: formData.has('sat') ? 1 : 0,
      DateNeededFrom: this.convertDateToNumber(formData.get('dateFrom') as string),
      DateNeededTo: this.convertDateToNumber(formData.get('dateTo') as string)
    };
  }

  private convertDateToNumber(dateString: string): number {
    if (!dateString) return 0;
    try {
      const date = new Date(dateString);
      return date.getTime();
    } catch {
      return 0;
    }
  }

  private getGenderRequirement(value: string): number {
    switch (value) {
      case 'male': return 1;
      case 'female': return 2;
      case 'na': return 0;
      default: return 0;
    }
  }

  private async submitToSharePoint(data: IVolunteerRequest): Promise<void> {
    const listUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('VolunteerRequest')/items`;

    const body = JSON.stringify(data);

    const response: SPHttpClientResponse = await this.context.spHttpClient.post(
      listUrl,
      SPHttpClient.configurations.v1,
      {
        headers: {
          'Accept': 'application/json;odata=nometadata',
          'Content-type': 'application/json;odata=nometadata',
          'odata-version': ''
        },
        body: body
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
  }

  private async showConfirmDialog(): Promise<any> {
    return new Promise((resolve) => {
      // Create SweetAlert-like confirmation dialog
      const swalHtml = `
        <div id="swalOverlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
          <div style="background: white; padding: 2rem; border-radius: 0.5rem; max-width: 400px; text-align: center; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
            <div style="color: #f39c12; font-size: 3rem; margin-bottom: 1rem;">⚠️</div>
            <h3 style="margin-bottom: 1rem; color: #333;">Confirm Submission</h3>
            <p style="margin-bottom: 2rem; color: #666;">Are you sure you want to submit this volunteer request?</p>
            <div style="display: flex; gap: 1rem; justify-content: center;">
              <button id="swalConfirm" style="background: #3085d6; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 0.25rem; cursor: pointer;">Yes, Submit!</button>
              <button id="swalCancel" style="background: #aaa; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 0.25rem; cursor: pointer;">Cancel</button>
            </div>
          </div>
        </div>
      `;

      document.body.insertAdjacentHTML('beforeend', swalHtml);

      const overlay = document.getElementById('swalOverlay');
      const confirmBtn = document.getElementById('swalConfirm');
      const cancelBtn = document.getElementById('swalCancel');

      const cleanup = () => {
        if (overlay) overlay.remove();
      };

      confirmBtn?.addEventListener('click', () => {
        cleanup();
        resolve({ isConfirmed: true });
      });

      cancelBtn?.addEventListener('click', () => {
        cleanup();
        resolve({ isConfirmed: false });
      });

      overlay?.addEventListener('click', (e) => {
        if (e.target === overlay) {
          cleanup();
          resolve({ isConfirmed: false });
        }
      });
    });
  }

  private showAlert(type: 'success' | 'error', title: string, message: string): void {
    const icon = type === 'success' ? '✅' : '❌';
    const color = type === 'success' ? '#28a745' : '#dc3545';

    const swalHtml = `
      <div id="swalAlert" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
        <div style="background: white; padding: 2rem; border-radius: 0.5rem; max-width: 400px; text-align: center; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
          <div style="color: ${color}; font-size: 3rem; margin-bottom: 1rem;">${icon}</div>
          <h3 style="margin-bottom: 1rem; color: #333;">${title}</h3>
          <p style="margin-bottom: 2rem; color: #666;">${message}</p>
          <button id="swalOk" style="background: ${color}; color: white; border: none; padding: 0.75rem 2rem; border-radius: 0.25rem; cursor: pointer;">OK</button>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', swalHtml);

    const alertDiv = document.getElementById('swalAlert');
    const okBtn = document.getElementById('swalOk');

    const cleanup = () => {
      if (alertDiv) alertDiv.remove();
    };

    okBtn?.addEventListener('click', cleanup);
    alertDiv?.addEventListener('click', (e) => {
      if (e.target === alertDiv) cleanup();
    });

    // Auto close after 5 seconds
    setTimeout(cleanup, 5000);
  }
}
