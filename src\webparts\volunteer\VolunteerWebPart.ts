
import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';



export default class VolunteerWebPart extends BaseClientSideWebPart<IVolunteerWebPartProps> {

  public render(): void {
    this.domElement.innerHTML = `
    
                    <form id="volunteerRequestForm">
                      <!-- GENERAL GUIDELINES -->
                      <div class="mb-4">
                        <h6 class="fw-bold head-color">GENERAL GUIDELINES:</h6>
                        <ul class="text-color small mb-2">
                          <li>Requests of less than 50 volunteers must be received at least 4 weeks prior to the date of the 1st orientation/induction.</li>
                          <li>Requests of over 50 volunteers must be received at least 6 weeks before the date of the 1st orientation.</li>
                          <li>Volunteers will be managed and supervised by the requester.</li>
                          <li>Requester must meet with the volunteers at least 10 days prior to the event.</li>
                        </ul>
                        <div class="alert alert-warning py-2 px-3 small mb-2 card-color text-color">
                          <span class="fw-semibold">Please note:</span> It’s the responsibility of the requester to provide <a href="#" class="btn-main-link ">food and beverages for the volunteers</a>.
                          <ul class="mb-0 mt-2">
                            <li><input class="form-check-input me-1" type="checkbox" checked  > 2 to 3 hours, each volunteer should be provided with sufficient beverages (water, juices).</li>
                            <li><input class="form-check-input me-1" type="checkbox"  > 3 to 6 hours, each volunteer should be provided with a decent meal (lunch box) & sufficient beverages (water, juices).</li>
                            <li><input class="form-check-input me-1" type="checkbox"  > 6+ hours, each volunteer should be provided with TWO (2) decent meals (lunch box) & sufficient beverages (water, juices).</li>
                          </ul>
                        </div>
                        <div class="text-danger small fw-semibold mb-2">Failure to provide meal and beverage, the Community Service regrets to decline your Volunteer Request form.</div>
                      </div>

                      <!-- REQUESTER DETAILS -->
                      <div class="mb-4">
                        <div class="bg-secondary bg-opacity-10 py-2 px-3 mb-3 rounded"><span class="fw-bold head-color">REQUESTER DETAILS</span></div>
                        <div class="row g-3 align-items-end">
                          <div class="col-md-4">
                            <label class="form-label text-color">Museum/Department/Section <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" required>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Requester Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" required>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" required>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Phone <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" required>
                          </div>
                        </div>
                      </div>

                      <!-- ACTIVITY DETAILS -->
                      <div class="mb-4">
                        <div class="bg-secondary bg-opacity-10 py-2 px-3 mb-3 rounded"><span class="fw-bold head-color">ACTIVITY DETAILS</span></div>
                        <div class="mb-3">
                          <label class="form-label text-color">Please describe this volunteering activity with as much details as possible, information will be shared with the volunteers. Please attach any document(s) related to your project or activity. <span class="text-danger">*</span></label>
                          <textarea class="form-control" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                          <label class="form-label text-color">Attachments: <span class="text-danger">*</span></label>
                          <input type="file" class="form-control" required>
                        </div>
                        <div class="row g-3">
                          <div class="col-md-4">
                            <label class="form-label text-color">Location of the Event: <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" required>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Activity Date(s): <span class="text-danger">*</span></label>
                            <input type="text" class="form-control datepicker" required>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Activity Time(s): <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" required>
                          </div>
                        </div>
                      </div>

                      <!-- VOLUNTEER REQUIREMENT -->
                      <div class="mb-4">
                        <div class="bg-secondary bg-opacity-10 py-2 px-3 mb-3 rounded"><span class="fw-bold head-color">VOLUNTEER REQUIREMENT</span></div>
                        <div class="row g-3 mb-3">
                          <div class="col-md-4">
                            <label class="form-label text-color">No. of Volunteers Needed: <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" min="1" required>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Dates Needed From: <span class="text-danger">*</span></label>
                            <input type="text" class="form-control datepicker" required>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Dates Needed To: <span class="text-danger">*</span></label>
                            <input type="text" class="form-control datepicker" required>
                          </div>
                        </div>
                        <div class="mb-3">
                          <label class="form-label text-color">Days Needed:</label>
                          <div class="d-flex flex-wrap gap-2">
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" id="daySun">
                              <label class="form-check-label text-color" for="daySun">SUN</label>
                            </div>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" id="dayMon">
                              <label class="form-check-label text-color" for="dayMon">MON</label>
                            </div>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" id="dayTue">
                              <label class="form-check-label text-color" for="dayTue">TUE</label>
                            </div>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" id="dayWed">
                              <label class="form-check-label text-color" for="dayWed">WED</label>
                            </div>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" id="dayThu">
                              <label class="form-check-label text-color" for="dayThu">THU</label>
                            </div>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" id="dayFri">
                              <label class="form-check-label text-color" for="dayFri">FRI</label>
                            </div>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" id="daySat">
                              <label class="form-check-label text-color" for="daySat">SAT</label>
                            </div>
                          </div>
                        </div>
                        <div class="row g-3 mb-3">
                          <div class="col-md-6">
                            <label class="form-label text-color">Morning Shift <span class="small">(Specify the timings Start time and End time)</span></label>
                            <div class="row g-2">
                              <div class="col">
                                <input type="text" class="form-control" placeholder="From">
                              </div>
                              <div class="col">
                                <input type="text" class="form-control" placeholder="To">
                              </div>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <label class="form-label text-color">Afternoon Shift <span class="text-danger">*</span> <span class="small">(Specify the timings Start time and End time)</span></label>
                            <div class="row g-2">
                              <div class="col">
                                <input type="text" class="form-control" placeholder="From" required>
                              </div>
                              <div class="col">
                                <input type="text" class="form-control" placeholder="To" required>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="row g-3 mb-3">
                          <div class="col-md-4">
                            <label class="form-label text-color">Orientation Date:</label>
                            <input type="text" class="form-control datepicker">
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Orientation Time:</label>
                            <input type="text" class="form-control">
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Nationality: <span class="small">(if required)</span></label>
                            <input type="text" class="form-control">
                          </div>
                        </div>
                        <div class="row g-3 mb-3">
                          <div class="col-md-4">
                            <label class="form-label text-color">Age Requirement: <span class="small">(if required)</span></label>
                            <input type="text" class="form-control">
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Gender Requirement: <span class="small">(if required)</span></label>
                            <div class="d-flex gap-3 align-items-center mt-1">
                              <div class="form-check">
                                <input class="form-check-input" type="radio" name="genderReq" id="genderMale" value="male">
                                <label class="form-check-label text-color" for="genderMale">Male</label>
                              </div>
                              <div class="form-check">
                                <input class="form-check-input" type="radio" name="genderReq" id="genderFemale" value="female">
                                <label class="form-check-label text-color" for="genderFemale">Female</label>
                              </div>
                              <div class="form-check">
                                <input class="form-check-input" type="radio" name="genderReq" id="genderNA" value="na" checked>
                                <label class="form-check-label text-color" for="genderNA">N/A</label>
                              </div>
                            </div>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label text-color">Language Requirement: <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" required>
                          </div>
                        </div>
                      </div>

                      <!-- Skills and Duties -->
                      <div class="mb-3">
                        <label class="form-label text-color">Please list the skills and abilities required: <span class="text-danger">*</span></label>
                        <textarea class="form-control" rows="2" required></textarea>
                      </div>
                      <div class="mb-3">
                        <label class="form-label text-color">Please list the duties volunteers would do, be as specific as possible: <span class="text-danger">*</span></label>
                        <textarea class="form-control" rows="2" required></textarea>
                      </div>

                      <div class="text-danger small fw-semibold mb-3">*The Volunteer Programs reserves the right to return the request to the concerned department in case of missing or unclear information.</div>

                      <div class="d-flex justify-content-center gap-3 mt-4">
                        <button type="submit" class="btn btn-main text-white px-4">SUBMIT</button>
                        <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">CLOSE</button>
                      </div>
                    </form>
                 `
  }


}
