import { BaseClientSideWebPart } from "@microsoft/sp-webpart-base";
import { SPHttpClient } from "@microsoft/sp-http";
import Swal from "sweetalert2";
import { AadHttpClient } from "@microsoft/sp-http";

export interface IEmplolyeesWebPartProps {
  titleEnglish: string;
  titleArabic: string;
  itemsPerPage: number;
  selectedDepartment: string;
  defaultSortField: string;
  defaultSortDirection: string;
}

interface IEmployee {
  AccountName: string;
  DisplayName: string;
  Email: string;
  Title: string;
  DirectReports: string[];
  ExtendedReports: string[];
  ExtendedManagers: string[];
  PictureUrl: string;
  PersonalUrl: string;
  UserUrl: string;
  Communities?: {
    id: string;
    name: string;
    description: string;
    visibility: string;
    mail: string;
    photoUrl: string;
  }[];
  FirstName: string;
  LastName: string;
  PreferredName: string;
  WorkPhone: string;
  HomePhone: string;
  CellPhone: string;
  Department: string;
  SPSDepartment: string;
  Manager: string;
  SPSJobTitle: string;
  SPSHireDate: string;
  Nationality: string;
  WorkEmail: string;
  Office: string;
  SPSLocation: string;
  AboutMe: string;
  SPSSkills: string;
  SPSInterests: string;
  SPSSchool: string;
  SPSBirthday: string;
  UserName: string;

  Id: number;
  UserProfileProperties: Array<{
    Key: string;
    Value: string;
    ValueType: string;
  }>;
}

export default class EmplolyeesWebPart extends BaseClientSideWebPart<IEmplolyeesWebPartProps> {
  private _employees: IEmployee[] = [];
  private _filteredEmployees: IEmployee[] = [];
  private _isArabic: boolean = false;
  private _currentUserDepartment: string | undefined;
  private _currentPage: number = 1;
  private _totalPages: number = 1;
  private _activeFilter: string = "all";
  private _searchQuery: string = "";
  private _sortField: string = "Title";
  private _sortDirection: string = "asc";
  _searchTimeout: any = null;
  private _isLoading: boolean = false;

  private _testimonialHtml: string = "";

  public get properties(): IEmplolyeesWebPartProps {
    return {
      titleEnglish: "Employee Directory",
      titleArabic: "دليل الموظفين",
      itemsPerPage: 10,
      selectedDepartment: "",
      defaultSortField: "Title",
      defaultSortDirection: "asc",
    };
  }

  private getItemsPerPage(): number {
    return this.properties?.itemsPerPage || 10;
  }

  public render(): void {
    this._isArabic = this.detectArabicLanguage();

    if (this.properties.defaultSortField) {
      this._sortField = this.properties.defaultSortField;
    }

    if (this.properties.defaultSortDirection) {
      this._sortDirection = this.properties.defaultSortDirection;
    }

    this.addCustomStyles();

    if (this._isLoading) {
      this.renderLoadingState();
    } else if (!this._employees || this._employees.length === 0) {
      this.renderEmptyState();
    } else {
      this.filterEmployees();
      this.renderContents();
    }
  }

  private detectArabicLanguage(): boolean {
    return (
      document.documentElement.lang === "ar" ||
      window.location.pathname.toLowerCase().includes("/ar/")
    );
  }

  private addCustomStyles(): void {
    const styleId = "employee-directory-styles";
    if (!document.getElementById(styleId)) {
      const style = document.createElement("style");
      style.id = styleId;
      style.textContent = `
        :root {
          --pink: #ed3b88;
          --orange: #FFC200;
          --blue: #1292E5;
          --black: #000;
          --white: #fff;
          --gray: #cfcdcd;
          --main-color: var(--pink);
          --bg-color: #F7F7F7;
          --text-color: #333;
          --border-color: var(--gray);
          --head-color: var(--black);
          --card-color: white;
        }

        .employee-directory .head-color {
          color: var(--head-color) !important;
        }

        .employee-directory .main-color {
          color: var(--main-color) !important;
        }

        .employee-directory .card-color {
          background-color: var(--card-color) !important;
          border-color: var(--border-color) !important;
        }

        .employee-directory .btn-main {
          background-color: var(--main-color) !important;
          border-color: var(--main-color) !important;
          color: white !important;
        }

        .employee-directory .btn-main:hover {
          background-color: var(--orange) !important;
          border-color: var(--orange) !important;
        }

        .employee-directory .people-card {
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
          height: 350px;
        }

        .employee-directory .people-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .employee-directory .card-front {
          transition: all 0.3s ease;
        }

        .employee-directory .card-back {
          opacity: 0;
          transform: translateY(100%);
          transition: all 0.3s ease;
        }

        .employee-directory .people-card:hover .card-front {
          opacity: 0;
          transform: translateY(-100%);
        }

        .employee-directory .people-card:hover .card-back {
          opacity: 1;
          transform: translateY(0);
        }

        .employee-directory .contact-options a {
          background-color: #f8f9fa;
          border-radius: 8px;
          text-decoration: none;
          color: var(--main-color);
          transition: all 0.3s ease;
          border: 1px solid var(--border-color);
        }

        .employee-directory .contact-options a:hover {
          background-color: var(--main-color);
          color: white;
          transform: translateY(-2px);
        }

        .employee-directory .department-card {
          transition: all 0.3s ease;
          cursor: pointer;
        }

        .employee-directory .department-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 25px rgba(0,0,0,0.1);
          border-color: var(--main-color) !important;
        }

        .employee-directory .communities-card:hover {
          transform: translateY(-3px) !important;
          box-shadow: 0 8px 20px rgba(0,0,0,0.15) !important;
          border-color: var(--main-color) !important;
        }

        .employee-directory .communities-card a {
          text-decoration: none !important;
        }

        .employee-directory .communities-card .join-group-btn {
          z-index: 10;
          position: relative;
        }

        .employee-directory .nav-tabs .nav-link.active {
          background-color: var(--main-color) !important;
          border-color: var(--main-color) !important;
          color: white !important;
        }

        .employee-directory .nav-tabs .nav-link {
          color: var(--text-color);
          border-color: var(--border-color);
        }

        .employee-directory .nav-tabs .nav-link:hover {
          border-color: var(--main-color);
          color: var(--main-color);
        }

        .employee-directory .pagination .page-link {
          color: var(--main-color);
          border-color: var(--border-color);
        }

        .employee-directory .pagination .page-item.active .page-link {
          background-color: var(--main-color);
          border-color: var(--main-color);
        }

        .employee-directory .pagination .page-link:hover {
          background-color: var(--main-color);
          border-color: var(--main-color);
          color: white;
        }

        @media (max-width: 768px) {
          .employee-directory .people-card {
            height: auto;
            min-height: 300px;
          }

          .employee-directory .card-back {
            position: relative;
            opacity: 1;
            transform: none;
            margin-top: 20px;
          }

          .employee-directory .people-card:hover .card-front {
            opacity: 1;
            transform: none;
          }
        }
      `;
      document.head.appendChild(style);
    }
  }

  private renderLoadingState(): void {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);
    (this as any).domElement.innerHTML = `
      <div class="employee-directory">
        <div class="text-center py-5">
          <div class="spinner-border main-color" role="status">
            <span class="visually-hidden">${t(
      "Loading...",
      "جاري التحميل..."
    )}</span>
          </div>
          <h3 class="mt-3 text-muted">${t(
      "Loading employees...",
      "جاري تحميل الموظفين..."
    )}</h3>
        </div>
      </div>
    `;
  }

  private renderEmptyState(): void {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);
    (this as any).domElement.innerHTML = `
      <div class="employee-directory">
        <div class="text-center py-5">
          <i class="bi bi-people fs-1 text-muted mb-3"></i>
          <h3 class="text-muted">${t(
      "No employees found",
      "لا يوجد موظفين"
    )}</h3>
          <p class="text-muted">${t(
      "Please check back later or contact your administrator.",
      "يرجى المحاولة لاحقاً أو الاتصال بالمسؤول."
    )}</p>
        </div>
      </div>
    `;
  }

  private renderContents(): void {
    this.filterEmployees();

    (this as any).domElement.innerHTML = `
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
      .people-card {
        position: relative;
        overflow: hidden;
        transition: transform 0.3s ease;
        cursor: pointer;
        height: 350px;
      }

      .people-card:hover {
        transform: translateY(-5px);
      }

      .card-front {
        transition: opacity 0.3s ease;
      }

      .card-back {
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
      }

      .people-card:hover .card-front {
        opacity: 0;
      }

      .people-card:hover .card-back {
        opacity: 1;
        pointer-events: all;
      }

      .profile-img {
        width: 60px;
        height: 60px;
      }

      .contact-options a {
        text-decoration: none;
        color: #ed3c8a;
        border-radius: 8px;
        transition: background-color 0.2s ease;
      }

      .contact-options a:hover {
        background-color: #f8f9fa;
        color: #ed3c8a;
      }

      .contact-options svg {
        width: 20px;
        height: 20px;
      }

      .report-to img {
        border: 2px solid #ed3c8a;
      }
    </style>
    <div class="employee-directory">
      <div class="container-fluid">
        <div class="row mb-4">
          <div class="col-12">
           
          </div>
        </div>
        <div class="row">
          <div class="col-lg-3 mb-4">
            ${this.getFilterSidebarHtml()}
          </div>
          <div class="col-lg-9">
            ${this.renderFilterTabs()}
            <div id="pagination-container">
              ${this._activeFilter !== "departments"
        ? this.renderPagination()
        : ""
      }
            </div>
          </div>
        </div>
      </div>
      ${this.renderModalWithDynamicData(
        this._employees[0] || ({} as IEmployee)
      )}
    </div>
  `;
    const container = (this as any).domElement.querySelector("#modal-testimonial");
    if (container) {
      container.innerHTML = this._testimonialHtml;
    } else {
      console.warn("⚠️ #modal-testimonial not found in DOM");
    }
    this.attachEventListeners();

    this.updateTabContent(this._activeFilter);
  }

  private getFilterSidebarHtml(): string {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);
    const departments = this.extractDepartments();
    const nationalities = this.extractNationalities();
    const jobTitles = this.extractJobTitles();
    return `
    <div class="card card-color mb-4">
      <div class="card-body">
        <h3 class="fw-bold head-color mb-3">${t("Filters", "الفلترة")}</h3>
        <hr>
        <div class="mb-3">
          <label for="nameSearch" class="form-label">${t(
      "Name",
      "الاسم"
    )}</label>
          <div class="input-group">
            <input type="text" class="form-control" id="nameSearch" placeholder="${t(
      "Search",
      "ابحث"
    )}">
            <button class="btn btn-outline-secondary" type="button">
              <i class="bi bi-search"></i>
            </button>
          </div>
        </div>
        <div class="mb-3">
          <label class="form-label head-color">${t(
      "Job Title",
      "المنصب"
    )}</label>
          <select class="form-select" id="jobTitleFilter">
            <option value="all">${t("All Job Titles", "كل المناصب")}</option>
            ${jobTitles
        .map(
          (title) => `
              <option value="${title}">${title} (${this._employees.filter((e) => e.SPSJobTitle === title).length
            })</option>
            `
        )
        .join("")}
          </select>
        </div>
        <div class="mb-3">
          <label class="form-label head-color">${t(
          "Departments",
          "الأقسام"
        )}</label>
          <select class="form-select" id="departmentFilter">
            <option value="all">${t("All Departments", "كل الأقسام")}</option>
            ${departments
        .map(
          (dept) => `
              <option value="${dept}">${dept} (${this._employees.filter((e) => e.Department === dept).length
            })</option>
            `
        )
        .join("")}
          </select>
        </div>
        <div class="mb-3">
          <label class="form-label head-color">${t(
          "Nationality",
          "الجنسية"
        )}</label>
          <select class="form-select" id="nationalityFilter">
            <option value="all">${t(
          "All Nationalities",
          "كل الجنسيات"
        )}</option>
            ${nationalities
        .map(
          (nat) => `
              <option value="${nat}">${nat} (${this._employees.filter((e) => e.Nationality === nat).length
            })</option>
            `
        )
        .join("")}
          </select>
        </div>
        <div class="d-grid gap-2 mt-4">
          <button type="button" class="btn btn-main fw-bold text-white btn-apply-filter">${t(
          "Apply Filter",
          "تطبيق الفلتر"
        )}</button>
          <button type="button" class="btn btn-outline-dark fw-bold btn-reset-filter">${t(
          "Reset",
          "إعادة تعيين"
        )}</button>
        </div>
      </div>
    </div>
  `;
  }

  private extractNationalities(): string[] {
    const nationalities = new Set<string>();
    this._employees.forEach((employee) => {
      if (employee.Nationality) {
        nationalities.add(employee.Nationality.trim());
      }
    });
    return Array.from(nationalities).sort();
  }

  private renderFilterTabs(): string {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);

    return `
    <div class="filter-header-container">
      <!-- Header with Tabs and Sort -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="nav nav-tabs" id="employeeTabs" role="tablist">
          <button class="nav-link ${this._activeFilter === "all" ? "active" : ""
      }"
                  id="all-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#all-content"
                  type="button"
                  role="tab"
                  aria-controls="all-content"
                  aria-selected="${this._activeFilter === "all"}">
            ${t("All Employees", "جميع الموظفين")} (${this._employees.length})
          </button>
          <button class="nav-link ${this._activeFilter === "new" ? "active" : ""
      }"
                  id="new-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#new-content"
                  type="button"
                  role="tab"
                  aria-controls="new-content"
                  aria-selected="${this._activeFilter === "new"}">
            ${t("New Employees", "الموظفين الجدد")} (${this._employees.filter((emp) => this.isNewEmployee(emp)).length
      })
          </button>
          <button class="nav-link ${this._activeFilter === "departments" ? "active" : ""
      }"
                  id="departments-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#departments-content"
                  type="button"
                  role="tab"
                  aria-controls="departments-content"
                  aria-selected="${this._activeFilter === "departments"}">
            ${t("My Departments", "أقسامي")} 
          </button>
        </div>

        <!-- Sort Dropdown -->
        <div class="dropdown">
          <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                  id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="bi bi-sort-down me-2"></i>${t(
        "Sort by: ",
        "الترتيب حسب: "
      )}${this.getSortFieldName(this._sortField)}
          </button>
          <ul class="dropdown-menu" aria-labelledby="sortDropdown">
            <li><a class="dropdown-item" href="#" data-direction="desc" data-sort="DisplayName">${t(
        "Name",
        "الاسم"
      )}</a></li>
            <li><a class="dropdown-item" data-direction="desc" href="#" data-sort="Department">${t(
        "Department",
        "القسم"
      )}</a></li>
            <li><a class="dropdown-item" data-direction="desc" href="#" data-sort="Nationality">${t(
        "Nationality",
        "الجنسية"
      )}</a>
            <li><a class="dropdown-item" href="#" data-sort="SPS-HireDate" data-direction="asc">${t(
        "Joining Date (Oldest)",
        "تاريخ التوظيف (الأقدم)"
      )}</a></li>
            <li><a class="dropdown-item" href="#" data-sort="SPS-HireDate" data-direction="desc">${t(
        "Joining Date (Newest)",
        "تاريخ التوظيف (الأحدث)"
      )}</a></li>
          </ul>
        </div>
      </div>
      <!-- Tab Content -->
      <div class="tab-content mb-4" id="employeeTabContent">
        <div class="tab-pane fade ${this._activeFilter === "all" ? "show active" : ""
      }"
             id="all-content"
             role="tabpanel">
          <!-- Content will be loaded dynamically -->
        </div>
        <div class="tab-pane fade ${this._activeFilter === "new" ? "show active" : ""
      }"
             id="new-content"
             role="tabpanel">
          <!-- Content will be loaded dynamically -->
        </div>
        <div class="tab-pane fade ${this._activeFilter === "departments" ? "show active" : ""
      }"
             id="departments-content"
             role="tabpanel">
          <!-- Content will be loaded dynamically -->
        </div>
      </div>
    </div>
  `;
  }

  private renderDepartmentCards(): string {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);

    // Check if current user's department is available
    if (!this._currentUserDepartment) {
      return `
      <div class="text-center py-5">
        <h3 class="text-muted">${t(
        "User department not found",
        "القسم الخاص بالمستخدم غير موجود"
      )}</h3>
      </div>
    `;
    }

    // Filter employees by the current user's department
    const employeesInUserDepartment = this._filteredEmployees.filter(
      (employee: IEmployee) => {
        const dept = (employee.Department || employee.SPSDepartment || "")
          .trim()
          .toLowerCase();
        return dept === this._currentUserDepartment?.trim().toLowerCase();
      }
    );

    // If no employees are found in the department
    if (employeesInUserDepartment.length === 0) {
      return `
      <div class="text-center py-5">
        <h3 class="text-muted">${t(
        "No employees found in your department",
        "لا يوجد موظفين في قسمك"
      )}</h3>
      </div>
    `;
    }

    // Temporarily set _filteredEmployees to department employees for rendering
    const originalFiltered = this._filteredEmployees;
    this._filteredEmployees = employeesInUserDepartment;

    // Render the employee grid
    const result = this.renderEmployeesGrid();

    // Restore original filtered employees
    this._filteredEmployees = originalFiltered;

    return result;
  }

  private getSortFieldName(field: string): string {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);
    switch (field) {
      case "DisplayName":
      case "PreferredName":
        return t("Name", "الاسم");
      case "Department":
      case "SPSDepartment":
        return t("Department", "القسم");
      case "Nationality":
        return t("Nationality", "الجنسية");
      case "SPSJobTitle":
        return t("Job Title", "المنصب");
      case "SPS-HireDate":
        return t("Hire Date", "تاريخ التوظيف");
      case "Nationality":
        return t("Nationality", "الجنسية");
      default:
        return t("Name", "الاسم");
    }
  }

  private updateTabContent(filterType: string): void {
    const tabContents = document.querySelectorAll(".tab-pane");
    tabContents.forEach((content) => {
      content.classList.remove("show", "active");
    });

    let targetContentId = "";
    let contentHtml = "";

    switch (filterType) {
      case "all":
        targetContentId = "all-content";
        contentHtml = this.renderEmployeesGrid();
        break;
      case "new":
        targetContentId = "new-content";
        contentHtml = this.renderEmployeesGrid();
        break;
      case "departments":
        targetContentId = "departments-content";
        contentHtml = this.renderDepartmentCards();
        break;
      default:
        targetContentId = "all-content";
        contentHtml = this.renderEmployeesGrid();
    }

    const targetContent = document.querySelector(`#${targetContentId}`);
    if (targetContent) {
      targetContent.classList.add("show", "active");
      targetContent.innerHTML = contentHtml;

      this.attachCardEventListeners();
    }
  }

  private updatePagination(): void {
    const paginationContainer = document.querySelector("#pagination-container");
    if (paginationContainer && this._activeFilter !== "departments") {
      paginationContainer.innerHTML = this.renderPagination();
      this.attachPaginationEventListeners();
    } else if (paginationContainer) {
      paginationContainer.innerHTML = "";
    }
  }

  private attachCardEventListeners(): void {
    const deptFilterButtons = document.querySelectorAll(".filter-by-dept");
    deptFilterButtons.forEach((button) => {
      button.addEventListener("click", (event) => {
        event.preventDefault();
        const dept = (event.target as HTMLElement)
          .closest(".filter-by-dept")
          ?.getAttribute("data-dept");
        if (dept) {
          this.filterByDepartment(dept);
        }
      });
    });

    const viewDetailsButtons = document.querySelectorAll(
      ".view-details-btn, [data-employee-email]"
    );
    viewDetailsButtons.forEach((button) => {
      button.addEventListener("click", (event) => {
        event.preventDefault();
        const employeeEmail = (event.target as HTMLElement)
          .closest("[data-employee-email]")
          ?.getAttribute("data-employee-email");
        if (employeeEmail) {
          this.showEmployeeDetails(employeeEmail);
        }
      });
    });
  }

  private attachPaginationEventListeners(): void {
    const pageButtons = document.querySelectorAll(".page-link");
    pageButtons.forEach((button) => {
      button.addEventListener("click", (event) => {
        event.preventDefault();
        const page = parseInt(
          (event.target as HTMLElement).getAttribute("data-page") || "1"
        );
        if (page >= 1 && page <= this._totalPages) {
          this._currentPage = page;
          this.updateTabContent(this._activeFilter);
          this.updatePagination();
        }
      });
    });
  }

  private filterByDepartment(department: string): void {
    this._activeFilter = "all";
    this._searchQuery = department;
    this._currentPage = 1;
    this.filterEmployees();

    const tabButtons = document.querySelectorAll(".nav-link");
    tabButtons.forEach((btn) => btn.classList.remove("active"));
    const allTab = document.querySelector("#all-tab");
    if (allTab) allTab.classList.add("active");

    this.updateTabContent("all");
    this.updatePagination();
  }

  private async showEmployeeDetails(employeeEmail: string): Promise<void> {

    // التحقق من حالة الموديل
    const modal = document.querySelector("#detailsModal");
    const isModalOpen = modal && modal.classList.contains("show");

    if (isModalOpen) {
      // إذا كان الموديل مفتوح، عرض loading
      this.showModalLoading();
    }

    let employee = this._employees.find(
      (emp) => emp.Email === employeeEmail || emp.WorkEmail === employeeEmail
    );

    if (!employee) {
      const profile = await this.getUserProfile(employeeEmail);
      employee = await this.createEmployeeFromProfile(profile, employeeEmail);
    }

    if (employee) {

      this.clearAllModalData();

      const communities = await this.loadVivaCommunities(employeeEmail);
      employee.Communities = communities;
      this.updateModalContent(employee);
      this.activateFirstModalTab();

      if (modal) {
        if (!isModalOpen) {
          // فتح الموديل إذا لم يكن مفتوح
          if ((window as any).bootstrap) {
            const bsModal = new (window as any).bootstrap.Modal(modal);
            bsModal.show();
          } else {
            (modal as HTMLElement).style.display = "block";
            (modal as HTMLElement).classList.add("show");
            document.body.classList.add("modal-open");
          }
        }
        // إخفاء loading بعد تحميل البيانات
        this.hideModalLoading();
      }
    } else {
      console.warn(`❌ Employee with email ${employeeEmail} not found`);
      if (isModalOpen) {
        this.hideModalLoading();
      }
    }
  }

  private updateModalContent(employee: IEmployee): void {
    // ملاحظة: مسح البيانات السابقة يتم في دالة showEmployeeDetails قبل استدعاء هذه الدالة
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);
    const getSafeString = (value: string | undefined, defaultValue: string) =>
      value || defaultValue;
    const modal = document.querySelector("#detailsModal");
    if (!modal) return;

    const displayName =
      employee.DisplayName ||
      employee.PreferredName ||
      (employee.FirstName && employee.LastName
        ? `${employee.FirstName} ${employee.LastName}`
        : employee.FirstName || employee.LastName || "");

    const jobTitle = employee.SPSJobTitle || t("No Job Title", "لا يوجد منصب");
    const department = employee.Department || employee.SPSDepartment
    
    const email = employee.Email || employee.WorkEmail || "";
    const phone =
      employee.WorkPhone || employee.HomePhone || employee.CellPhone || "";
    const skills =
      employee.SPSSkills || t("No skills available", "لا توجد مهارات متاحة");
    const aboutMe =
      employee.AboutMe ||
      t("No about me information available", "لا توجد معلومات نبذة عني");
    const interests = getSafeString(
      employee.SPSInterests,
      t("No interests available", "لا توجد اهتمامات متاحة")
    );
    const school = getSafeString(
      employee.SPSSchool,
      t("No Certificates information available", "لا توجد معلومات عن المدرسة")
    );
    const photoUrl =
      employee.PictureUrl ||
      `/_layouts/15/userphoto.aspx?size=L&accountname=${encodeURIComponent(
        employee.AccountName || ""
      )}`;

    const profileImg = modal.querySelector(
      ".modal-body img"
    ) as HTMLImageElement;
    if (profileImg) {
      profileImg.src = photoUrl;
      profileImg.alt = displayName;
      profileImg.onerror = () => {
        profileImg.src = "/_layouts/15/images/person.gif";
      };
    }

    const nameElement = modal.querySelector(".modal-body h3");
    if (nameElement) {
      nameElement.textContent = displayName;
    }

    const jobTitleElement = modal.querySelector(".modal-body p.text-color");
    if (jobTitleElement) {
      jobTitleElement.textContent = jobTitle;
    }
    const departmentElement = modal.querySelector("#modal-employee-Department");
    if (departmentElement) {
      departmentElement.textContent = department;
    } else {
      // إذا مافيش عنصر للقسم، أضفه ديناميكيًا بعد jobTitle
      const jobTitleContainer = modal.querySelector(".modal-body .d-flex.flex-column");
      if (jobTitleContainer) {
        const deptElement = document.createElement("p");
        deptElement.id = "modal-employee-Department";
        deptElement.className = "text-color mb-0";
        deptElement.textContent = department;
        jobTitleContainer.appendChild(deptElement);
      }
    }

    const emailLinks = modal.querySelectorAll(
      'a[href^="mailto:"], #modal-email-link, #modal-contact-email'
    );
    emailLinks.forEach((link) => {
      if (email) {
        (link as HTMLAnchorElement).href = `mailto:${email}`;
        const span = link.querySelector("span");
        if (span) span.textContent = email;
      }
    });

    const phoneLinks = modal.querySelectorAll(
      'a[href^="tel:"], #modal-phone-link, #modal-contact-phone'
    );
    phoneLinks.forEach((link) => {
      if (phone) {
        (link as HTMLAnchorElement).href = `tel:${phone}`;
        const span = link.querySelector("span");
        if (span) span.textContent = phone;
      } else {
        (link as HTMLAnchorElement).href = "";
        const span = link.querySelector("span");
        if (span) span.textContent = "";
      }
    });

    const chatLinks = modal.querySelectorAll(
      "#modal-chat-link, #modal-contact-chat"
    );
    chatLinks.forEach((link) => {
      if (email) {
        (
          link as HTMLAnchorElement
        ).href = `https://teams.microsoft.com/l/chat/0/0?users=${encodeURIComponent(
          email
        )}`;
      }
    });

    const contactsTab = modal.querySelector("#contacts");
    if (contactsTab) {
      const emailLinkInTab = contactsTab.querySelector(
        'a[href^="mailto:"]'
      ) as HTMLAnchorElement;
      if (emailLinkInTab && email) {
        emailLinkInTab.href = `mailto:${email}`;
        const emailSpan = emailLinkInTab.querySelector("span");
        if (emailSpan) emailSpan.textContent = email;
      }

      const phoneLinkInTab = contactsTab.querySelector(
        'a[href^="tel:"]'
      ) as HTMLAnchorElement;
      if (phoneLinkInTab && phone) {
        phoneLinkInTab.href = `tel:${phone}`;
        const phoneSpan = phoneLinkInTab.querySelector("span");
        if (phoneSpan) phoneSpan.textContent = phone;
      }

      const aboutMeElement = contactsTab.querySelector("#modal-about-me");
      if (aboutMeElement) {
        aboutMeElement.textContent = aboutMe;
      }

      const skillsElement = contactsTab.querySelector("#modal-skills");
      if (skillsElement) {
        skillsElement.innerHTML = skills;
      }
      const interestsElement = contactsTab.querySelector("#modal-interests");
      if (interestsElement) interestsElement.innerHTML = interests;

      const schoolElement = contactsTab.querySelector("#modal-school");
      if (schoolElement) schoolElement.innerHTML = school;
      // تحديث المجتمعات في الـ contacts tab
      const communitiesElement =
        contactsTab.querySelector("#modal-communities");
      if (communitiesElement) {
        const communitiesHtml =
          employee.Communities && employee.Communities.length > 0
            ? employee.Communities.map((community) => {
              const communityImage =
                community.photoUrl ||
                "https://img.freepik.com/premium-vector/login-business-illustration_736766-14.jpg";
              const communityIcon =
                "https://i.pinimg.com/736x/d0/ba/6c/d0ba6cf4ba1cfdd464ea96dd95e63e37.jpg";

              return `
                        <div class="col-md-3">
                            <a href="https://engage.cloud.microsoft/main/groups/eyJfdHlwZSI6Ikdyb3VwIiwiaWQiOiI${btoa(community.id)}"}/all" target="_blank" class="text-decoration-none">
                                <div class="card position-relative overflow-hidden card-color communities-card" style="cursor: pointer; transition: transform 0.2s;">
                                    <div class="card-img-top">
                                        <img src="${communityImage}" class="card-img-top" alt="${community.name} Header">
                                    </div>
                                    <div class="card-body text-center pt-2">
                                        <div class="d-flex justify-content-between position-relative">
                                            <div class="profile-img-container">
                                                <img src="${communityIcon}" alt="${community.name} Icon" class="img-fluid">
                                            </div>
                                            <button class="btn btn-link position-absolute top-0 end-0 m-2 p-0 join-group-btn" data-group-id="${community.id}" onclick="event.preventDefault(); event.stopPropagation();">
                                                <i class="bi bi-plus-circle fs-3 head-color"></i>
                                            </button>
                                        </div>
                                        <h6 class="head-color fw-bold text-start mt-3">${community.name}</h6>
                                    </div>
                                </div>
                            </a>
                        </div>
                    `;
            }).join("")
            : `<p class="text-muted">${t(
              "No communities available",
              "لا توجد مجتمعات متاحة"
            )}</p>`;
        communitiesElement.innerHTML = communitiesHtml;

        const joinButtons =
          communitiesElement.querySelectorAll(".join-group-btn");
        joinButtons.forEach((button) => {
          button.addEventListener("click", async (event) => {
            const groupId = (event.currentTarget as HTMLElement).getAttribute(
              "data-group-id"
            );
            if (groupId) {
              const currentUserEmail = (this as any).context.pageContext.user.email;
              const success = await this.joinGroup(groupId, currentUserEmail);
              if (success) {
                const isPrivate =
                  employee.Communities?.find((c) => c.id === groupId)
                    ?.visibility === "Private";
                Swal.fire({
                  title: t("Success", "نجاح"),
                  text: isPrivate
                    ? t(
                      "Membership request submitted successfully!",
                      "تم تقديم طلب الانضمام بنجاح!"
                    )
                    : t(
                      "Successfully joined the group!",
                      "تم الانضمام إلى المجتمع بنجاح!"
                    ),
                  icon: "success",
                  confirmButtonText: t("OK", "موافق"),
                  confirmButtonColor: "#3085d6",
                }).then(() => {
                  // إعادة تحميل المجتمعات إذا لزم الأمر
                  this.loadVivaCommunities(employee.Email).then(
                    (updatedCommunities) => {
                      employee.Communities = updatedCommunities;
                      this.updateModalContent(employee);
                    }
                  );
                });
              } else {
                Swal.fire({
                  title: t("Error", "خطأ"),
                  text: t(
                    "Failed to join or request membership.",
                    "فشل الانضمام أو تقديم طلب العضوية."
                  ),
                  icon: "error",
                  confirmButtonText: t("OK", "موافق"),
                  confirmButtonColor: "#d33",
                });
              }
            }
          });
        });
      }

      this.updateEmployeeTestimonials(email);
    }

    const orgTab = modal.querySelector("#organization");
    if (orgTab) {
      const { managerName, managerPhotoUrl, managerEmail } = this.getManagerDetails(employee);

      const managerPhotoElement = orgTab.querySelector(
        "#modal-manager-photo"
      ) as HTMLImageElement;
      if (managerPhotoElement) {
        managerPhotoElement.src = managerPhotoUrl;
        managerPhotoElement.alt = managerName;
        managerPhotoElement.setAttribute('data-manager-email', managerEmail);
      }

      const managerNameElement = orgTab.querySelector("#modal-manager-name");
      if (managerNameElement) {
        managerNameElement.textContent = `${managerName}`;
      }

      const colleagues = this.getColleagues(employee);
      const teamMembersContainer = orgTab.querySelector("#modal-team-members");
      if (teamMembersContainer) {
        if (colleagues.length > 0) {
          teamMembersContainer.innerHTML = colleagues
            .map(
              (colleague) => `
                            <div class="col-md-4 d-flex align-items-center mb-3">
                                <img src="${colleague.photoUrl}"
                                    alt="${colleague.name}"
                                    class="rounded-circle me-2"
                                    style="width:40px; height:40px; object-fit: cover;"
                                    onerror="this.src='/_layouts/15/images/person.gif';"
                                    data-colleague-email="${colleague.email}">
                                <span class="head-color">${colleague.name}</span>
                            </div>
                        `
            )
            .join("");
        } else {
          teamMembersContainer.innerHTML = `<p class="text-muted">${t(
            "No colleagues in this department",
            "لا يوجد زملاء في هذا القسم"
          )}</p>`;
        }
      }

      const teamSection = orgTab.querySelector("h5.head-color:nth-of-type(2)");
      if (
        teamSection &&
        employee.DirectReports &&
        employee.DirectReports.length > 0
      ) {
        const teamContainer =
          teamSection.nextElementSibling?.nextElementSibling;
        if (teamContainer) {
          teamContainer.innerHTML = employee.DirectReports.slice(0, 6)
            .map((report) => {
              const name = report.split("|").pop()?.split("@")[0] || report;
              const initials = name
                .split(" ")
                .map((n) => n[0])
                .join("")
                .toUpperCase()
                .substring(0, 2);
              return `
                            <div class="col-md-4 d-flex align-items-center">
                                <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-2"
                                     style="width:50px; height:50px;">
                                    ${initials}
                                </div>
                                <span>${name}</span>
                            </div>
                        `;
            })
            .join("");
        }
      }

      const testimonials: any = this.getTestimonialsByEmail(email);
      let testimonialsHtml = "";
      if (testimonials.length > 0) {
        testimonialsHtml = testimonials
          .map((testimonial: any) =>
            this.renderSimpleTestimonialsHTML(testimonial)
          )
          .join("");
      } else {
        testimonialsHtml = `<p class="text-muted">${t(
          "No testimonials available",
          "لا توجد شهادات متاحة"
        )}</p>`;
      }

      const testimonialContainer = modal.querySelector("#modal-testimonial");
      if (testimonialContainer) {
        testimonialContainer.innerHTML = testimonialsHtml;
      }
    }

    const linkedinTab = modal.querySelector("#linkedin");
    if (linkedinTab) {
      const linkedinContent = linkedinTab.querySelector("p");
      if (linkedinContent) {
        linkedinContent.innerHTML = ``;
      }
    }

    // إضافة event listeners باستخدام event delegation
    this.setupModalEventDelegation();
  }

  // دالة إعداد event delegation للموديل (تعمل مرة واحدة فقط)
  private setupModalEventDelegation(): void {
    const modal = document.querySelector("#detailsModal");
    if (!modal) return;

    // التحقق من وجود event listener سابق
    if ((modal as any)._hasEventDelegation) {
      return;
    }


    // إضافة event listener واحد للموديل بالكامل
    modal.addEventListener("click", (event) => {
      const target = event.target as HTMLElement;


      // التحقق من النقر على صورة المدير
      const managerPhoto =
        target.closest("#modal-manager-photo") ||
        (target.id === "modal-manager-photo" ? target : null);

      if (managerPhoto) {
        event.preventDefault();
        event.stopPropagation();

        const managerEmail = managerPhoto.getAttribute("data-manager-email");

        if (managerEmail && managerEmail.trim() !== "") {
          this.showEmployeeDetails(managerEmail);
        } else {
          console.warn("⚠️ لا يوجد إيميل للمدير - لا يمكن فتح البروفايل");
          // يمكن إضافة رسالة للمستخدم هنا إذا أردت
        }
        return;
      }

      // التحقق من النقر على صورة زميل
      const colleaguePhoto =
        target.closest("[data-colleague-email]") ||
        (target.hasAttribute("data-colleague-email") ? target : null);

      if (colleaguePhoto) {
        event.preventDefault();
        event.stopPropagation();

        const colleagueEmail = colleaguePhoto.getAttribute(
          "data-colleague-email"
        );

        if (colleagueEmail && colleagueEmail.trim() !== "") {
          this.showEmployeeDetails(colleagueEmail);
        } else {
          console.warn("⚠️ لا يوجد إيميل للزميل");
        }
        return;
      }
    });

    // تسجيل أن event delegation تم إعداده
    (modal as any)._hasEventDelegation = true;
  }

  // دالة منفصلة لإضافة event listeners للموديل مع إعادة محاولة (احتياطية)
  addModalEventListeners(modal: Element): void {
    let attempts = 0;
    const maxAttempts = 5;

    const tryAddListeners = () => {
      attempts++;

      // إضافة event listener للمدير
      const managerPhoto = modal.querySelector("#modal-manager-photo");
      if (managerPhoto) {
        const managerEmail = (managerPhoto as HTMLElement).getAttribute(
          "data-manager-email"
        );

        if (managerEmail && managerEmail.trim() !== "") {
          // إزالة event listeners السابقة
          const newManagerPhoto = managerPhoto.cloneNode(true) as HTMLElement;
          managerPhoto.parentNode?.replaceChild(newManagerPhoto, managerPhoto);

          newManagerPhoto.addEventListener("click", (event) => {
            event.preventDefault();
            event.stopPropagation();
            const clickedManagerEmail = (
              event.target as HTMLElement
            ).getAttribute("data-manager-email");
            if (clickedManagerEmail && clickedManagerEmail.trim() !== "") {
              this.showEmployeeDetails(clickedManagerEmail);
            }
          });
        }
      }

      // إضافة event listeners للزملاء
      const colleaguePhotos = modal.querySelectorAll("[data-colleague-email]");

      colleaguePhotos.forEach((photo, index) => {
        const email = (photo as HTMLElement).getAttribute(
          "data-colleague-email"
        );

        if (email && email.trim() !== "") {
          // إزالة event listeners السابقة
          const newColleaguePhoto = photo.cloneNode(true) as HTMLElement;
          photo.parentNode?.replaceChild(newColleaguePhoto, photo);

          newColleaguePhoto.addEventListener("click", (event) => {
            event.preventDefault();
            event.stopPropagation();
            const colleagueEmail = (event.target as HTMLElement).getAttribute(
              "data-colleague-email"
            );
            if (colleagueEmail && colleagueEmail.trim() !== "") {
              this.showEmployeeDetails(colleagueEmail);
            }
          });
        }
      });


      // إعادة المحاولة إذا لم تنجح
      const hasManager = modal.querySelector(
        "#modal-manager-photo[data-manager-email]"
      );
      const hasColleagues =
        modal.querySelectorAll("[data-colleague-email]").length > 0;

      if (!hasManager && !hasColleagues && attempts < maxAttempts) {

        setTimeout(tryAddListeners, 200 * attempts);
      } else {
      }
    };

    // البدء بتأخير أولي
    setTimeout(tryAddListeners, 100);
  }

  // دالة عرض loading في الموديل
  private showModalLoading(): void {
    const modal = document.querySelector("#detailsModal");
    if (!modal) return;

    // إضافة overlay loading
    let loadingOverlay = modal.querySelector(".modal-loading-overlay");
    if (!loadingOverlay) {
      loadingOverlay = document.createElement("div");
      loadingOverlay.className = "modal-loading-overlay";
      loadingOverlay.innerHTML = `
        <div class="d-flex justify-content-center align-items-center h-100">
          <div class="text-center">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-2">جاري التحميل...</div>
          </div>
        </div>
      `;
      (loadingOverlay as HTMLElement).style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.9);
        z-index: 1050;
        display: flex;
      `;
      modal.querySelector(".modal-content")?.appendChild(loadingOverlay);
    }
    (loadingOverlay as HTMLElement).style.display = "flex";
  }

  // دالة إخفاء loading من الموديل
  private hideModalLoading(): void {
    const modal = document.querySelector("#detailsModal");
    if (!modal) return;

    const loadingOverlay = modal.querySelector(".modal-loading-overlay");
    if (loadingOverlay) {
      (loadingOverlay as HTMLElement).style.display = "none";
    }
  }

  private async updateEmployeeTestimonials(email: string): Promise<void> {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);
    const modal = document.querySelector("#detailsModal");
    if (!modal) return;

    const testimonialContainer = modal.querySelector("#modal-testimonial");
    if (!testimonialContainer) return;

    try {
      const testimonials = await this.getTestimonialsByEmail(email);

      if (testimonials && testimonials.length > 0) {
        const testimonialsHtml = testimonials
          .map((testimonial: any) =>
            this.renderSimpleTestimonialsHTML(testimonial)
          )
          .join("");
        testimonialContainer.innerHTML = testimonialsHtml;
      } else {
        testimonialContainer.innerHTML = `<p class="text-muted">${t(
          "No testimonials available for this employee",
          "لا توجد شهادات متاحة لهذا الموظف"
        )}</p>`;
      }
    } catch (error) {
      console.error("❌ Error loading testimonials for", email, error);
      testimonialContainer.innerHTML = `<p class="text-muted">${t(
        "Error loading testimonials",
        "خطأ في تحميل الشهادات"
      )}</p>`;
    }
  }

  private isNewEmployee(employee: IEmployee): boolean {
    if (!employee.SPSHireDate) {
      return false;
    }

    try {
      const joinDate = new Date(employee.SPSHireDate);
      const now = new Date();

      if (isNaN(joinDate.getTime())) {
        return false;
      }

      const diffTime = Math.abs(now.getTime() - joinDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays <= 30;
    } catch (error) {
      return false;
    }
  }

  private extractDepartments(): string[] {
    const departments = new Set<string>();
    this._employees.forEach((employee) => {
      const dept = employee.Department || employee.SPSDepartment;
      if (dept && dept.trim() !== "" && dept.toLowerCase() !== "null") {
        departments.add(dept.trim());
      }
    });
    const sortedDepts = Array.from(departments).sort();

    return sortedDepts;
  }

  private renderEmployeesGrid(): string {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);

    if (!this._filteredEmployees || this._filteredEmployees.length === 0) {
      return `
      <div class="text-center py-5">
        <h3 class="text-muted">${t("No Results Found", "لا توجد نتائج")}</h3>
        <p class="text-muted">${t(
        "Try adjusting your filters",
        "جرب تعديل الفلاتر"
      )}</p>
      </div>
    `;
    }

    const itemsPerPage = this.getItemsPerPage();
    const startIndex = (this._currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedEmployees = this._filteredEmployees.slice(
      startIndex,
      endIndex
    );

    if (paginatedEmployees.length === 0) {
      return `
      <div class="text-center py-5">
        <h3 class="text-muted">${t("No Results Found", "لا توجد نتائج")}</h3>
        <p class="text-muted">${t(
        "Current page is out of range",
        "الصفحة الحالية خارج النطاق"
      )}</p>
      </div>
    `;
    }

    return `
    <div class="d-grid people-list">
      ${paginatedEmployees
        .map((employee) => this.renderEmployeeCard(employee))
        .join("")}
    </div>
  `;
  }

  private renderEmployeeCard(employee: IEmployee): string {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);

    const displayName =
      employee.DisplayName ||
      employee.PreferredName ||
      (employee.FirstName && employee.LastName
        ? `${employee.FirstName} ${employee.LastName}`
        : employee.FirstName || employee.LastName || "");

    const jobTitle = employee.SPSJobTitle || "";
    
    
    const photoUrl =
      employee.PictureUrl ||
      `/_layouts/15/userphoto.aspx?size=M&accountname=${encodeURIComponent(
        employee.AccountName || ""
      )}` ||
      "/_layouts/15/images/person.gif";

    // استخدام دالة getManagerDetails للحصول على بيانات المدير بما في ذلك الإيميل
    const { managerName, managerPhotoUrl, managerEmail } =
      this.getManagerDetails(employee);

    return `
      <div class="grid-item">
        <div class="card card-color p-4 people-card">
          <div class="card-front">
            <div class="d-flex justify-content-center align-items-center mb-3">
              <img src="${photoUrl}"
                   alt="${displayName}"
                   class="img-fluid rounded-circle"
                   style="width: 150px; height: 150px; object-fit: cover;"
                   loading="lazy"
                   onerror="this.src='/_layouts/15/images/person.gif';">
            </div>
            <div class="d-flex flex-column align-items-center">
              <h3 class="text-center head-color font-weight-bold">${displayName}</h3>
              <p class="text-color text-center">${jobTitle}</p>

            </div>
          </div>

          <div class="card-back position-absolute top-0 start-0 w-100 h-100 bg-white p-2 py-4">
            <div class="d-flex flex-column justify-content-between h-100">
              <div class="d-flex justify-content-start align-items-center">
                <div class="d-flex justify-content-center align-items-center mb-3">
                  <img src="${photoUrl}"
                       alt="${displayName}"
                       class="img-fluid rounded-circle profile-img"
                       style="width: 60px; height: 60px; object-fit: cover;"
                       loading="lazy"
                       onerror="this.src='/_layouts/15/images/person.gif';">
                </div>
                <div class="d-flex flex-column mx-3">
                  <h3 class="head-color font-weight-bold">${displayName}</h3>
                  <p class="text-color">${jobTitle}</p>
                </div>
              </div>

              <div class="contact-options flex-wrap">
                <a href="mailto:${employee.Email || employee.WorkEmail || ""}"
                   class="p-2 mt-2 mx-2 d-flex align-items-center justify-content-center"
                   title="${t("Send Email", "إرسال إيميل")}">
                  <svg xmlns="http://www.w3.org/2000/svg" id="Message" width="26.291" height="25.249" viewBox="0 0 26.291 25.249">
                    <path id="Stroke_1" data-name="Stroke 1" d="M7.366,6.1c-1.484,0-3.279-.909-5.335-2.7A26.173,26.173,0,0,1-.524.828.96.96,0,1,1,.944-.409C2.44,1.365,5.508,4.18,7.366,4.18S12.261,1.368,13.74-.4A.96.96,0,0,1,15.215.824,25.818,25.818,0,0,1,12.682,3.4C10.64,5.189,8.852,6.1,7.366,6.1Z" transform="translate(5.794 8.641)" fill="#ed3c8a"></path>
                    <path id="Stroke_3" data-name="Stroke 3" d="M12.4-.75c5.007,0,8.148.869,10.184,2.818s2.962,4.985,2.962,9.807-.913,7.845-2.962,9.806S17.4,24.5,12.4,24.5,4.248,23.63,2.212,21.681-.75,16.7-.75,11.875.163,4.029,2.212,2.068,7.388-.75,12.4-.75Zm0,23.33c8.6,0,11.226-2.5,11.226-10.705S21,1.169,12.4,1.169,1.169,3.671,1.169,11.875,3.792,22.58,12.4,22.58Z" transform="translate(0.75 0.75)" fill="#ed3c8a"></path>
                  </svg>
                  <span class="mx-2">${t("Send Email", "إرسال إيميل")}</span>
                </a>

                <a href="https://teams.microsoft.com/l/chat/0/0?users=${encodeURIComponent(
      employee.Email || employee.WorkEmail || ""
    )}"
                   class="p-2 mt-2 mx-2 d-flex align-items-center justify-content-center"
                   target="_blank"
                   title="${t(
      "Start chat in Microsoft Teams",
      "بدء محادثة في Microsoft Teams"
    )}">
                  <svg xmlns="http://www.w3.org/2000/svg" width="26.722" height="26.721" viewBox="0 0 26.722 26.721">
                    <g id="chat-ro" transform="translate(1 1)">
                      <path id="Path_77853" data-name="Path 77853" d="M18.2,12.937h-.011a.969.969,0,1,1,0-1.937H18.2a.969.969,0,0,1,0,1.937Z" transform="translate(-0.887 0.392)" fill="#ee3c8a"></path>
                      <path id="Path_77853-2" data-name="Path 77853" d="M18.2,12.937h-.011a.969.969,0,1,1,0-1.937H18.2a.969.969,0,0,1,0,1.937Z" transform="translate(-5.729 0.392)" fill="#ee3c8a"></path>
                      <path id="Path_77853-3" data-name="Path 77853" d="M18.2,12.937h-.011a.969.969,0,1,1,0-1.937H18.2a.969.969,0,0,1,0,1.937Z" transform="translate(-10.572 0.392)" fill="#ee3c8a"></path>
                      <path id="Path_77854" data-name="Path 77854" d="M14.361,27.721a13.226,13.226,0,0,1-5.949-1.395,1.068,1.068,0,0,0-.741-.1l-2.759.738A2.568,2.568,0,0,1,2.2,26.043a2.557,2.557,0,0,1-.443-2.234l.738-2.759a1.059,1.059,0,0,0-.1-.741A13.363,13.363,0,0,1,14.361,1a13.361,13.361,0,0,1,9.447,22.808A13.273,13.273,0,0,1,14.361,27.721ZM7.9,24.263a3.1,3.1,0,0,1,1.373.33,11.426,11.426,0,0,0,16.51-10.232,11.424,11.424,0,0,0-19.5-8.078A11.433,11.433,0,0,0,4.128,19.446a2.991,2.991,0,0,1,.234,2.1L3.625,24.31a.626.626,0,0,0,.108.554.644.644,0,0,0,.505.256.672.672,0,0,0,.173-.024l2.758-.738A2.831,2.831,0,0,1,7.9,24.263Z" transform="translate(-2 -2)" fill="#ee3c8a"></path>
                    </g>
                  </svg>
                  <span class="mx-2">${t("Start Chat", "بدء محادثة")}</span>
                </a>

                <a href="tel:${employee.WorkPhone ||
      employee.HomePhone ||
      employee.CellPhone ||
      ""
      }"
                   class="p-2 mt-2 mx-2 d-flex align-items-center justify-content-center"
                   title="${t("Call", "اتصال")}">
                  <svg xmlns="http://www.w3.org/2000/svg" width="26.053" height="26.056" viewBox="0 0 21.053 21.056">
                    <path id="Stroke_1" data-name="Stroke 1" d="M15.425,20.306c-2.663,0-6.059-2.046-10.094-6.081-3.155-3.155-5.1-5.941-5.8-8.28A4.559,4.559,0,0,1-.082,1.856h0C.193,1.441,1.749-.75,3.875-.75A3.356,3.356,0,0,1,6.029.076C7.636,1.4,8.51,2.488,8.779,3.5a3.279,3.279,0,0,1-.409,2.433c-.476.977-.851,1.748,1.326,3.926,1.144,1.144,2.046,1.7,2.757,1.7a2.7,2.7,0,0,0,1.17-.374,3.827,3.827,0,0,1,1.726-.5c1.238,0,2.512.876,4.131,2.842a3.286,3.286,0,0,1,.772,2.742,5.96,5.96,0,0,1-2.55,3.37h0A3.841,3.841,0,0,1,15.425,20.306ZM1.21,2.693q-.018.03-.038.059C.137,4.2.973,7.688,6.42,13.135c4.653,4.653,7.465,5.63,9,5.63a2.32,2.32,0,0,0,1.38-.381q.029-.021.06-.039a4.449,4.449,0,0,0,1.877-2.394,1.754,1.754,0,0,0-.454-1.448c-1.263-1.533-2.225-2.279-2.94-2.279a2.533,2.533,0,0,0-1.051.348,4.041,4.041,0,0,1-1.844.53c-1.156,0-2.378-.684-3.846-2.151A6.5,6.5,0,0,1,6.49,7.567A3.7,3.7,0,0,1,6.984,5.26A1.821,1.821,0,0,0,7.29,3.9A6.454,6.454,0,0,0,5.051,1.266A1.835,1.835,0,0,0,3.875.791C2.636.791,1.461,2.287,1.21,2.693Z" transform="translate(0.748 0.75)" fill="#ed3c8a"></path>
                  </svg>
                  <span class="mx-2">${t("Call", "اتصال")}</span>
                </a>
              </div>

              <div class="report-to mt-2">
                <h5 class="head-color mb-2">${t(
        "Report to",
        "يرفع تقرير إلى"
      )}</h5>
                <div class="d-flex align-items-center">
                  <img src="${managerPhotoUrl}"
                      alt="${managerName}"
                      class="rounded-circle me-2"
                      style="width: 40px; height: 40px; object-fit: cover; cursor: pointer;"
                      onerror="this.src='/_layouts/15/images/person.gif';"
                      data-bs-toggle="modal"
                      data-bs-target="#detailsModal"
                      data-employee-email="${managerEmail || ""}"
                      onclick="event.stopPropagation();"
                      title="${t(
        "View manager profile",
        "عرض بروفايل المدير"
      )}">
                  <span class="head-color">${managerName}</span>
                </div>
              </div>

              <a class="btn btn-link text-primary text-decoration-none d-flex align-items-center justify-content-end mt-2 view-details-btn"
                 data-employee-email="${employee.Email}"
                 data-bs-toggle="modal"
                 data-bs-target="#detailsModal">
                ${t(
        "View Details",
        "عرض التفاصيل"
      )}<i class="icon-arrow-right2 ms-2"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderPagination(): string {
    if (this._totalPages <= 1) return "";

    const t = (en: string, ar: string) => (this._isArabic ? ar : en);
    const prevText = t("Previous", "السابق");
    const nextText = t("Next", "التالي");

    let paginationHtml = `
    <nav aria-label="Page navigation">
      <ul class="pagination justify-content-center">
        <li class="page-item ${this._currentPage === 1 ? "disabled" : ""}">
          <button class="page-link" data-page="${this._currentPage - 1
      }">${prevText}</button>
        </li>
  `;

    for (let i = 1; i <= this._totalPages; i++) {
      paginationHtml += `
      <li class="page-item ${i === this._currentPage ? "active" : ""}">
        <button class="page-link" data-page="${i}">${i}</button>
      </li>
    `;
    }

    paginationHtml += `
        <li class="page-item ${this._currentPage === this._totalPages ? "disabled" : ""
      }">
          <button class="page-link" data-page="${this._currentPage + 1
      }">${nextText}</button>
        </li>
      </ul>
    </nav>
  `;

    return paginationHtml;
  }

  private attachEventListeners(): void {
    const updateGridAndPagination = () => {
      this.updateTabContent(this._activeFilter);
      this.updatePagination();
    };

    const handlePageClick = (event: Event) => {
      event.preventDefault();
      const page = parseInt(
        (event.target as HTMLElement).getAttribute("data-page") || "1"
      );
      if (page >= 1 && page <= this._totalPages) {
        this._currentPage = page;
        updateGridAndPagination();
      }
    };

    const tabButtons = document.querySelectorAll("#employeeTabs button");
    tabButtons.forEach((button) => {
      button.addEventListener("click", (event) => {
        event.preventDefault();
        const target = event.target as HTMLElement;
        const buttonId = target.id;

        let filterType = "all";
        if (buttonId === "all-tab") filterType = "all";
        else if (buttonId === "new-tab") filterType = "new";
        else if (buttonId === "departments-tab") filterType = "departments";

        this._activeFilter = filterType;
        this._currentPage = 1;
        this._searchQuery = "";

        this.filterEmployees();
        updateGridAndPagination();

        tabButtons.forEach((btn) => btn.classList.remove("active"));
        target.classList.add("active");
      });
    });

    const deptFilterButtons = document.querySelectorAll(".filter-by-dept");
    deptFilterButtons.forEach((button) => {
      button.addEventListener("click", (event) => {
        event.preventDefault();
        const dept = (event.target as HTMLElement)
          .closest(".filter-by-dept")
          ?.getAttribute("data-dept");
        if (dept) {
          this._activeFilter = "all";
          this._searchQuery = dept;
          this._currentPage = 1;
          this.filterEmployees();
          const gridContainer = document.querySelector("#all-content");
          if (gridContainer) {
            gridContainer.classList.add("show", "active");
            gridContainer.innerHTML = this.renderEmployeesGrid();
            const allTab = document.querySelector("#all-tab");
            if (allTab) {
              tabButtons.forEach((btn) => btn.classList.remove("active"));
              allTab.classList.add("active");
            }
          }
          updateGridAndPagination();
        }
      });
    });

    const sortItems = document.querySelectorAll(".dropdown-item[data-sort]");
    sortItems.forEach((item) => {
      item.addEventListener("click", (event) => {
        event.preventDefault();
        const sortField = (event.target as HTMLElement).getAttribute(
          "data-sort"
        );
        if (sortField) {
          this._sortField = sortField;
          this._sortDirection = sortField.includes("SPS-HireDate")
            ? "desc"
            : "asc";
          this.filterEmployees();
          const dropdownButton = document.querySelector("#sortDropdown");
          if (dropdownButton) {
            dropdownButton.textContent = `${this._isArabic ? "الترتيب حسب: " : "Sort by: "
              }${this.getSortFieldName(sortField)}`;
          }
          updateGridAndPagination();
        }
      });
    });

    const nameSearch = document.querySelector(
      "#nameSearch"
    ) as HTMLInputElement;
    if (nameSearch) {
      nameSearch.addEventListener("input", () => {
        // Add input handling if needed
      });
    }

    const applyFilterBtn = document.querySelector(".btn-apply-filter");
    if (applyFilterBtn) {
      applyFilterBtn.addEventListener("click", () => {
        const nameSearchValue =
          (document.querySelector("#nameSearch") as HTMLInputElement)?.value ||
          "";
        const jobTitleFilterValue =
          (document.querySelector("#jobTitleFilter") as HTMLSelectElement)
            ?.value || "all";
        const departmentFilterValue =
          (document.querySelector("#departmentFilter") as HTMLSelectElement)
            ?.value || "all";
        const nationalityFilterValue =
          (document.querySelector("#nationalityFilter") as HTMLSelectElement)
            ?.value || "all";

        this._activeFilter =
          departmentFilterValue === "all" && jobTitleFilterValue === "all"
            ? "all"
            : "dept";
        this._searchQuery =
          departmentFilterValue !== "all"
            ? departmentFilterValue
            : jobTitleFilterValue !== "all"
              ? jobTitleFilterValue
              : nameSearchValue;
        this._currentPage = 1;
        this.filterEmployees(
          nameSearchValue,
          jobTitleFilterValue !== "all" ? jobTitleFilterValue : "",
          departmentFilterValue,
          nationalityFilterValue
        );
        updateGridAndPagination();

        const tabs = document.querySelectorAll(".nav-link");
        tabs.forEach((tab) => tab.classList.remove("active"));
        const allTab = document.querySelector("#all-tab");
        if (allTab) allTab.classList.add("active");
      });
    }

    const resetFilterBtn = document.querySelector(".btn-reset-filter");
    if (resetFilterBtn) {
      resetFilterBtn.addEventListener("click", () => {
        this._activeFilter = "all";
        this._searchQuery = "";
        this._sortField = this.properties.defaultSortField || "Title";
        this._sortDirection = this.properties.defaultSortDirection || "asc";
        this._currentPage = 1;

        const inputs = document.querySelectorAll(
          'input[type="text"], select'
        ) as NodeListOf<HTMLInputElement | HTMLSelectElement>;
        inputs.forEach((input) => {
          input.value = input.tagName.toLowerCase() === "select" ? "all" : "";
        });

        const tabs = document.querySelectorAll(".nav-link");
        tabs.forEach((tab) => tab.classList.remove("active"));
        const allTab = document.querySelector("#all-tab");
        if (allTab) allTab.classList.add("active");

        this.filterEmployees();
        updateGridAndPagination();
      });
    }

    const pageButtons = document.querySelectorAll(".page-link");
    pageButtons.forEach((button) => {
      button.addEventListener("click", handlePageClick);
    });

    const modalCloseBtn = document.querySelector("#modal-close-btn");
    if (modalCloseBtn) {
      modalCloseBtn.addEventListener("click", () => {
        const modal = document.querySelector("#detailsModal");
        if (modal) {
          (modal as HTMLElement).style.display = "none";
          (modal as HTMLElement).classList.remove("show");
          document.body.classList.remove("modal-open");
          const backdrop = document.querySelector(".modal-backdrop");
          if (backdrop) {
            backdrop.remove();
          }
        }
      });
    }

    const viewDetailsButtons = document.querySelectorAll(
      ".view-details-btn, [data-bs-toggle='modal'][data-employee-email]"
    );
    viewDetailsButtons.forEach((button) => {
      button.addEventListener("click", (event) => {
        event.preventDefault();
        const employeeEmail = (event.target as HTMLElement)
          .closest("[data-employee-email]")
          ?.getAttribute("data-employee-email");

        if (employeeEmail) {
          this.showEmployeeDetails(employeeEmail);
        }
      });
    });
  }

  protected async onInit(): Promise<void> {
    console.dir("Test2");
    await super.onInit();
    try {
      this._currentUserDepartment = await this.getCurrentUserDepartment();
      const response = await (this as any).context.spHttpClient.get(
        `${(this as any).context.pageContext.web.absoluteUrl
        }/_api/web/lists/getbytitle('Testimonials')/items?$select=*,Author/Created,Author/Title,Author/JobTitle,Author/Department,Author/EMail,Question1_EN/Title,Question2_EN/Title,Question3_EN/Title,Question4_EN/Title&$expand=Question1_EN,Question2_EN,Question3_EN,Question4_EN,Author`,
        SPHttpClient.configurations.v1
      );
      const data = await response.json();
      this._testimonialHtml =
        data.value && data.value.length > 0
          ? this.renderSimpleTestimonialsHTML(data.value[0])
          : `<p class="text-muted">${this._isArabic
            ? "لا توجد شهادات متاحة"
            : "No testimonials available"
          }</p>`;

      await this.loadEmployees();
    } catch (error) {
      console.error("Error loading employees:", error);
    }

    if (!this._employees || this._employees.length === 0) {
    }

    this.filterEmployees();
    this.render();
  }

  private async getEmailsToLoad(): Promise<string[]> {
    try {
      const response = await (this as any).context.spHttpClient.get(
        `${(this as any).context.pageContext.web.absoluteUrl
        }/_api/web/siteusers?$select=*&$top=2000`,
        SPHttpClient.configurations.v1
      );

      if (response.ok) {
        const data = await response.json();

        const emails = data.value
          .filter((user: any) => {
            const isValidEmail = user.Email && user.Email.includes("@");
            const isNotSystemAccount =
              !user.Email.includes("sharepoint.com") &&
              !user.Email.includes("onmicrosoft.com#ext#");
            const isNotGroup =
              !user.LoginName.includes("spo-grid-all-users") &&
              !user.LoginName.includes("SharingLinks") &&
              !user.LoginName.includes("Everyone") &&
              !user.LoginName.includes("ExternalUsers");
            const isRealUser =
              user.LoginName.includes("membership") ||
              user.LoginName.includes("federateduser");

            return (
              isValidEmail && isNotSystemAccount && isNotGroup && isRealUser
            );
          })
          .map((user: any) => user.Email);

        if (emails.length > 0) {
          return emails;
        }
      } else {
        console.error("❌ Failed to fetch users:", response.status);
      }
    } catch (error) {
      console.error("❌ Error fetching users:", error);
    }

    try {
      const response2 = await (this as any).context.spHttpClient.get(
        `${(this as any).context.pageContext.web.absoluteUrl
        }/_api/web/siteusers?$select=Email,LoginName&$top=1000`,
        SPHttpClient.configurations.v1
      );

      if (response2.ok) {
        const data2 = await response2.json();

        const emails2 = data2.value
          .filter(
            (user: any) =>
              user.Email &&
              user.Email.includes("@") &&
              !user.Email.includes("sharepoint.com") &&
              !user.Email.includes("#ext#") &&
              user.LoginName &&
              !user.LoginName.includes("Everyone") &&
              !user.LoginName.includes("SharingLinks")
          )
          .map((user: any) => user.Email);

        if (emails2.length > 0) {
          return emails2;
        }
      }
    } catch (error2) {
      console.error("❌ Fallback API failed:", error2);
    }

    return [];
  }

  private async loadEmployees(): Promise<void> {
    try {
      this._isLoading = true;
      this.render();

      const emailsToLoad = await this.getEmailsToLoad();
      const loadedEmployees: IEmployee[] = [];
      let successCount = 0;
      let failCount = 0;

      for (const email of emailsToLoad) {
        try {
          const profile = await this.getUserProfile(email);
          if (profile && (profile.DisplayName || profile.Email)) {
            const employee = await this.createEmployeeFromProfile(
              profile,
              email
            ); // Now async
            loadedEmployees.push(employee);
            successCount++;
          } else {
            failCount++;
          }
        } catch (error) {
          console.error(`❌ Failed to load profile for ${email}:`, error);
          const basicEmployee = this.createBasicEmployee(email);
          loadedEmployees.push(basicEmployee);
          failCount++;
        }
      }

      console.log(`✅ Successfully loaded ${successCount} profiles.`);
      console.log(`❌ Failed to load ${failCount} profiles.`);

      this._employees = loadedEmployees;

      this._employees.forEach((emp, index) => {
        emp.Id = index + 1;
      });

      this.filterEmployees();
    } catch (error) {
      this._employees = [];
    } finally {
      this._isLoading = false;
      this.render();
    }
  }
  private createBasicEmployee(email: string): IEmployee {
    return {
      // البيانات الأساسية من SharePoint Response
      AccountName: "",
      DisplayName: email.split("@")[0],
      Email: email,
      Title: "",
      DirectReports: [],
      ExtendedReports: [],
      ExtendedManagers: [],
      PictureUrl: "",
      PersonalUrl: "",
      UserUrl: "",

      // البيانات المستخرجة من UserProfileProperties
      FirstName: "",
      LastName: "",
      PreferredName: "",
      WorkPhone: "",
      HomePhone: "",
      CellPhone: "",
      Department: "",
      SPSDepartment: "",
      Manager: "",
      SPSJobTitle: "Employee",
      SPSHireDate: "",
      Nationality: "",
      WorkEmail: email,
      Office: "",
      SPSLocation: "",
      AboutMe: "",
      SPSSkills: "",
      SPSInterests: "",
      SPSSchool: "",
      SPSBirthday: "",
      UserName: "",

      // خصائص إضافية للتطبيق
      Id: 0,
      UserProfileProperties: [],
    };
  }

  private async createEmployeeFromProfile(
    profile: any,
    email: string
  ): Promise<IEmployee> {
    const displayName =
      profile.DisplayName ||
      profile.PreferredName ||
      (profile.FirstName && profile.LastName
        ? `${profile.FirstName} ${profile.LastName}`
        : profile.FirstName || profile.LastName || email.split("@")[0]);

    // Fetch communities for this employee
    // const communities = await this.loadVivaCommunities(email);

    return {
      AccountName: profile.AccountName || "",
      DisplayName: profile.DisplayName || displayName,
      Email: profile.Email || email,
      Title: profile.Title || "",
      DirectReports: profile.DirectReports || [],
      ExtendedReports: profile.ExtendedReports || [],
      ExtendedManagers: profile.ExtendedManagers || [],
      PictureUrl: profile.PictureUrl || "",
      PersonalUrl: profile.PersonalUrl || "",
      UserUrl: profile.UserUrl || "",
      // Communities: communities, // Assign fetched communities
      FirstName: profile.FirstName || "",
      LastName: profile.LastName || "",
      PreferredName: profile.PreferredName || "",
      WorkPhone: profile.WorkPhone || "",
      HomePhone: profile.HomePhone || "",
      CellPhone: profile.CellPhone || "",
      Department: profile.Department || "",
      SPSDepartment: profile.SPSDepartment || "",
      Manager: profile.Manager || "",
      SPSJobTitle: profile.Title || "",
      SPSHireDate: profile.SPSHireDate || "",
      Nationality: profile.Nationality || "",
      WorkEmail: profile.WorkEmail || email,
      Office: profile.Office || "",
      SPSLocation: profile.SPSLocation || "",
      AboutMe: profile.AboutMe || "",
      SPSSkills: profile.SPSSkills || "",
      SPSInterests: profile.SPSInterests || "",
      SPSSchool: profile.SPSSchool || "",
      SPSBirthday: profile.SPSBirthday || "",
      UserName: profile.UserName || "",
      Id: 0,
      UserProfileProperties: profile.UserProfileProperties || [],
    };
  }

  async validateImageUrl(url: string): Promise<string> {
    try {
      const response = await fetch(url, { method: "HEAD" });
      if (!response.ok) {
        return "";
      }
      return url;
    } catch (error) {
      console.error(`Error validating image URL ${url}:`, error);
      return "";
    }
  }

  private async getUserProfile(email: string): Promise<IEmployee> {
    try {
      if (!email || !email.trim()) {
        throw new Error("Invalid email provided");
      }

      const accountName = `i:0#.f|membership|${email}`;
      const apiUrl = `${(this as any).context.pageContext.web.absoluteUrl
        }/_api/SP.UserProfiles.PeopleManager/GetPropertiesFor(accountName=@v)?@v='${encodeURIComponent(
          accountName
        )}'`;

      const response = await (this as any).context.spHttpClient.get(
        apiUrl,
        SPHttpClient.configurations.v1
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch profile for ${email}: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      if (!data || typeof data !== "object") {
        throw new Error("Invalid response data");
      }

      // التحقق من وجود UserProfileProperties
      if (
        !data.UserProfileProperties ||
        !Array.isArray(data.UserProfileProperties)
      ) {
        // استخدام البيانات المباشرة فقط مع fallback للخصائص المهمة
        const fallbackResult = {
          AccountName: data.AccountName || "",
          DisplayName: data.DisplayName || "",
          Email: data.Email || email,
          Title: data.Title || "",
          DirectReports: data.DirectReports || [],
          ExtendedReports: data.ExtendedReports || [],
          ExtendedManagers: data.ExtendedManagers || [],
          PictureUrl: data.PictureUrl || "",
          PersonalUrl: data.PersonalUrl || "",
          UserUrl: data.UserUrl || "",

          // محاولة استخراج البيانات من الخصائص المباشرة إذا كانت متاحة
          FirstName: data.FirstName || "",
          LastName: data.LastName || "",
          PreferredName: data.PreferredName || data.DisplayName || "",
          WorkPhone: data.WorkPhone || "",
          HomePhone: data.HomePhone || "",
          CellPhone: data.CellPhone || "",
          Department: data.Department || "",
          SPSDepartment: data.Department || "",
          Manager: data.Manager || "",
          PictureURL: data.PictureURL || data.PictureUrl || "",
          SPSJobTitle: data.Title || "",
          SPSHireDate: data.SPSHireDate || "",
          Nationality: data.Nationality || "",
          WorkEmail: data.WorkEmail || data.Email || email,
          Office: data.Office || "",
          SPSLocation: data.SPSLocation || "",
          AboutMe: data.AboutMe || "",
          SPSSkills: data.SPSSkills || "",
          SPSInterests: data.SPSInterests || "",
          SPSSchool: data.SPSSchool || "",
          SPSBirthday: data.SPSBirthday || "",
          UserName: data.UserName || data.Email || email,

          Id: 0,
          UserProfileProperties: [],
        };

        return fallbackResult;
      }

      // دالة للحصول على قيمة من UserProfileProperties
      const getPropFromArray = (key: string): string => {
        if (
          data.UserProfileProperties &&
          Array.isArray(data.UserProfileProperties)
        ) {
          const prop = data.UserProfileProperties.find(
            (p: any) => p.Key === key
          );
          if (prop && prop.Value) {
            const value = prop.Value.toString().trim();

            return value !== "" ? value : "";
          }
        }

        return "";
      };

      // استخراج البيانات بناءً على الـ response الذي أرسلته
      const firstName = getPropFromArray("FirstName");
      const lastName = getPropFromArray("LastName");
      const preferredName = getPropFromArray("PreferredName");
      const jobTitle = getPropFromArray("SPS-JobTitle");
      const department =
        getPropFromArray("Department") || getPropFromArray("SPS-Department");
      // محاولة استخراج رقم الهاتف من مصادر متعددة
      const workPhone =
        getPropFromArray("WorkPhone") ||
        getPropFromArray("Phone") ||
        getPropFromArray("BusinessPhone");
      const HomePhone =
        getPropFromArray("HomePhone") ||
        getPropFromArray("MobilePhone") ||
        getPropFromArray("Mobile");
      const CellPhone = getPropFromArray("CellPhone");
      const pictureURL = getPropFromArray("PictureURL");
      const hireDate = getPropFromArray("SPS-HireDate");
      const nationality = getPropFromArray("Nationality");
      const workEmail = getPropFromArray("WorkEmail");
      const office = getPropFromArray("Office");
      const location = getPropFromArray("SPS-Location");
      const manager = getPropFromArray("Manager");

      const result = {
        AccountName: data.AccountName || "",
        DisplayName: data.DisplayName || "",
        Email: data.Email || email,
        Title: data.Title || "",
        DirectReports: data.DirectReports || [],
        ExtendedReports: data.ExtendedReports || [],
        ExtendedManagers: data.ExtendedManagers || [],
        PictureUrl: data.PictureUrl || "",
        PersonalUrl: data.PersonalUrl || "",
        UserUrl: data.UserUrl || "",

        FirstName: firstName,
        LastName: lastName,
        PreferredName: preferredName,
        WorkPhone: workPhone,
        HomePhone: HomePhone,
        CellPhone: CellPhone,
        Department: department,
        SPSDepartment: getPropFromArray("SPS-Department"),
        Manager: manager,
        PictureURL: pictureURL,
        SPSJobTitle: jobTitle,
        SPSHireDate: hireDate,
        Nationality: nationality,
        WorkEmail: workEmail,
        Office: office,
        SPSLocation: location,
        AboutMe: getPropFromArray("AboutMe"),
        SPSSkills: getPropFromArray("SPS-Skills"),
        SPSInterests: getPropFromArray("SPS-Interests"),
        SPSSchool: getPropFromArray("SPS-School"),
        SPSBirthday: getPropFromArray("SPS-Birthday"),
        UserName: getPropFromArray("UserName"),

        // خصائص إضافية للتطبيق
        Id: 0,
        UserProfileProperties: data.UserProfileProperties || [],
      };

      return result;
    } catch (error) {
      console.error(`Error fetching profile for ${email}:`, error);
      return {
        // البيانات الأساسية من SharePoint Response
        AccountName: "",
        DisplayName: email.split("@")[0],
        Email: email,
        Title: "",
        DirectReports: [],
        ExtendedReports: [],
        ExtendedManagers: [],
        PictureUrl: "",
        PersonalUrl: "",
        UserUrl: "",

        // البيانات المستخرجة من UserProfileProperties
        FirstName: "",
        LastName: "",
        PreferredName: "",
        WorkPhone: "",
        HomePhone: "",
        CellPhone: "",
        Department: "",
        SPSDepartment: "",
        Manager: "",
        SPSJobTitle: "",
        SPSHireDate: "",
        Nationality: "",
        WorkEmail: email,
        Office: "",
        SPSLocation: "",
        AboutMe: "",
        SPSSkills: "",
        SPSInterests: "",
        SPSSchool: "",
        SPSBirthday: "",
        UserName: "",

        // خصائص إضافية للتطبيق
        Id: 0,
        UserProfileProperties: [],
      };
    }
  }

  private sortEmployees(employees: IEmployee[]): IEmployee[] {
    if (!employees || employees.length === 0) return [];

    return [...employees].sort((a: IEmployee, b: IEmployee): number => {
      let valueA: string | Date | null = null;
      let valueB: string | Date | null = null;
      let isDate = false;

      // Helper function to handle empty or undefined values
      const getSafeString = (value: string | undefined | null): string => {
        return (value || "").trim().toLowerCase() || "zzz"; // Place empty values at the end
      };

      // Helper function to validate and parse date
      const getValidDate = (
        dateString: string | undefined | null
      ): Date | null => {
        if (!dateString || dateString.trim() === "") return null;
        const date = new Date(dateString);
        return isNaN(date.getTime()) ? null : date;
      };

      switch (this._sortField) {
        case "DisplayName":
        case "PreferredName":
          valueA = getSafeString(a.DisplayName || a.PreferredName);
          valueB = getSafeString(b.DisplayName || b.PreferredName);
          break;
        case "Department":
        case "SPSDepartment":
          valueA = getSafeString(a.Department || a.SPSDepartment);
          valueB = getSafeString(b.Department || b.SPSDepartment);
          break;
        case "SPSJobTitle":
          valueA = getSafeString(a.SPSJobTitle);
          valueB = getSafeString(b.SPSJobTitle);
          break;
        case "Nationality":
          valueA = getSafeString(a.Nationality);
          valueB = getSafeString(b.Nationality);
          break;
        case "SPS-HireDate":
          valueA = getValidDate(a.SPSHireDate);
          valueB = getValidDate(b.SPSHireDate);
          isDate = true;
          break;
        default:
          valueA = getSafeString(a.DisplayName || a.PreferredName);
          valueB = getSafeString(b.DisplayName || b.PreferredName);
      }

      if (isDate) {
        const dateA = valueA as Date | null;
        const dateB = valueB as Date | null;

        // Handle null/undefined dates (place them at the end)
        if (!dateA && !dateB) return 0;
        if (!dateA) return 1; // Null dates go to the end
        if (!dateB) return -1; // Null dates go to the end

        // Sort based on direction
        return this._sortDirection === "asc"
          ? dateA.getTime() - dateB.getTime()
          : dateB.getTime() - dateA.getTime();
      }

      const stringA = valueA as string;
      const stringB = valueB as string;

      // Use localeCompare with language support for Arabic
      const locale = this._isArabic ? "ar" : "en";
      return this._sortDirection === "asc"
        ? stringA.localeCompare(stringB, locale, { sensitivity: "base" })
        : stringB.localeCompare(stringA, locale, { sensitivity: "base" });
    });
  }

  private filterEmployees(
    nameSearch: string = "",
    jobTitleSearch: string = "",
    departmentFilter: string = "all",
    nationalityFilter: string = "all"
  ): void {
    if (!this._employees || this._employees.length === 0) {
      this._filteredEmployees = [];
      this._totalPages = 1;
      this._currentPage = 1;
      return;
    }

    let filtered = [...this._employees];

    if (
      this._activeFilter === "all" &&
      !this._searchQuery &&
      !nameSearch &&
      !jobTitleSearch &&
      departmentFilter === "all" &&
      nationalityFilter === "all"
    ) {
    } else {
      if (this._activeFilter === "new") {
        filtered = filtered.filter((employee: IEmployee) =>
          this.isNewEmployee(employee)
        );
      }

      if (this._searchQuery && this._searchQuery.trim() !== "") {
        filtered = filtered.filter(
          (employee: IEmployee) =>
            (employee.Department || employee.SPSDepartment || "")
              .toLowerCase()
              .includes(this._searchQuery.toLowerCase()) ||
            (employee.DisplayName || employee.PreferredName || "")
              .toLowerCase()
              .includes(this._searchQuery.toLowerCase()) ||
            (employee.SPSJobTitle || "")
              .toLowerCase()
              .includes(this._searchQuery.toLowerCase())
        );
      }

      if (nameSearch && nameSearch.trim() !== "") {
        filtered = filtered.filter((employee: IEmployee) =>
          (employee.DisplayName || employee.PreferredName || "")
            .toLowerCase()
            .includes(nameSearch.toLowerCase().trim())
        );
      }

      if (jobTitleSearch && jobTitleSearch.trim() !== "") {
        filtered = filtered.filter((employee: IEmployee) =>
          (employee.SPSJobTitle || "")
            .toLowerCase()
            .includes(jobTitleSearch.toLowerCase().trim())
        );
      }

      if (departmentFilter && departmentFilter !== "all") {
        filtered = filtered.filter(
          (employee: IEmployee) =>
            (
              employee.Department ||
              employee.SPSDepartment ||
              ""
            ).toLowerCase() === departmentFilter.toLowerCase()
        );
      }

      if (nationalityFilter && nationalityFilter !== "all") {
        filtered = filtered.filter(
          (employee: IEmployee) =>
            (employee.Nationality || "").toLowerCase() ===
            nationalityFilter.toLowerCase()
        );
      }
    }

    filtered = this.sortEmployees(filtered);

    this._filteredEmployees = filtered;
    const itemsPerPage = this.getItemsPerPage();
    this._totalPages = Math.ceil(filtered.length / itemsPerPage);

    if (this._currentPage > this._totalPages && this._totalPages > 0) {
      this._currentPage = 1;
    }
    this._currentPage = Math.min(
      this._currentPage,
      Math.max(1, this._totalPages)
    );

    if (this._employees.length - filtered.length > 5) {
      console.warn("⚠️ SIGNIFICANT REDUCTION in employee count:", {
        original: this._employees.length,
        filtered: filtered.length,
        lost: this._employees.length - filtered.length,
      });
    }
  }

  // دالة لتفعيل التاب الأول في المودال (مثل تاب All)
  private activateFirstModalTab() {
    // تفعيل التاب الأول (Contacts) مثل تاب All تماماً
    const allTabs = document.querySelectorAll("#detailsTab .nav-link");
    const allTabPanes = document.querySelectorAll(
      "#detailsTabContent .tab-pane"
    );

    // إزالة active من جميع التابات
    allTabs.forEach((tab) => tab.classList.remove("active"));
    allTabPanes.forEach((pane) => {
      pane.classList.remove("show", "active");
    });

    const firstTab = document.querySelector("#contacts-tab");
    const firstTabPane = document.querySelector("#contacts");
    if (firstTab) firstTab.classList.add("active");
    if (firstTabPane) {
      firstTabPane.classList.add("show", "active");
    }
  }

  private getManagerDetails(employee: IEmployee): {
    managerName: string;
    managerPhotoUrl: string;
    managerEmail: string;
  } {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);
    let managerName = t("No Manager", "لا يوجد مدير");
    let managerPhotoUrl = "/_layouts/15/images/person.gif";
    let managerEmail = "";



    if (employee.Manager && employee.Manager.trim()) {
      let extractedEmail = "";
      if (employee.Manager.includes("|")) {
        const parts = employee.Manager.split("|");
        if (parts.length >= 3) {
          extractedEmail = parts[2];
        }
      } else if (employee.Manager.includes("@")) {
        extractedEmail = employee.Manager;
      }

      const manager = this._employees.find(
        (emp) =>
          emp.AccountName === employee.Manager ||
          emp.Email === extractedEmail ||
          emp.WorkEmail === extractedEmail
      );

      if (manager) {
        managerName = manager.DisplayName || "";
        managerPhotoUrl = manager.PictureUrl || "";
        managerEmail = manager.WorkEmail || manager.Email || extractedEmail;

      } else if (extractedEmail) {
        managerName = extractedEmail.split("@")[0];
        managerPhotoUrl = `/_layouts/15/userphoto.aspx?size=S&accountname=${encodeURIComponent(
          employee.Manager
        )}`;
        managerEmail = extractedEmail;

      }
    }

    if (
      !managerEmail &&
      employee.ExtendedManagers &&
      employee.ExtendedManagers.length > 0
    ) {
      const firstManager = employee.ExtendedManagers[0];
      if (firstManager && firstManager.trim()) {
        let extractedEmail = "";
        if (firstManager.includes("|")) {
          const parts = firstManager.split("|");
          if (parts.length >= 3) {
            extractedEmail = parts[2];
          }
        } else if (firstManager.includes("@")) {
          extractedEmail = firstManager;
        }

        if (extractedEmail) {
          const manager = this._employees.find(
            (emp) =>
              emp.Email === extractedEmail || emp.WorkEmail === extractedEmail
          );

          if (manager) {
            managerName = manager.DisplayName || "";
            managerPhotoUrl = manager.PictureUrl || "";
            managerEmail = manager.WorkEmail || manager.Email || extractedEmail;

          } else {
            managerName = extractedEmail.split("@")[0];
            managerPhotoUrl = `/_layouts/15/userphoto.aspx?size=S&accountname=${encodeURIComponent(
              firstManager
            )}`;
            managerEmail = extractedEmail;

          }
        }
      }
    }

    // محاولة 3: البحث في UserProfileProperties
    if (!managerEmail && employee.UserProfileProperties) {
      const managerProperty = employee.UserProfileProperties.find(
        (p) =>
          p.Key === "Manager" ||
          p.Key === "Supervisor" ||
          p.Key.toLowerCase().includes("manager")
      );

      if (
        managerProperty &&
        managerProperty.Value &&
        managerProperty.Value.trim()
      ) {
        let extractedEmail = "";
        if (managerProperty.Value.includes("|")) {
          const parts = managerProperty.Value.split("|");
          if (parts.length >= 3) {
            extractedEmail = parts[2];
          }
        } else if (managerProperty.Value.includes("@")) {
          extractedEmail = managerProperty.Value;
        }

        if (extractedEmail) {
          managerName = extractedEmail.split("@")[0];
          managerPhotoUrl = `/_layouts/15/userphoto.aspx?size=S&accountname=${encodeURIComponent(
            managerProperty.Value
          )}`;
          managerEmail = extractedEmail;

        }
      }
    }

    // إذا لم نجد إيميل المدير، نجلب البيانات async ونحدث الموديل
    if (!managerEmail || managerEmail.trim() === "") {


      // جلب بيانات المدير async وتحديث الموديل
      this.fetchAndUpdateManagerData(
        employee.Email || employee.WorkEmail || ""
      );
    } else {
    }



    return { managerName, managerPhotoUrl, managerEmail };
  }

  // دالة جلب بيانات المدير async وتحديث الموديل
  private async fetchAndUpdateManagerData(userEmail: string): Promise<void> {
    try {


      if (!userEmail || userEmail.trim() === "") {
        console.warn("⚠️ إيميل المستخدم فاضي، لا يمكن جلب بيانات المدير");
        return;
      }

      // محاولة 1: SharePoint User Profile API
      let managerData = await this.getManagerFromUserProfile(userEmail);

      // محاولة 2: Microsoft Graph API إذا فشلت الأولى
      if (!managerData) {
        managerData = await this.getManagerFromGraphAPI(userEmail);
      }

      if (managerData && managerData.email) {

        // تحديث بيانات المدير في الموديل
        const modal = document.querySelector("#detailsModal");
        if (modal) {
          const managerPhoto = modal.querySelector("#modal-manager-photo");
          const managerNameElement = modal.querySelector("#modal-manager-name");

          if (managerPhoto) {
            managerPhoto.setAttribute("data-manager-email", managerData.email);
            (managerPhoto as HTMLElement).style.cursor = "pointer";
            (managerPhoto as HTMLElement).title = "عرض بروفايل المدير";
            if (managerData.photoUrl) {
              (managerPhoto as HTMLImageElement).src = managerData.photoUrl;
            }

          }

          if (managerNameElement) {
            managerNameElement.textContent =
              managerData.name || managerData.email.split("@")[0];
          }

        } else {
          console.warn("⚠️ لم يتم العثور على الموديل لتحديث بيانات المدير");
        }
      } else {
      }
    } catch (error) {
      console.error("❌ خطأ في جلب وتحديث بيانات المدير:", error);
    }
  }

  // دالة جلب بيانات المدير من SharePoint User Profile API
  private async getManagerFromUserProfile(userEmail: string): Promise<{
    email: string;
    name: string;
    photoUrl: string;
  } | null> {
    try {
      if (!userEmail || userEmail.trim() === "") {
        return null;
      }



      const userProfileUrl = `${(this as any).context.pageContext.web.absoluteUrl
        }/_api/SP.UserProfiles.PeopleManager/GetPropertiesFor(accountName=@v)?@v='${encodeURIComponent(
          userEmail
        )}'`;

      const response = await (this as any).context.spHttpClient.get(
        userProfileUrl,
        SPHttpClient.configurations.v1
      );

      if (!response.ok) {
        console.warn("⚠️ فشل في جلب بيانات المستخدم من User Profile API");
        return null;
      }

      const userData = await response.json();

      const userProperties = userData.UserProfileProperties || [];


      // const managerRelatedProperties = userProperties.filter(
      //   (prop: any) =>
      //     prop.Key.toLowerCase().includes("manager") ||
      //     prop.Key.toLowerCase().includes("supervisor") ||
      //     prop.Key.toLowerCase().includes("reportsto") ||
      //     prop.Key === "Manager" ||
      //     prop.Key === "Supervisor"
      // );


      const managerProperty = userProperties.find(
        (prop: any) => prop.Key === "Manager" || prop.Key === "Supervisor"
      );

      if (!managerProperty || !managerProperty.Value) {


        // محاولة بديلة: استخدام Microsoft Graph API
        try {
          const graphManagerData = await this.getManagerFromGraphAPI(userEmail);
          if (graphManagerData) {

            return graphManagerData;
          }
        } catch (graphError) {
          console.warn(
            "⚠️ فشل في جلب بيانات المدير من Microsoft Graph API:",
            graphError
          );
        }

        return null;
      }


      // استخراج إيميل المدير من القيمة
      let managerEmail = "";
      let managerName = "";

      if (managerProperty.Value.includes("|")) {
        // تنسيق: i:0#.f|membership|<EMAIL>
        const parts = managerProperty.Value.split("|");
        if (parts.length >= 3) {
          managerEmail = parts[2];
          managerName = managerEmail.split("@")[0];
        }
      } else if (managerProperty.Value.includes("@")) {
        // تنسيق مباشر: <EMAIL>
        managerEmail = managerProperty.Value;
        managerName = managerEmail.split("@")[0];
      }

      if (!managerEmail) {
        return null;
      }

      // جلب صورة المدير
      const managerPhotoUrl = `/_layouts/15/userphoto.aspx?size=S&accountname=${encodeURIComponent(
        managerEmail
      )}`;



      return {
        email: managerEmail,
        name: managerName,
        photoUrl: managerPhotoUrl,
      };
    } catch (error) {
      console.error("❌ خطأ في جلب بيانات المدير من User Profile API:", error);
      return null;
    }
  }

  private async getManagerFromGraphAPI(userEmail: string): Promise<{
    email: string;
    name: string;
    photoUrl: string;
  } | null> {
    try {


      const client = await (this as any).context.aadHttpClientFactory.getClient(
        "https://graph.microsoft.com"
      );

      const userResponse = await client.get(
        `https://graph.microsoft.com/v1.0/users/${encodeURIComponent(
          userEmail
        )}?$expand=manager`,
        AadHttpClient.configurations.v1
      );

      if (!userResponse.ok) {
        console.warn("⚠️ فشل في جلب بيانات المستخدم من Microsoft Graph API");
        return null;
      }

      const userData = await userResponse.json();

      if (!userData.manager) {
        return null;
      }

      const manager = userData.manager;

      const managerEmail = manager.mail || manager.userPrincipalName;
      const managerName =
        manager.displayName || managerEmail?.split("@")[0] || "";

      if (!managerEmail) {
        return null;
      }

      // جلب صورة المدير
      let managerPhotoUrl = `/_layouts/15/userphoto.aspx?size=S&accountname=${encodeURIComponent(
        managerEmail
      )}`;

      try {
        // محاولة جلب صورة المدير من Graph API
        const photoResponse = await client.get(
          `https://graph.microsoft.com/v1.0/users/${encodeURIComponent(
            managerEmail
          )}/photo/$value`,
          AadHttpClient.configurations.v1
        );

        if (photoResponse.ok) {
          const photoBlob = await photoResponse.blob();
          managerPhotoUrl = URL.createObjectURL(photoBlob);
        }
      } catch (photoError) {

      }



      return {
        email: managerEmail,
        name: managerName,
        photoUrl: managerPhotoUrl,
      };
    } catch (error) {
      console.error(
        "❌ خطأ في جلب بيانات المدير من Microsoft Graph API:",
        error
      );
      return null;
    }
  }

  private getColleagues(
    employee: IEmployee
  ): { name: string; photoUrl: string; email: string }[] {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);

    // تحديد القسم
    const department = (
      employee.Department ||
      employee.SPSDepartment ||
      ""
    ).trim();

    // تصفية الزملاء في نفس القسم ماعدا الموظف الحالي
    const colleagues = this._employees
      .filter((emp) => {
        const empDepartment = (
          emp.Department ||
          emp.SPSDepartment ||
          ""
        ).trim();

        // لازم القسم يطابق بالضبط (مع تجاهل حالة الحروف)
        const sameDepartment =
          empDepartment.toLowerCase() === department.toLowerCase();

        // تجنب تكرار الموظف نفسه
        const isNotSamePerson =
          emp.AccountName !== employee.AccountName &&
          emp.Email !== employee.Email &&
          emp.WorkEmail !== employee.WorkEmail;

        return sameDepartment && isNotSamePerson;
      })
      .map((colleague) => {
        const name =
          colleague.DisplayName ||
          colleague.PreferredName ||
          (colleague.FirstName && colleague.LastName
            ? `${colleague.FirstName} ${colleague.LastName}`
            : colleague.FirstName ||
            colleague.LastName ||
            t("Unknown", "غير معروف"));

        const photoUrl =
          colleague.PictureUrl ||
          (colleague.AccountName || colleague.Email || colleague.WorkEmail
            ? `/_layouts/15/userphoto.aspx?size=S&accountname=${encodeURIComponent(
              colleague.AccountName || colleague.Email || colleague.WorkEmail
            )}`
            : "/_layouts/15/images/person.gif");

        const email = colleague.Email || colleague.WorkEmail || "";

        return { name, photoUrl, email };
      });


    return colleagues;
  }

  // دالة جديدة للمودال مع البيانات الديناميكية
  private renderModalWithDynamicData(employee: IEmployee): string {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);
    const getSafeString = (
      value: string | undefined | null | any,
      defaultValue: string
    ): string => {
      return value ? value : defaultValue;
    };

    const employeeName = getSafeString(
      employee.DisplayName ||
      employee.PreferredName ||
      (employee.FirstName && employee.LastName
        ? `${employee.FirstName} ${employee.LastName}`
        : employee.FirstName || employee.LastName),
      t("Employee Name", "اسم الموظف")
    );
    const jobTitle = getSafeString(
      employee.SPSJobTitle,
      t("No Job Title", "لا يوجد منصب")
    );
    const departmentFromProperties = employee.UserProfileProperties?.find(
      (p) => p.Key === "Department" || p.Key === "SPS-Department"
    )?.Value;
    const Department = employee.Department || employee.SPSDepartment || departmentFromProperties

    
    const photoUrl =
      employee.PictureUrl ||
      `/_layouts/15/userphoto.aspx?size=L&accountname=${encodeURIComponent(
        employee.AccountName || ""
      )}` ||
      "/_layouts/15/images/person.gif";
    const email = getSafeString(
      employee.WorkEmail || employee.Email,
      t("No email available", "لا يوجد بريد إلكتروني متاح")
    );
    const phone = getSafeString(
      employee.WorkPhone || employee.HomePhone || employee.CellPhone,
      ""
    );
    // سيتم جلب بيانات المدير لاحقاً بشكل async
    const managerName = "جاري التحميل...";
    const managerPhotoUrl = "/_layouts/15/images/person.gif";
    const managerEmail = "";
    const colleagues = this.getColleagues(employee);

    // Render communities specific to the employee
    const communitiesHtml =
      employee.Communities && employee.Communities.length > 0
        ? employee.Communities.map((community) => {
          const communityImage = community.mail
            ? `/_layouts/15/userphoto.aspx?size=M&accountname=${encodeURIComponent(
              community.mail
            )}`
            : "https://img.freepik.com/premium-vector/login-business-illustration_736766-14.jpg";
          const communityIcon =
            "https://i.pinimg.com/736x/d0/ba/6c/d0ba6cf4ba1cfdd464ea96dd95e63e37.jpg";

          return `
                        <div class="col-md-3">
                            <a href="https://engage.cloud.microsoft/main/groups/eyJfdHlwZSI6Ikdyb3VwIiwiaWQiOiI${btoa(community.id)}"}/all" target="_blank" class="text-decoration-none">
                                <div class="card position-relative overflow-hidden card-color communities-card" style="cursor: pointer; transition: transform 0.2s;">
                                    <div class="card-img-top">
                                        <img src="${communityImage}" class="card-img-top" alt="${community.name} Header">
                                    </div>
                                    <div class="card-body text-center pt-2">
                                        <div class="d-flex justify-content-between position-relative">
                                            <div class="profile-img-container">
                                                <img src="${communityIcon}" alt="${community.name} Icon" class="img-fluid">
                                            </div>
                                            <button class="btn btn-link position-absolute top-0 end-0 m-2 p-0 join-group-btn" data-group-id="${community.id}" onclick="event.preventDefault(); event.stopPropagation();">
                                                <i class="bi bi-plus-circle fs-3 head-color"></i>
                                            </button>
                                        </div>
                                        <h6 class="head-color fw-bold text-start mt-3">${community.name}</h6>
                                    </div>
                                </div>
                            </a>
                        </div>
                    `;
        }).join("")
        : `<p class="text-muted">${t(
          "No communities available",
          "لا توجد مجتمعات متاحة"
        )}</p>`;

    return `
        <div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header border-0">
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="modal-close-btn"></button>
                    </div>
                    <div class="modal-body p-4">
                        <!-- Profile Header -->
                        <div class="d-flex align-items-center mb-4 flex-wrap">
                            <img id="modal-employee-photo" src="${photoUrl}" alt="${employeeName}"
                                class="rounded-circle me-3" style="width:80px; height:80px; object-fit: cover;"
                                onerror="this.src='/_layouts/15/images/person.gif';">
                            <div class="mt-2">
                                <h3 id="modal-employee-name" class="mb-1 head-color">${employeeName}</h3>
                                <p id="modal-employee-title" class="text-color mb-0">${jobTitle}</p>
                                <p id="modal-employee-Department" class="text-color mb-0">${Department}</p>
                            </div>
                            <div class="ms-auto d-flex align-items-center gap-3">
                                <div class="contact-options flex-wrap">
                                    <a id="modal-email-link" target="_blank" href="${email ? `mailto:${email}` : "#"
      }" class="p-2 mt-2 mx-2 d-flex align-items-center justify-content-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" id="Message" width="26.291" height="25.249" viewBox="0 0 26.291 25.249">
                                            <path id="Stroke_1" data-name="Stroke 1" d="M7.366,6.1c-1.484,0-3.279-.909-5.335-2.7A26.173,26.173,0,0,1-.524.828.96.96,0,1,1,.944-.409C2.44,1.365,5.508,4.18,7.366,4.18S12.261,1.368,13.74-.4A.96.96,0,0,1,15.215.824,25.818,25.818,0,0,1,12.682,3.4C10.64,5.189,8.852,6.1,7.366,6.1Z" transform="translate(5.794 8.641)" fill="#ed3c8a"></path>
                                            <path id="Stroke_3" data-name="Stroke 3" d="M12.4-.75c5.007,0,8.148.869,10.184,2.818s2.962,4.985,2.962,9.807-.913,7.845-2.962,9.806S17.4,24.5,12.4,24.5,4.248,23.63,2.212,21.681-.75,16.7-.75,11.875.163,4.029,2.212,2.068,7.388-.75,12.4-.75Zm0,23.33c8.6,0,11.226-2.5,11.226-10.705S21,1.169,12.4,1.169,1.169,3.671,1.169,11.875,3.792,22.58,12.4,22.58Z" transform="translate(0.75 0.75)" fill="#ed3c8a"></path>
                                        </svg>
                                        <span class="mx-2">${t(
        "Send Email",
        "إرسال إيميل"
      )}</span>
                                    </a>
                                    <a id="modal-chat-link" target="_blank" href="${email
        ? `https://teams.microsoft.com/l/chat/0/0?users=${encodeURIComponent(
          email
        )}`
        : "#"
      }" class="p-2 mt-2 mx-2 d-flex align-items-center justify-content-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="26.722" height="26.721" viewBox="0 0 26.722 26.721">
                                            <g id="chat-ro" transform="translate(1 1)">
                                                <path id="Path_77853" data-name="Path 77853" d="M18.2,12.937h-.011a.969.969,0,1,1,0-1.937H18.2a.969.969,0,0,1,0,1.937Z" transform="translate(-0.887 0.392)" fill="#ee3c8a"></path>
                                                <path id="Path_77853-2" data-name="Path 77853" d="M18.2,12.937h-.011a.969.969,0,1,1,0-1.937H18.2a.969.969,0,0,1,0,1.937Z" transform="translate(-5.729 0.392)" fill="#ee3c8a"></path>
                                                <path id="Path_77853-3" data-name="Path 77853" d="M18.2,12.937h-.011a.969.969,0,1,1,0-1.937H18.2a.969.969,0,0,1,0,1.937Z" transform="translate(-10.572 0.392)" fill="#ee3c8a"></path>
                                                <path id="Path_77854" data-name="Path 77854" d="M14.361,27.721a13.226,13.226,0,0,1-5.949-1.395,1.068,1.068,0,0,0-.741-.1l-2.759.738A2.568,2.568,0,0,1,2.2,26.043a2.557,2.557,0,0,1-.443-2.234l.738-2.759a1.059,1.059,0,0,0-.1-.741A13.363,13.363,0,0,1,14.361,1a13.361,13.361,0,0,1,9.447,22.808A13.273,13.273,0,0,1,14.361,27.721ZM7.9,24.263a3.1,3.1,0,0,1,1.373.33,11.426,11.426,0,0,0,16.51-10.232,11.424,11.424,0,0,0-19.5-8.078A11.433,11.433,0,0,0,4.128,19.446a2.991,2.991,0,0,1,.234,2.1L3.625,24.31a.626.626,0,0,0,.108.554.644.644,0,0,0,.505.256.672.672,0,0,0,.173-.024l2.758-.738A2.831,2.831,0,0,1,7.9,24.263Z" transform="translate(-2 -2)" fill="#ee3c8a"></path>
                                            </g>
                                        </svg>
                                        <span class="mx-2">${t(
        "Start Chat",
        "بدء محادثة"
      )}</span>
                                    </a>
                                    ${phone
        ? `
                                    <a id="modal-phone-link" href="tel:${phone}" class="p-2 mt-2 mx-2 d-flex align-items-center justify-content-center">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="26.053" height="26.056" viewBox="0 0 21.053 21.056">
                                        <path id="Stroke_1" data-name="Stroke 1" d="M15.425,20.306c-2.663,0-6.059-2.046-10.094-6.081-3.155-3.155-5.1-5.941-5.8-8.28A4.559,4.559,0,0,1-.082,1.856h0C.193,1.441,1.749-.75,3.875-.75A3.356,3.356,0,0,1,6.029.076C7.636,1.4,8.51,2.488,8.779,3.5a3.279,3.279,0,0,1-.409,2.433c-.476.977-.851,1.748,1.326,3.926,1.144,1.144,2.046,1.7,2.757,1.7a2.7,2.7,0,0,0,1.17-.374,3.827,3.827,0,0,1,1.726-.5c1.238,0,2.512.876,4.131,2.842a3.286,3.286,0,0,1,.772,2.742,5.96,5.96,0,0,1-2.55,3.37h0A3.841,3.841,0,0,1,15.425,20.306ZM1.21,2.693q-.018.03-.038.059C.137,4.2.973,7.688,6.42,13.135c4.653,4.653,7.465,5.63,9,5.63a2.32,2.32,0,0,0,1.38-.381q.029-.021.06-.039a4.449,4.449,0,0,0,1.877-2.394,1.754,1.754,0,0,0-.454-1.448c-1.263-1.533-2.225-2.279-2.94-2.279a2.533,2.533,0,0,0-1.051.348,4.041,4.041,0,0,1-1.844.53c-1.156,0-2.378-.684-3.846-2.151A6.5,6.5,0,0,1,6.49,7.567A3.7,3.7,0,0,1,6.984,5.26A1.821,1.821,0,0,0,7.29,3.9A6.454,6.454,0,0,0,5.051,1.266A1.835,1.835,0,0,0,3.875.791C2.636.791,1.461,2.287,1.21,2.693Z" transform="translate(0.748 0.75)" fill="#ed3c8a"></path>
                                      </svg>
                                      <span class="mx-2">${t(
          "Call",
          "اتصال"
        )}</span>
                                    </a>
                                  `
        : ""
      }

                                </div>
                            </div>
                        </div>

                        <!-- Tabs -->
                        <ul class="nav nav-tabs mb-4" id="detailsTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="contacts-tab" data-bs-toggle="tab" data-bs-target="#contacts" type="button" role="tab">${t(
        "Contacts",
        "جهات الاتصال"
      )}</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="org-tab" data-bs-toggle="tab" data-bs-target="#organization" type="button" role="tab">${t(
        "Organization",
        "المؤسسة"
      )}</button>
                            </li>
                        </ul>

                        <div class="tab-content" id="detailsTabContent">
                            <!-- Contacts Tab -->
                            <div class="tab-pane fade active show" id="contacts" role="tabpanel">
                                <div class="row mb-4">
                                    <div class="col-md-4 mb-2">
                                        <a id="modal-contact-email" target="_blank" href="${email ? `mailto:${email}` : "#"
      }" class="d-flex align-items-center text-decoration-none text-dark">
                                            <i class="bi bi-envelope fs-4 me-2 main-color"></i>
                                            <span class="head-color">${email}</span>
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <a id="modal-contact-chat" target="_blank" href="${email
        ? `https://teams.microsoft.com/l/chat/0/0?users=${encodeURIComponent(
          email
        )}`
        : "#"
      }" class="d-flex align-items-center text-decoration-none text-dark">
                                            <i class="bi bi-chat-dots fs-4 me-2 main-color"></i>
                                            <span class="head-color">${t(
        "Start Chat",
        "بدء محادثة"
      )}</span>
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        ${phone
        ? `<a id="modal-contact-phone" href="${phone ? `tel:${phone}` : "#"
        }" class="d-flex align-items-center text-decoration-none text-dark">
                                            <i class="bi bi-telephone fs-4 me-2 main-color"></i>
                                            <span class="head-color">${phone}</span>
                                        </a>`
        : ""
      }
                                    </div>
                                </div>

                                <!-- About Me Section -->
                                <h5 class="mt-2 tittle">${t(
        "About Me",
        "نبذة عني"
      )}</h5>
                                <p id="modal-about-me">${getSafeString(
        employee.AboutMe,
        t(
          "No about me information available",
          "لا توجد معلومات نبذة عني"
        )
      )}</p>

                                <!-- Skills & Projects -->
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6>${t("Skills", "المهارات")}</h6>
                                        <ul id="modal-skills">
                                            ${getSafeString(
        employee.SPSSkills,
        t(
          "No Skills available",
          "لا توجد معلومات"
        )
      )}
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>${t(
        "Interests & Hobbies",
        "الاهتمامات والهوايات"
      )}</h6>
                                        <ul id="modal-interests">
                                         ${getSafeString(
        employee.SPSInterests,
        t(
          "No interests available",
          "لا توجد اهتمامات متاحة"
        )
      )}
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>${t(
        "Certificates",
        "الشهادات"
      )}</h6>
                                        <ul id="modal-school">
                                         ${getSafeString(
        employee.SPSSchool,
        t(
          "No Certificates information available",
          "لا توجد معلومات عن المدرسة"
        )
      )}
                                        </ul>
                                      </div>
                                    
                                </div>

                                <!-- Testimonial Section -->
                                <h5 class="mt-4 tittle">${t(
        "Testimonial",
        "شهادة"
      )}</h5>
                                <div id="modal-testimonial" class="card p-3 mb-4 card-color"></div>

                                <!-- My Communities Section -->
                                <h5 class="mt-2 tittle">${t(
        "My Communities",
        "مجتمعاتي"
      )}</h5>
                                <div id="modal-communities" class="row g-3">
                                    ${communitiesHtml}
                                </div>
                            </div>

                            <!-- Organization Tab -->
                            <div class="tab-pane fade" id="organization" role="tabpanel">
                                <!-- Report To -->
                                <h5 class="head-color">${t(
        "Report to",
        "يرفع تقرير إلى"
      )}</h5>
                                <div class="d-flex align-items-center mb-4">
                                    <img id="modal-manager-photo" src="${managerPhotoUrl}" alt="${managerName}"
                                        class="rounded-circle me-2" style="width:40px; height:40px; object-fit: cover; cursor: pointer;"
                                        onerror="this.src='/_layouts/15/images/person.gif';"
                                        data-manager-email="${managerEmail}"
                                        title="${t(
        "View manager profile",
        "عرض بروفايل المدير"
      )}">
                                    <span id="modal-manager-name" class="head-color">${managerName} - ${managerEmail}</span>
                                </div>
                                <hr>
                                <!-- Organization Team (You work with) -->
                                <h5 class="head-color">${t(
        "Organization",
        "المؤسسة"
      )} <small class="text-muted d-block">${t(
        "You work with",
        "تعمل مع"
      )}</small></h5>
                                <div class="row" id="modal-team-members">
                                    ${colleagues.length > 0
        ? colleagues
          .map(
            (colleague) => `
                                            <div class="col-md-4 d-flex align-items-center mb-3">
                                                <img src="${colleague.photoUrl}"
                                                    alt="${colleague.name}"
                                                    class="rounded-circle me-2"
                                                    style="width:40px; height:40px; object-fit: cover; cursor: pointer;"
                                                    onerror="this.src='/_layouts/15/images/person.gif';"
                                                    data-colleague-email="${colleague.email
              }"
                                                    title="${t(
                "View colleague profile",
                "عرض بروفايل الزميل"
              )}">
                                                <span class="head-color">${colleague.name
              }</span>
                                            </div>
                                        `
          )
          .join("")
        : `<p class="text-muted">${t(
          "No colleagues in this department",
          "لا يوجد زملاء في هذا القسم"
        )}</p>`
      }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>`;
  }

  // دالة شاملة لمسح جميع البيانات من الموديل
  private clearAllModalData(): void {
    const modal = document.querySelector("#detailsModal");
    if (!modal) return;

    // مسح الصورة الشخصية
    const profileImg = modal.querySelector(
      ".modal-body img"
    ) as HTMLImageElement;
    if (profileImg) {
      profileImg.src = "/_layouts/15/images/person.gif";
      profileImg.alt = "";
    }

    // مسح الاسم
    const nameElement = modal.querySelector(".modal-body h3");
    if (nameElement) {
      nameElement.textContent = "";
    }

    // مسح المنصب
    const jobTitleElement = modal.querySelector(".modal-body p.text-color");
    if (jobTitleElement) {
      jobTitleElement.textContent = "";
    }

    // مسح جميع روابط الإيميل
    const emailLinks = modal.querySelectorAll(
      'a[href^="mailto:"], #modal-email-link, #modal-contact-email'
    );
    emailLinks.forEach((link) => {
      (link as HTMLAnchorElement).href = "#";
      const span = link.querySelector("span");
      if (span) span.textContent = "";
    });

    // مسح جميع أرقام الهواتف
    this.clearAllPhoneNumbersFromModal();

    // مسح روابط الشات
    const chatLinks = modal.querySelectorAll(
      "#modal-chat-link, #modal-contact-chat"
    );
    chatLinks.forEach((link) => {
      (link as HTMLAnchorElement).href = "#";
    });

    // مسح بيانات تاب الاتصالات
    const contactsTab = modal.querySelector("#contacts");
    if (contactsTab) {
      const aboutMeElement = contactsTab.querySelector("#modal-about-me");
      if (aboutMeElement) aboutMeElement.textContent = "";

      const skillsElement = contactsTab.querySelector("#modal-skills");
      if (skillsElement) skillsElement.innerHTML = "";

      const interestsElement = contactsTab.querySelector("#modal-interests");
      if (interestsElement) interestsElement.innerHTML = "";

      const schoolElement = contactsTab.querySelector("#modal-school");
      if (schoolElement) schoolElement.innerHTML = "";

      const communitiesElement =
        contactsTab.querySelector("#modal-communities");
      if (communitiesElement) communitiesElement.innerHTML = "";
    }

    // مسح بيانات تاب التنظيم
    const orgTab = modal.querySelector("#organization");
    if (orgTab) {
      const managerPhotoElement = orgTab.querySelector(
        "#modal-manager-photo"
      ) as HTMLImageElement;
      if (managerPhotoElement) {
        managerPhotoElement.src = "/_layouts/15/images/person.gif";
        managerPhotoElement.alt = "";
      }

      const managerNameElement = orgTab.querySelector("#modal-manager-name");
      if (managerNameElement) managerNameElement.textContent = "";

      const teamMembersContainer = orgTab.querySelector("#modal-team-members");
      if (teamMembersContainer) teamMembersContainer.innerHTML = "";

      const testimonialContainer = modal.querySelector("#modal-testimonial");
      if (testimonialContainer) testimonialContainer.innerHTML = "";
    }

    // مسح تاب LinkedIn
    const linkedinTab = modal.querySelector("#linkedin");
    if (linkedinTab) {
      const linkedinContent = linkedinTab.querySelector("p");
      if (linkedinContent) linkedinContent.innerHTML = "";
    }
  }

  // دالة مساعدة لمسح جميع أرقام الهواتف من المودال
  private clearAllPhoneNumbersFromModal(): void {
    const modal = document.querySelector("#detailsModal");
    if (!modal) return;

    const phoneSelectors = [
      'a[href^="tel:"]',
      "#modal-phone-link",
      "#modal-contact-phone",
      "[data-phone]",
      "[data-phone-number]",
    ];

    phoneSelectors.forEach((selector) => {
      const elements = modal.querySelectorAll(selector);
      elements.forEach((element, index) => {
        const linkElement = element as HTMLAnchorElement;

        if (linkElement.href && linkElement.href.includes("tel:")) {
          linkElement.href = "#";
        }

        const spans = linkElement.querySelectorAll("span");
        spans.forEach((span, spanIndex) => {
          if (span.textContent) {
            span.textContent = "";
          }
        });
      });
    });

    // مسح إضافي لأي نص يحتوي على أرقام في المودال
    const allSpans = modal.querySelectorAll("span");
    allSpans.forEach((span, index) => {
      if (
        span.textContent &&
        (span.textContent.includes("+") || /\d{3,}/.test(span.textContent))
      ) {
        span.textContent = "";
      }
    });
  }

  async updateModalData(employee: IEmployee) {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);

    // debugging بيانات الموظف
    console.log("🔍 تحديث بيانات الموديل للموظف:", {
      name: employee.DisplayName,
      department: employee.Department,
      spsDepartment: employee.SPSDepartment,
      email: employee.Email || employee.WorkEmail
    });

    // مسح جميع البيانات السابقة من الموديل قبل عرض البيانات الجديدة
    this.clearAllModalData();

    const displayName =
      employee.DisplayName ||
      employee.PreferredName ||
      (employee.FirstName && employee.LastName
        ? `${employee.FirstName} ${employee.LastName}`
        : employee.FirstName || employee.LastName || t("No Name", "لا اسم"));
    const jobTitle = employee.SPSJobTitle || t("No Job Title", "لا يوجد منصب");
    const email = employee.Email || employee.WorkEmail || "";
    let phone =
      employee.WorkPhone || employee.HomePhone || employee.CellPhone || "";

    if (!phone && employee.UserProfileProperties) {
      const phoneProperties = employee.UserProfileProperties.filter(
        (p) =>
          p.Key === "WorkPhone" ||
          p.Key === "HomePhone" ||
          p.Key === "CellPhone"
      );
      if (phoneProperties.length > 0) {
        phone = phoneProperties[0].Value || "";
      }
    }
    const departmentFromProperties = employee.UserProfileProperties?.find(
      (p) => p.Key === "Department" || p.Key === "SPS-Department"
    )?.Value;
    // const department = employee.Department || employee.SPSDepartment || departmentFromProperties || t("No Department", "لا يوجد قسم");
    if (!employee.Department && !employee.SPSDepartment && !departmentFromProperties) {
      console.warn(`No department found for employee: ${displayName}`);
    }
    const photoUrl =
      employee.PictureUrl ||
      `/_layouts/15/userphoto.aspx?size=L&accountname=${encodeURIComponent(
        employee.AccountName || ""
      )}` ||
      "/_layouts/15/images/person.gif";
    const { managerName, managerPhotoUrl, managerEmail } =
      this.getManagerDetails(employee);
    const colleagues = this.getColleagues(employee);

    // Update communities
    const communitiesHtml =
      employee.Communities && employee.Communities.length > 0
        ? employee.Communities.map((community) => {
          const communityImage = community.mail
            ? `/_layouts/15/userphoto.aspx?size=M&accountname=${encodeURIComponent(
              community.mail
            )}`
            : "https://img.freepik.com/premium-vector/login-business-illustration_736766-14.jpg";
          const communityIcon =
            "https://i.pinimg.com/736x/d0/ba/6c/d0ba6cf4ba1cfdd464ea96dd95e63e37.jpg";

          return `
                        <div class="col-md-3">
                            <a href="https://engage.cloud.microsoft/main/groups/eyJfdHlwZSI6Ikdyb3VwIiwiaWQiOiI${btoa(community.id)}"}/all" target="_blank" class="text-decoration-none">
                                <div class="card position-relative overflow-hidden card-color communities-card" style="cursor: pointer; transition: transform 0.2s;">
                                    <div class="card-img-top">
                                        <img src="${communityImage}" class="card-img-top" alt="${community.name} Header">
                                    </div>
                                    <div class="card-body text-center pt-2">
                                        <div class="d-flex justify-content-between position-relative">
                                            <div class="profile-img-container">
                                                <img src="${communityIcon}" alt="${community.name} Icon" class="img-fluid">
                                            </div>
                                            <button class="btn btn-link position-absolute top-0 end-0 m-2 p-0 join-group-btn" data-group-id="${community.id}" onclick="event.preventDefault(); event.stopPropagation();">
                                                <i class="bi bi-plus-circle fs-3 head-color"></i>
                                            </button>
                                        </div>
                                        <h6 class="head-color fw-bold text-start mt-3">${community.name}</h6>
                                    </div>
                                </div>
                            </a>
                        </div>
                    `;
        }).join("")
        : `<p class="text-muted">${t(
          "No communities available",
          "لا توجد مجتمعات متاحة"
        )}</p>`;

    const modalElement = document.querySelector("#detailsModal");
    if (modalElement) {
      const modalPhoto = document.getElementById(
        "modal-employee-photo"
      ) as HTMLImageElement;
      const modalName = document.getElementById("modal-employee-name");
      const modalTitle = document.getElementById("modal-employee-title");
      const modalDepartment: any = document.getElementById(
        "modal-employee-Department"
      );
      const modalEmailLink = document.getElementById(
        "modal-email-link"
      ) as HTMLAnchorElement;
      const modalChatLink = document.getElementById(
        "modal-chat-link"
      ) as HTMLAnchorElement;
      const modalPhoneLink = document.getElementById(
        "modal-phone-link"
      ) as HTMLAnchorElement;
      const modalContactEmail = document.getElementById(
        "modal-contact-email"
      ) as HTMLAnchorElement;
      const modalContactChat = document.getElementById(
        "modal-contact-chat"
      ) as HTMLAnchorElement;
      const modalContactPhone = document.getElementById(
        "modal-contact-phone"
      ) as HTMLAnchorElement;
      const modalManagerPhoto = document.getElementById(
        "modal-manager-photo"
      ) as HTMLImageElement;
      const modalManagerName = document.getElementById("modal-manager-name");
      const modalAboutMe = document.getElementById("modal-about-me");
      const modalSkills = document.getElementById("modal-skills");
      const modalTeamMembers = document.getElementById("modal-team-members");
      const modalCommunities = document.getElementById("modal-communities");

      if (modalPhoto) {
        modalPhoto.src = photoUrl;
        modalPhoto.onerror = () => {
          modalPhoto.src = "/_layouts/15/images/person.gif";
        };
      }

      if (modalName) modalName.textContent = displayName;
      if (modalTitle) modalTitle.textContent = jobTitle;
      if (modalDepartment) {
        const departmentText = employee.Department || employee.SPSDepartment || "لا يوجد قسم";
        modalDepartment.textContent = departmentText;
        console.log("✅ تم تحديث القسم في الموديل:", departmentText);
      } else {
        console.warn("⚠️ لم يتم العثور على عنصر القسم في الموديل");
      }
      if (modalEmailLink) modalEmailLink.href = email ? `mailto:${email}` : "#";
      if (modalChatLink)
        modalChatLink.href = email
          ? `https://teams.microsoft.com/l/chat/0/0?users=${encodeURIComponent(
            email
          )}`
          : "#";
      if (modalPhoneLink) {
        modalPhoneLink.href = phone ? `tel:${phone}` : "#";
        const phoneSpanInLink = modalPhoneLink.querySelector("span");
        if (phoneSpanInLink) {
          phoneSpanInLink.textContent = phone ? t("Call", "اتصال") : "";
        }
      }
      if (modalContactEmail) {
        modalContactEmail.href = email ? `mailto:${email}` : "#";
        const emailSpan = modalContactEmail.querySelector("span");
        if (emailSpan)
          emailSpan.textContent = email || t("No Email", "لا يوجد إيميل");
      }
      if (modalContactChat) {
        modalContactChat.href = email
          ? `https://teams.microsoft.com/l/chat/0/0?users=${encodeURIComponent(
            email
          )}`
          : "#";
      }
      if (modalContactPhone) {
        modalContactPhone.href = phone ? `tel:${phone}` : "#";
        const phoneSpan = modalContactPhone.querySelector("span");
        if (phoneSpan) phoneSpan.textContent = phone || "";
      }
      if (modalManagerPhoto) {
        modalManagerPhoto.src = managerPhotoUrl;
        modalManagerPhoto.onerror = () => {
          modalManagerPhoto.src = "/_layouts/15/images/person.gif";
        };
      }
      if (modalManagerName) modalManagerName.textContent = managerName;
      if (modalAboutMe) {
        modalAboutMe.textContent =
          employee.AboutMe ||
          t("No about me information available", "لا توجد معلومات نبذة عني");
      }
      if (modalSkills) {
        const skills = employee.SPSSkills
          ? employee.SPSSkills.split(";").filter((skill) => skill.trim())
          : [];
        modalSkills.innerHTML =
          skills.length > 0
            ? skills.map((skill) => `<li>${skill.trim()}</li>`).join("")
            : `<li>${t("No skills listed", "لا توجد مهارات مدرجة")}</li>`;
      }
      if (modalTeamMembers) {
        modalTeamMembers.innerHTML =
          colleagues.length > 0
            ? colleagues
              .map(
                (colleague) => `
                                <div class="col-md-4 d-flex align-items-center mb-3">
                                    <img src="${colleague.photoUrl}"
                                        alt="${colleague.name}"
                                        class="rounded-circle me-2"
                                        style="width:40px; height:40px; object-fit: cover;"
                                        onerror="this.src='/_layouts/15/images/person.gif';"
                                         data-colleague-email="${colleague.email}"
                                        >
                                    <span class="head-color">${colleague.name}</span>
                                </div>
                            `
              )
              .join("")
            : `<p class="text-muted">${t(
              "No colleagues in this department",
              "لا يوجد زملاء في هذا القسم"
            )}</p>`;
      }
      if (modalCommunities) {
        modalCommunities.innerHTML = communitiesHtml; // Update communities section

        // إضافة event listeners لأزرار الانضمام للمجتمعات
        const joinButtons = modalCommunities.querySelectorAll(".join-group-btn");
        joinButtons.forEach((button) => {
          button.addEventListener("click", async (event) => {
            event.preventDefault();
            event.stopPropagation();

            const groupId = (event.currentTarget as HTMLElement).getAttribute("data-group-id");
            if (groupId) {
              const currentUserEmail = (this as any).context.pageContext.user.email;
              const success = await this.joinGroup(groupId, currentUserEmail);
              if (success) {
                const isPrivate = employee.Communities?.find((c) => c.id === groupId)?.visibility === "Private";
                const t = (en: string, ar: string) => (this._isArabic ? ar : en);

                // عرض رسالة نجاح
                if ((window as any).Swal) {
                  (window as any).Swal.fire({
                    title: t("Success", "نجاح"),
                    text: isPrivate
                      ? t("Membership request submitted successfully!", "تم تقديم طلب الانضمام بنجاح!")
                      : t("Successfully joined the group!", "تم الانضمام إلى المجتمع بنجاح!"),
                    icon: "success",
                    confirmButtonText: t("OK", "موافق"),
                    confirmButtonColor: "#3085d6",
                  });
                }
              }
            }
          });
        });
      }
      // إضافة Event Listeners للمدير
      if (modalManagerPhoto && managerEmail) {
        modalManagerPhoto.addEventListener("click", () => {
          this.openProfile(managerEmail);
        });
      }

      // إضافة Event Listeners للزملاء
      const colleagueElements = modalTeamMembers?.querySelectorAll("[data-colleague-email]");
      if (colleagueElements) {
        colleagueElements.forEach((element) => {
          const colleagueEmail = element.getAttribute("data-colleague-email");
          if (colleagueEmail) {
            element.addEventListener("click", () => {
              this.openProfile(colleagueEmail);
            });
          }
        });
      }
    }

    // Final cleanup for phone numbers
    setTimeout(() => {
      const finalModal = document.querySelector("#detailsModal");
      if (finalModal) {
        const allPhoneSpans = finalModal.querySelectorAll(
          'a[href^="tel:"] span, [data-phone] span'
        );
        allPhoneSpans.forEach((span) => {
          const currentText = span.textContent;
          if (
            currentText &&
            (currentText.includes("+") ||
              currentText.match(/\d/) ||
              currentText.includes("Call") ||
              currentText.includes("اتصال"))
          ) {
            span.textContent = phone ? t("Call", "اتصال") : "";
          }
        });
      }
    }, 100);
  }
  private async openProfile(email: string): Promise<void> {
    try {
      // جلب بيانات الموظف بناءً على الإيميل
      const employee = await this.getUserProfile(email);
      if (employee) {
        // تحديث المودال ببيانات الموظف الجديد
        await this.updateModalData(employee);
        // التأكد من إن المودال مفتوح
        const modalElement = document.querySelector("#detailsModal");
        if (modalElement) {
          const modal = new (window as any).bootstrap.Modal(modalElement);
          modal.show();
        }
        // تفعيل التاب الأول
        this.activateFirstModalTab();
      } else {
        console.error(`لم يتم العثور على بيانات الموظف للإيميل: ${email}`);
      }
    } catch (error) {
      console.error(`خطأ في فتح بروفايل الموظف ${email}:`, error);
    }
  }
  private renderSimpleTestimonialsHTML(listData: any): string {
    const t = (en: string, ar: string) => (this._isArabic ? ar : en);
    const testimonialId = listData.Id || "";
    const name = listData?.Author.Title || t("Anonymous", "مجهول");
    const photo = listData.Author.EMail
      ? `/_layouts/15/userphoto.aspx?size=M&accountname=${encodeURIComponent(
        listData.Author.EMail
      )}`
      : "/_layouts/15/images/person.gif";
    return `
          <div class="card p-3 mb-4 card-color">
            <div class="d-flex align-items-center">
              <img src="${photo}" alt="${name}" class="rounded-circle me-2" style="width:40px; height:40px;" onerror="this.src='/_layouts/15/images/person.gif';">
              <div>
                <strong class="head-color">${name}</strong>
              </div>
              <div class="ms-auto">
                <a href="/sites/intranet-qm/SitePages/Testimonials.aspx?empId=${testimonialId}" class="btn btn-link text-primary text-decoration-none d-flex align-items-center justify-content-end mt-2" data-bs-toggle="modal" data-bs-target="#detailsModal">
                  ${t(
      "View Details",
      "عرض المزيد"
    )}<i class="icon-arrow-right2 ms-2"></i>
                </a>
              </div>
            </div>
          </div>
        `;
  }

  private async getTestimonialsByEmail(email: string): Promise<any[]> {
    const url = `${(this as any).context.pageContext.web.absoluteUrl
      }/_api/web/lists/getbytitle('Testimonials')/items?$select=*,Author/Title,Author/EMail,Question1_EN/Title,Question2_EN/Title,Question3_EN/Title,Question4_EN/Title&$expand=Author,Question1_EN,Question2_EN,Question3_EN,Question4_EN&$filter=Author/EMail eq '${encodeURIComponent(
        email
      )}'`;
    try {
      const response = await (this as any).context.spHttpClient.get(
        url,
        SPHttpClient.configurations.v1
      );
      if (response.ok) {
        const data = await response.json();
        return data.value || [];
      } else {
        console.error("فشل في جلب الـ Testimonials:", response.statusText);
        return [];
      }
    } catch (error) {
      console.error("خطأ أثناء جلب الـ Testimonials:", error);
      return [];
    }
  }
  private async loadVivaCommunities(email: string): Promise<
    {
      id: string;
      name: string;
      description: string;
      visibility: string;
      mail: string;
      photoUrl: string;
    }[]
  > {
    const url = `https://graph.microsoft.com/v1.0/users/${encodeURIComponent(
      email
    )}/memberOf`;
    try {
      const client = await (this as any).context.aadHttpClientFactory.getClient(
        "https://graph.microsoft.com"
      );
      const response = await client.get(url, AadHttpClient.configurations.v1);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `Graph API error for ${email}:`,
          response.status,
          errorText
        );
        return [];
      }

      const json = await response.json();

      const communities = (json.value || [])
        .filter(
          (group: any) =>
            group["@odata.type"] === "#microsoft.graph.group" &&
            group.groupTypes?.includes("Unified") &&
            group.creationOptions?.includes("YammerProvisioning")
        )
        .map(async (group: any) => {
          // جلب صورة الجروب
          let photoUrl =
            "https://img.freepik.com/premium-vector/login-business-illustration_736766-14.jpg"; // صورة افتراضية
          try {
            const photoResponse = await client.get(
              `https://graph.microsoft.com/v1.0/groups/${group.id}/photo/$value`,
              AadHttpClient.configurations.v1
            );
            if (photoResponse.ok) {
              const photoBlob = await photoResponse.blob();
              photoUrl = URL.createObjectURL(photoBlob); // تحويل البيانات إلى URL
            }
          } catch (error) {
            console.warn(
              `No photo available for group ${group.displayName}:`,
              error
            );
          }

          return {
            id: group.id,
            name: group.displayName || "Unnamed Community",
            description: group.description || "",
            visibility: group.visibility || "Private",
            mail: group.mail || "",
            photoUrl: photoUrl,
          };
        });

      // انتظار كل الوعود (Promises) لتحميل الصور
      const resolvedCommunities = await Promise.all(communities);
      return resolvedCommunities;
    } catch (error) {
      console.error(`Error loading Viva Communities for ${email}:`, error);
      return [];
    }
  }
  private async joinGroup(
    groupId: string,
    userEmail: string
  ): Promise<boolean> {
    try {
      const client = await (this as any).context.aadHttpClientFactory.getClient(
        "https://graph.microsoft.com"
      );

      // جلب معرف اليوزر (userId)
      const userIdUrl = `https://graph.microsoft.com/v1.0/users/${encodeURIComponent(
        userEmail
      )}`;
      const userResponse = await client.get(
        userIdUrl,
        AadHttpClient.configurations.v1
      );

      if (!userResponse.ok) {
        console.error(
          `Failed to get user ID for ${userEmail}:`,
          await userResponse.text()
        );
        return false;
      }

      const user = await userResponse.json();
      const userId = user.id;

      // التحقق من نوع الجروب
      const groupUrl = `https://graph.microsoft.com/v1.0/groups/${groupId}`;
      const groupResponse = await client.get(
        groupUrl,
        AadHttpClient.configurations.v1
      );

      if (!groupResponse.ok) {
        console.error(
          `Failed to get group details for ${groupId}:`,
          await groupResponse.text()
        );
        return false;
      }

      const group = await groupResponse.json();
      const isPrivate = group.visibility === "Private";

      if (isPrivate) {
        // لو الجروب خاص، قدم طلب انضمام
        const requestUrl = `https://graph.microsoft.com/v1.0/groups/${groupId}/requests`;
        const payload = {
          "@odata.type": "#microsoft.graph.groupMembershipRequest",
          userId: userId,
          groupId: groupId,
        };

        const requestResponse = await client.post(
          requestUrl,
          AadHttpClient.configurations.v1,
          {
            body: JSON.stringify(payload),
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (requestResponse.ok) {

          return true;
        } else {
          console.error(
            `Failed to submit membership request for group ${groupId}:`,
            await requestResponse.text()
          );
          return false;
        }
      } else {
        // لو الجروب عام، أضف اليوزر مباشرة
        const joinUrl = `https://graph.microsoft.com/v1.0/groups/${groupId}/members/$ref`;
        const payload = {
          "@odata.id": `https://graph.microsoft.com/v1.0/users/${userId}`,
        };

        const joinResponse = await client.post(
          joinUrl,
          AadHttpClient.configurations.v1,
          {
            body: JSON.stringify(payload),
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (joinResponse.ok) {
          return true;
        } else {
          console.error(
            `Failed to join group ${groupId}:`,
            await joinResponse.text()
          );
          return false;
        }
      }
    } catch (error) {
      console.error(`Error joining group ${groupId} for ${userEmail}:`, error);
      return false;
    }
  }
  private async getCurrentUserDepartment(): Promise<string | undefined> {
    try {
      const email = (this as any).context.pageContext.user.email;
      const profile = await this.getUserProfile(email);
      return profile.Department || profile.SPSDepartment || undefined;
    } catch (error) {
      console.error("Error fetching current user's department:", error);
      return undefined;
    }
  }
  private extractJobTitles(): string[] {
    const jobTitles = new Set<string>();
    this._employees.forEach((employee) => {
      if (employee.SPSJobTitle && employee.SPSJobTitle.trim() !== "") {
        jobTitles.add(employee.SPSJobTitle.trim());
      }
    });
    return Array.from(jobTitles).sort();
  }
}
