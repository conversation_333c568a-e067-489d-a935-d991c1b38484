import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';

export interface INewsTickerWebPartProps {
  description: string;
}

export interface INewsItem {
  Id: number;
  Title: string;
  TitleAR: string;
  Date: string;
  label: string;
  labelAR: string;
  source: string;
  sourceAR: string;
  StartDate: string;
  EndDate: string;
  Active: boolean;
  Order: number;
  viewCount: number;
  Link_EN: { Url: string } | null;
  Link_AR: { Url: string } | null;
}

export default class NewsTickerWebPart extends BaseClientSideWebPart<INewsTickerWebPartProps> {
  private _isArabic: boolean = false;

  public async render(): Promise<void> {
    try {
      this._isArabic = window.location.pathname.toLowerCase().includes('/ar/');
      const today = new Date().toISOString().split('T')[0];

      const listUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getByTitle('NewsTicker')/items`;
      const query = `$select=*&$filter=Active eq 1 and EndDate ge '${today}'&$orderby=Order asc`;

      const response: SPHttpClientResponse = await this.context.spHttpClient.get(
        `${listUrl}?${query}`,
        SPHttpClient.configurations.v1
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch items: ${response.statusText} (${response.status})`);
      }

      const data = await response.json();
      const items: INewsItem[] = data.value;

      if (!items || items.length === 0) {
        this._renderError('No news items available.');
        return;
      }

      const itemsHtml = items.map(item => {
        const date = item.Date ? new Date(item.Date) : null;
        const title = this._isArabic ? item.TitleAR || item.Title : item.Title;
        const link = item[this._isArabic ? 'Link_AR' : 'Link_EN']?.Url ?? '';

        return `
          <div class="ticker-item">
            <span class="ticker-date text-color">
              ${date && !isNaN(date.getTime()) ? date.toLocaleDateString(this._isArabic ? 'ar-SA' : 'en-US', { year: 'numeric', month: 'short', day: 'numeric' }) : ''}
            </span>
            <span class="ticker-text">
              ${link
            ? `<button type="button" class="ticker-title-link main-color btn" data-item-id="${item.Id}" data-url="${link}">${title}</button>`
            : `<span class="ticker-title-text">${title}</span>`}
            </span>
          </div>`;
      }).join('');

      this.domElement.innerHTML = `
      <style>
        .ticker-track {
          overflow: hidden;
          position: relative;
          width: 100%;
        }
        .ticker-track-inner {
          display: flex;
          white-space: nowrap;
          width: max-content;
          animation: scrollTicker 30s linear infinite;
        }
        @keyframes scrollTicker {
          0% { transform: translateX(0%); }
          100% { transform: translateX(-50%); }
        }
      </style>
      <section class="breack-newas" aria-live="polite">
        <div class="ticker-wrapper">
          <div class="news-ticker">
            <div class="ticker-track">
              <div class="ticker-track-inner">
                ${itemsHtml}
              </div>
            </div>
          </div>
        </div>
      </section>
      `;

      const wrapper = this.domElement.querySelector('.ticker-wrapper') as HTMLElement;
      const track = this.domElement.querySelector('.ticker-track') as HTMLElement;

      if (!wrapper || !track) {
        this._renderError('Failed to initialize ticker elements.');
        return;
      }

      this._addClickEventListeners(items);
      this._autoDuplicateAndAnimate();

    } catch (error) {
      console.error('Error in NewsTicker:', error);
      this._renderError('Error loading news items.');
    }
  }

  private _renderError(message: string): void {
    this.domElement.innerHTML = `
      <section class="news-ticker-section">
        <div class="ticker-wrapper">
          <div class="news-ticker">
            <div class="ticker-track">
              <div class="ticker-track-inner">
                <div class="ticker-item">${message}</div>
              </div>
            </div>
          </div>
        </div>
      </section>`;
  }

  private async _updateViewCount(itemId: number): Promise<void> {
    try {
      const getUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getByTitle('NewsTicker')/items(${itemId})?$select=viewCount`;

      const getResponse = await this.context.spHttpClient.get(getUrl, SPHttpClient.configurations.v1);
      if (!getResponse.ok) {
        throw new Error(`Failed to get current view count: ${getResponse.statusText}`);
      }

      const currentItem = await getResponse.json();
      const newViewCount = (currentItem.viewCount || 0) + 1;

      const updateUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getByTitle('NewsTicker')/items(${itemId})`;

      const updateResponse = await this.context.spHttpClient.post(updateUrl, SPHttpClient.configurations.v1, {
        headers: {
          'Accept': 'application/json;odata=nometadata',
          'Content-Type': 'application/json;odata=nometadata',
          'odata-version': '',
          'IF-MATCH': '*',
          'X-HTTP-Method': 'MERGE',
        },
        body: JSON.stringify({ viewCount: newViewCount }),
      });

      if (!updateResponse.ok) {
        throw new Error(`Failed to update view count: ${updateResponse.statusText} (${updateResponse.status})`);
      }
    } catch (error) {
      console.error('Error updating view count:', error);
    }
  }

  private _addClickEventListeners(items: INewsItem[]): void {
    const track = this.domElement.querySelector('.ticker-track');
    if (!track) return;

    track.addEventListener('click', async (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      if (target.tagName.toLowerCase() === 'button' && target.classList.contains('ticker-title-link')) {
        event.preventDefault();
        event.stopPropagation();

        const itemIdStr = target.getAttribute('data-item-id');
        const url = target.getAttribute('data-url');

        if (!itemIdStr || !url) return;

        const itemId = parseInt(itemIdStr, 10);
        if (isNaN(itemId)) return;

        try {
          await this._updateViewCount(itemId);
          window.open(url, '_blank');
        } catch (error) {
          console.warn('View count update failed. Opening link anyway.');
          window.open(url, '_blank');
        }
      }
    });
  }

  private _autoDuplicateAndAnimate(): void {
    const trackInner = this.domElement.querySelector('.ticker-track-inner') as HTMLElement;
    const wrapper = this.domElement.querySelector('.ticker-wrapper') as HTMLElement;

    if (!trackInner || !wrapper) return;

    // ✅ Duplicate once only
    if (!trackInner.dataset.duplicated) {
      trackInner.innerHTML += trackInner.innerHTML;
      trackInner.dataset.duplicated = 'true';
    }

    // ✅ Restart animation
    trackInner.style.animation = 'none';
    void trackInner.offsetWidth;
    trackInner.style.animation = '';

    wrapper.addEventListener('mouseenter', () => {
      trackInner.style.animationPlayState = 'paused';
    });

    wrapper.addEventListener('mouseleave', () => {
      trackInner.style.animationPlayState = 'running';
    });
  }
}
