import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';

export interface IVacancayDetailsWebPartProps {
  description: string;
}

export interface IBreadcrumbItem {
  title: string;
  url?: string;
  isCurrent?: boolean;
}

export default class VacancayDetailsWebPart extends BaseClientSideWebPart<IVacancayDetailsWebPartProps> {

  private _isArabic: boolean = false;

  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;
    const urlParams = new URLSearchParams(window.location.search);
    const viewID: any = urlParams.get('viewID');
    const referrer = document.referrer;
    const isArabic = window.location.pathname.includes('/ar/');
    const backText = isArabic ? 'رجوع' : 'Back';
    // const breadcrumbItems = this.generateDynamicBreadcrumb();
    // const breadcrumbHtml = this.generateBreadcrumbHtml(breadcrumbItems);
    const shareText = isArabic ? 'مشاركة' : 'Share';


    if (referrer) {
      const refUrl = new URL(referrer);
      const serverRelative = refUrl.pathname;

      this.getPageTitle(serverRelative).then(title => {
        this.domElement.querySelector('#referrerName')!.textContent = title;
      });
    }
    this.domElement.innerHTML = `
    <style>
      .tab-section { display: none; }
      .tab-section.active { display: block; }
      .tab-btn.active {
        font-weight: bold;
        background-color: #f0f0f0;
      }
    </style>
    <main id="main-content" class="vacancy-details-page">
     <section class="bread-cramp mt-3 mb-3">
        <div class="card card-color">
          <div class="card-body d-flex justify-content-between align-items-center flex-wrap">
            <nav aria-label="breadcrumb">
              <ol class="breadcrumb breadcrumb-container m-0">
               <li class="breadcrumb-item">
                <a href="/sites/intranet-qm/SitePages/TopicHome.aspx" class="btn-main-link fw-bold">Home</a>
               </li>
               <li class="breadcrumb-item">
                <a href="/sites/intranet-qm/SitePages/internal-vacancies.aspx" class="btn-main-link fw-bold">Internal Career Opportunity</a>
               </li>
               <li class="breadcrumb-item text-color fw-semibold" aria-current="page">Executive Secretary</li>
              </ol>
            </nav>
            <div class="d-flex align-items-center gap-3">
              


            <button class="btn btn-outline-dark d-flex align-items-center gap-1" onclick="history.back()">
              <i class="bi bi-arrow-left"></i> ${backText}
            </button>
            

            </div>
          </div>
        </div>
      </section>
      <div class="container mt-4">
      <div class="row">
        <div class="col-lg-3">
          <div class="card card-color mb-4">
            <div class="card-body p-0">
              <div class="nav nav-pills flex-column" id="custom-tab-list">
                <button class="nav-link text-start tab-btn active" data-tab="department">Department</button>
                <button class="nav-link text-start tab-btn" data-tab="role-purpose">Role Purpose</button>
                <button class="nav-link text-start tab-btn" data-tab="duties">Duties & Responsibilities</button>
                <button class="nav-link text-start tab-btn" data-tab="work-environment">Work Environment</button>
                <button class="nav-link text-start tab-btn" data-tab="qualifications">Qualifications/Requirements</button>
                <button class="nav-link text-start tab-btn" data-tab="competencies">Competencies/Skills</button>
              </div>
            </div>
          </div>
        </div>

        <div class="col-lg-9">
          <div class="card card-color mb-4">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center mb-4 border-bottom pb-3">
                <h2 class="m-0 main-color" id="tab-title">Department</h2>
                <div class="d-flex align-items-center">
                  <div class="me-3 d-flex align-items-center">
                    <i class="bi bi-calendar3 me-1"></i>
                    <span class="text-color" id="publish-date">Loading...</span>
                  </div>
                  <div class="dropdown ms-auto">
                        <a class="text-decoration-none text-color d-flex align-items-center" href="#" id="shareDropdown"
                            role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-share main-color me-1"></i> ${shareText}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="shareDropdown">
                            <li>
                                <a class="dropdown-item d-flex align-items-center gap-2" href="#" id="sendEmail">
                                    <i class="bi bi-send main-color"></i> Send
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center gap-2" href="#" id="copyLink">
                                    <i class="bi bi-clipboard main-color"></i> Copy Link
                                </a>
                                </li>
                        </ul>
                    </div>
                </div>
              </div>

              <div class="tab-content" id="custom-tab-content">
                <div class="tab-section active" id="department">
                  <p class="text-color" id="department-name">Loading...</p>
                  <p class="text-color" id="Report-to">Loading...</p>
                </div>
                <div class="tab-section" id="role-purpose">
                  <p class="text-color" id="role-purpose">Loading...</p>
                </div>
                <div class="tab-section" id="duties">
                  <ul class="text-color ps-3" id="duties-list">
                    <li class="mb-2">Loading...</li>
                  </ul>
                </div>
                <div class="tab-section" id="work-environment">
                  <p class="text-color" id="work-environment">Loading...</p>
                </div>
                <div class="tab-section" id="qualifications">
                  <ul class="text-color ps-3" id="qualifications-list">
                    <li class="mb-2">Loading...</li>
                  </ul>
                </div>
                <div class="tab-section" id="competencies">
                  <ul class="text-color ps-3" id="competencies-list">
                    <li class="mb-2">Loading...</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-12">
          <div class="card card-color d-flex justify-content-end">
            <div class="text-end m-3">
              <button type="button" class="btn btn-main text-white px-4 py-2">Apply</button>
            </div>
          </div>
        </div>
      </div>
      </div>
    </main>
    `;

    // Custom Tab Behavior
    const tabButtons = this.domElement.querySelectorAll('.tab-btn');
    const tabSections = this.domElement.querySelectorAll('.tab-section');
    const tabTitle = this.domElement.querySelector('#tab-title');

    tabButtons.forEach((btn: HTMLElement) => {
      btn.addEventListener('click', () => {
        tabButtons.forEach(btn => btn.classList.remove('active'));
        tabSections.forEach(section => section.classList.remove('active'));

        btn.classList.add('active');
        const targetId = btn.getAttribute('data-tab');
        const targetSection = this.domElement.querySelector(`#${targetId}`);
        targetSection?.classList.add('active');

        if (tabTitle) {
          tabTitle.textContent = btn.textContent?.trim() || 'Tab';
        }
      });
    });
    this.fetchVacancyData(viewID);
    this.addEventListeners();
  }

  private fetchVacancyData(viewID: string): void {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getByTitle('CommINT')/items(${viewID})?$select=*,ReportTo/Title,ReportTo/Department,Department_EN/Title,Department__x0650_AR/Title&$expand=ReportTo,Department_EN,Department__x0650_AR`;

    this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then((item) => {
        const isArabic = this._isArabic;

        const publishDate = document.getElementById('publish-date');
        const departmentName = document.getElementById('department-name');
        const reportTo = document.getElementById('Report-to');
        const rolePurpose = document.getElementById('role-purpose');
        const workEnvironment = document.getElementById('work-environment');
        const dutiesList = document.getElementById('duties-list');
        const qualificationsList = document.getElementById('qualifications-list');
        const competenciesList = document.getElementById('competencies-list');

        if (publishDate) publishDate.textContent = item.PublishingDate ? new Date(item.PublishingDate).toLocaleDateString() : '';

        if (departmentName) departmentName.textContent = isArabic ? item.Department_AR?.Title || '' : item.Department_EN?.Title || '';

        if (reportTo) {
          const title = item.ReportTo?.Title || '';
          const department = item.ReportTo?.Department || '';
          reportTo.textContent = department ? `${title} - ${department}` : title;
        }

        if (rolePurpose) {
          const rawHtml = isArabic ? item.RolePurpose_AR || '' : item.RolePurpose_EN || '';
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = rawHtml;
          rolePurpose.textContent = tempDiv.textContent?.trim() || '';
        }

        if (workEnvironment) {
          const rawHtml = isArabic ? item.WorkEnvironment_AR || '' : item.WorkEnvironment_EN || '';
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = rawHtml;
          workEnvironment.textContent = tempDiv.textContent?.trim() || '';
        }

        if (dutiesList) {
          const rawHtml = isArabic ? item.Responsibilities_AR : item.Responsibilities_EN;
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = rawHtml || '';
          const plainText = tempDiv.textContent || '';
          const dutiesArray = plainText.split(';').map(d => d.trim()).filter(d => d);
          dutiesList.innerHTML = dutiesArray.map(d => `<li class="mb-2">${d}</li>`).join('');
        }

        if (qualificationsList) {
          const qualificationsRaw = isArabic ? item.QualificationsandRequirements_AR : item.QualificationsandRequirements_EN;
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = qualificationsRaw || '';
          const plainText = tempDiv.textContent || '';
          const qualificationsArray = plainText.split(';').map(q => q.trim()).filter(q => q);
          qualificationsList.innerHTML = qualificationsArray.map(q => `<li class="mb-2">${q}</li>`).join('');
        }

        if (competenciesList) {
          const competenciesRaw = isArabic ? item.CompetenciesandSkills_AR : item.CompetenciesandSkills_EN;
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = competenciesRaw || '';
          const plainText = tempDiv.textContent || '';
          const competenciesArray = plainText.split(';').map(c => c.trim()).filter(c => c);
          competenciesList.innerHTML = competenciesArray.map(c => `<li class="mb-2">${c}</li>`).join('');
        }
      })
      .catch((error) => {
        console.error('Error fetching vacancy data:', error);
      });
  }

  addEventListeners(): void {
    const sendEmailBtn = this.domElement.querySelector('#sendEmail');
    const copyLinkBtn = this.domElement.querySelector('#copyLink');
  
    const isArabic = this._isArabic;
    const copySuccessMsg = isArabic ? 'تم نسخ الرابط بنجاح' : 'Link copied successfully';
  
    if (sendEmailBtn) {
      sendEmailBtn.addEventListener('click', () => this.sendEmail(window.location.href));
    }
  
    if (copyLinkBtn) {
      copyLinkBtn.addEventListener('click', () => {
        this.copyLink(window.location.href, copySuccessMsg);
      });
    }
  }
  
  copyLink(url: string, successMessage: string): void {
    navigator.clipboard.writeText(url).then(() => {
      alert(successMessage);
    }).catch((error) => {
      console.error('Error copying link:', error);
    });
  }
  sendEmail(url: string): void {
    const subject = "Check this out!";
    const body = `Here is the link to the news article: ${url}`;
    window.location.href = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  }


 
  async getPageTitle(serverRelativeUrl: string): Promise<string> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/getfilebyserverrelativeurl('${serverRelativeUrl}')/ListItemAllFields?$select=Title`;

    const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
    const data = await response.json();

    return data?.Title || '';
  }
  generateDynamicBreadcrumb(): IBreadcrumbItem[] {
    const path = window.location.pathname;
    const segments = path.split('/').filter(seg => seg); // Remove empty strings
    const breadcrumbItems: IBreadcrumbItem[] = [];

    let accumulatedUrl = '';

    segments.forEach((segment, index) => {
      accumulatedUrl += `/${segment}`;

      const skipList = ['sitepages', 'pages', 'sites'];
      if (skipList.indexOf(segment.toLowerCase()) !== -1) return;


      const formattedTitle = decodeURIComponent(segment)
        .replace(/[-_]/g, ' ')
        .replace(/\.aspx/i, '')
        .replace(/\b\w/g, c => c.toUpperCase());

      if (index === segments.length - 1) {
        breadcrumbItems.push({
          title: formattedTitle,
          isCurrent: true
        });
      } else {
        breadcrumbItems.push({
          title: formattedTitle,
          url: accumulatedUrl
        });
      }
    });

    // Add home link at the beginning
    breadcrumbItems.unshift({
      title: this._isArabic ? 'الرئيسية' : 'Home',
      url: '/'
    });

    return breadcrumbItems;
  }
  generateBreadcrumbHtml(items: IBreadcrumbItem[]): string {
    return `
        <ol class="breadcrumb m-0">
          ${items
        .map(item => {
          if (item.isCurrent) {
            return `<li class="breadcrumb-item active" aria-current="page">${item.title}</li>`;
          } else {
            return `<li class="breadcrumb-item"><a href="${item.url}" class="btn-main-link fw-bold">${item.title}</a></li>`;
          }
        })
        .join('')}
        </ol>
      `;
  }
}
