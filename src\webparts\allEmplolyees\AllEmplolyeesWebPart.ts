import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient } from '@microsoft/sp-http';
import { Version } from '@microsoft/sp-core-library';
import {
  type IPropertyPaneConfiguration,
  PropertyPaneDropdown,
  PropertyPaneTextField
} from '@microsoft/sp-property-pane';

export interface IAllEmployeesWebPartProps {
  description: string;
  selectedDepartment: string;
  titleEnglish: string;
  titleArabic: string;
}

interface IEmployee {
  Id: number;
  Title: string;
  Department: string;
  JobTitle: string;
  Email: string;
  Phone?: string;
  ProfilePicture?: string;
  AttachmentFiles?: any[];
}

export default class AllEmployeesWebPart extends BaseClientSideWebPart<IAllEmployeesWebPartProps> {
  private _employees: IEmployee[] = [];
  private _filteredEmployees: IEmployee[] = [];
  private _isArabic: boolean = false;
  private _isLoading: boolean = false;
  private _searchTerm: string = '';
  private _currentPage: number = 1;
  private _itemsPerPage: number = 10;
  private _availableDepartments: Array<{key: string, text: string}> = [];

  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().includes('/ar/');

    const loadingText = this._isArabic ? 'جاري التحميل...' : 'Loading...';
    const noEmployeesText = this._isArabic ? 'لا يوجد موظفين' : 'No employees found';
    const errorText = this._isArabic ? 'حدث خطأ في تحميل البيانات' : 'Error loading data';
    const searchPlaceholder = this._isArabic ? 'البحث بالاسم...' : 'Search by name...';
    // استخدام العناوين من الإعدادات أو القيم الافتراضية
    const defaultTitleArabic = 'كل الموظفين';
    const defaultTitleEnglish = 'All Employees';

    const displayTitle = this._isArabic ?
      (this.properties.titleArabic || defaultTitleArabic) :
      (this.properties.titleEnglish || defaultTitleEnglish);

    this.domElement.innerHTML = `
      <section class="card department-employees bg-remover">
        <div class="container-fluid">
          <h2 class="fw-bold head-color my-3 tittle">${displayTitle}</h2>

          <!-- Search Bar -->
          <div class="search-container mb-4">
            <div class="row">
              <div class="col-md-12">
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="bi bi-search"></i>
                  </span>
                  <input type="text"
                         class="form-control"
                         id="employeeSearch"
                         placeholder="${searchPlaceholder}"
                         value="${this._searchTerm}">
                </div>
              </div>
              
            </div>
          </div>

          <div class="employees-container">
            ${this._isLoading ? `
              <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">${loadingText}</span>
                </div>
                <p class="mt-3">${loadingText}</p>
              </div>
            ` : this._renderEmployees(noEmployeesText, errorText)}
          </div>
        </div>
      </section>
    `;

    // إضافة event listeners
    this._attachEventListeners();

    // تحميل الصور بعد العرض إذا كانت البيانات موجودة
    if (!this._isLoading && this._filteredEmployees && this._filteredEmployees.length > 0) {
      setTimeout(() => this._loadEmployeeImages(), 100);
    }
  }

  protected async onInit(): Promise<void> {
    await super.onInit();
    // تحميل الأقسام المتاحة أولاً
    await this._loadAvailableDepartments();
    // تحميل الموظفين
    await this._loadEmployees();
    this.render();
  }

  private _isDepartmentMatch(userDepartment: string, targetDepartment: string): boolean {
    if (!userDepartment || !targetDepartment) {
      return false;
    }

    const userDept = userDepartment.toLowerCase().trim();
    const targetDept = targetDepartment.toLowerCase().trim();

    // مطابقة مباشرة
    if (userDept === targetDept) {
      return true;
    }

    // مطابقة جزئية - البحث عن الكلمات المفتاحية
    const departmentKeywords: { [key: string]: string[] } = {
      'it': ['it', 'information technology', 'تقنية المعلومات', 'tech', 'technology', 'information'],
      'hr': ['hr', 'human resources', 'الموارد البشرية', 'human', 'resources', 'personnel'],
      'finance': ['finance', 'financial', 'accounting', 'المالية', 'المحاسبة', 'مالية', 'محاسبة'],
      'marketing': ['marketing', 'communications', 'التسويق', 'الاتصالات', 'تسويق', 'اتصالات'],
      'administration': ['admin', 'administration', 'الإدارة', 'إدارة', 'administrative'],
      'legal': ['legal', 'القانونية', 'قانونية', 'law'],
      'security': ['security', 'الأمن', 'أمن'],
      'facilities': ['facilities', 'المرافق', 'مرافق'],
      'operations': ['operations', 'العمليات', 'عمليات']
    };

    // البحث في الكلمات المفتاحية
    const keywords = departmentKeywords[targetDept] || [targetDept];

    for (const keyword of keywords) {
      if (userDept.includes(keyword.toLowerCase())) {
        return true;
      }
    }

    // مطابقة عكسية - البحث عن الكلمة المستهدفة في قسم المستخدم
    if (userDept.includes(targetDept)) {
      return true;
    }

    return false;
  }

  private _getDepartmentKeywords(department: string): string[] {
    const departmentKeywords: { [key: string]: string[] } = {
      'it': ['it', 'tech', 'technology', 'information'],
      'hr': ['hr', 'human', 'resources', 'personnel'],
      'finance': ['finance', 'financial', 'accounting'],
      'marketing': ['marketing', 'communications'],
      'administration': ['admin', 'administration', 'administrative'],
      'legal': ['legal', 'law'],
      'security': ['security'],
      'facilities': ['facilities'],
      'operations': ['operations']
    };

    return departmentKeywords[department.toLowerCase()] || [department.toLowerCase()];
  }

  private async _loadAvailableDepartments(): Promise<void> {
    try {
      const siteUrl = this.context.pageContext.site.absoluteUrl;
      const usersUrl = `${siteUrl}/_api/web/siteusers?$select=*&$filter=PrincipalType eq 1`;

      const response = await this.context.spHttpClient.get(usersUrl, SPHttpClient.configurations.v1);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const siteUsers = data.value || [];

      // فلترة المستخدمين (استبعاد system accounts)
      const filteredUsers = siteUsers.filter((user: any) =>
        user.Email &&
        !user.LoginName.includes('sharepoint') &&
        !user.LoginName.includes('system') &&
        !user.Title.includes('System') &&
        user.Title !== 'Everyone' &&
        user.Title !== 'NT AUTHORITY\\authenticated users'
      );

      const departmentsSet = new Set<string>();

      // جلب الأقسام من User Profiles
      for (const user of filteredUsers) {
        try {
          const profileUrl = `${this.context.pageContext.site.absoluteUrl}/_api/SP.UserProfiles.PeopleManager/GetPropertiesFor(accountName=@v)?@v='${encodeURIComponent(user.LoginName)}'`;
          const profileResponse = await this.context.spHttpClient.get(profileUrl, SPHttpClient.configurations.v1);

          if (profileResponse.ok) {
            const profileData = await profileResponse.json();

            if (profileData.UserProfileProperties) {
              const properties = profileData.UserProfileProperties;
              const deptProp = properties.find((p: any) => p.Key === 'Department');

              if (deptProp && deptProp.Value && deptProp.Value.trim() !== '') {
                departmentsSet.add(deptProp.Value.trim());
              }
            }
          }
        } catch (profileError) {
          // تجاهل الأخطاء والمتابعة
        }
      }

      // تحويل Set إلى Array مع الترجمة
      const departments = Array.from(departmentsSet).map(dept => ({
        key: dept,
        text: dept
      }));

      // إضافة أقسام افتراضية إذا لم نجد أي أقسام
      if (departments.length === 0) {
        this._availableDepartments = [
          { key: 'IT', text: this._isArabic ? 'تقنية المعلومات' : 'Information Technology' },
          { key: 'HR', text: this._isArabic ? 'الموارد البشرية' : 'Human Resources' },
          { key: 'Finance', text: this._isArabic ? 'المالية' : 'Finance' },
          { key: 'Marketing', text: this._isArabic ? 'التسويق' : 'Marketing' },
          { key: 'Administration', text: this._isArabic ? 'الإدارة' : 'Administration' }
        ];
      } else {
        this._availableDepartments = departments.sort((a, b) => a.text.localeCompare(b.text));
      }

    } catch (error) {
      console.error('Error loading departments:', error);
      // استخدام أقسام افتراضية في حالة الخطأ
      this._availableDepartments = [
        { key: 'IT', text: this._isArabic ? 'تقنية المعلومات' : 'Information Technology' },
        { key: 'HR', text: this._isArabic ? 'الموارد البشرية' : 'Human Resources' },
        { key: 'Finance', text: this._isArabic ? 'المالية' : 'Finance' },
        { key: 'Marketing', text: this._isArabic ? 'التسويق' : 'Marketing' },
        { key: 'Administration', text: this._isArabic ? 'الإدارة' : 'Administration' }
      ];
    }
  }



  private async _loadEmployees(): Promise<void> {
    try {
      this._isLoading = true;
      this.render(); // إعادة عرض الصفحة لإظهار Loading

      // جلب المستخدمين من SharePoint Site Users
      await this._loadSiteUsers();

    } catch (error) {
      console.error('Error fetching IT employees:', error);
      this._employees = [];
    } finally {
      this._isLoading = false;
    }
  }

  private async _loadSiteUsers(): Promise<void> {
    try {
      const siteUrl = this.context.pageContext.site.absoluteUrl;

      // جلب المستخدمين من الموقع
      const usersUrl = `${siteUrl}/_api/web/siteusers?$select=*&$filter=PrincipalType eq 1`;


      const response = await this.context.spHttpClient.get(usersUrl, SPHttpClient.configurations.v1);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const siteUsers = data.value || [];


      // فلترة المستخدمين وجلب معلومات إضافية من User Profiles
      await this._enrichUsersWithProfiles(siteUsers);

    } catch (error) {
      console.error('Error fetching site users:', error);
     
    }
  }

  private async _enrichUsersWithProfiles(siteUsers: any[]): Promise<void> {
    const enrichedEmployees: IEmployee[] = [];

    // فلترة المستخدمين (استبعاد system accounts)
    const filteredUsers = siteUsers.filter(user =>
      user.Email &&
      !user.LoginName.includes('sharepoint') &&
      !user.LoginName.includes('system') &&
      !user.Title.includes('System') &&
      user.Title !== 'Everyone' &&
      user.Title !== 'NT AUTHORITY\\authenticated users'
    );


    for (const user of filteredUsers) {
      try {
        const profileUrl = `${this.context.pageContext.site.absoluteUrl}/_api/SP.UserProfiles.PeopleManager/GetPropertiesFor(accountName=@v)?@v='${encodeURIComponent(user.LoginName)}'`;

        const profileResponse = await this.context.spHttpClient.get(profileUrl, SPHttpClient.configurations.v1);

        let jobTitle = '';
        let department = '';
        let phone = '';
        let profilePicture = '';

        if (profileResponse.ok) {
          const profileData = await profileResponse.json();

          // استخراج المعلومات من User Profile Properties
          if (profileData.UserProfileProperties) {
            const properties = profileData.UserProfileProperties;

            const titleProp = properties.find((p: any) => p.Key === 'Title');
            if (titleProp) jobTitle = titleProp.Value || jobTitle;

            const deptProp = properties.find((p: any) => p.Key === 'Department');
            if (deptProp) department = deptProp.Value || '';


          }

          // محاولة جلب صورة الملف الشخصي من عدة مصادر
          if (profileData.PictureUrl && profileData.PictureUrl !== '') {
            profilePicture = profileData.PictureUrl;
          } else {
            // جلب الصورة من User Photo Service
            profilePicture = await this._getUserPhotoUrl(user.Email);
          }
        } else {
          profilePicture = await this._getUserPhotoUrl(user.Email);
        }


        // فلترة الموظفين بناءً على القسم المحدد من الإعدادات
        const selectedDepartment = this.properties.selectedDepartment || 'IT';
        if (this._isDepartmentMatch(department, selectedDepartment)) {
          const employee: IEmployee = {
            Id: user.Id,
            Title: user.Title,
            Department: department,
            JobTitle: jobTitle || 'Not specified',
            Email: user.Email,
            Phone: phone,
            ProfilePicture: profilePicture
          };

          enrichedEmployees.push(employee);
        } 

      } catch (profileError) {
        console.warn('Could not fetch profile for user:', user.Title, profileError);

        // محاولة إضافة الموظف إذا كان من القسم المحدد (حتى لو لم نتمكن من جلب معلومات إضافية)
        const selectedDepartment = this.properties.selectedDepartment || 'IT';
        const emailDomain = user.Email ? user.Email.toLowerCase() : '';
        const userName = user.Title ? user.Title.toLowerCase() : '';

        // فحص بسيط للتحقق من احتمالية كونه من القسم المحدد
        const keywords = this._getDepartmentKeywords(selectedDepartment);
        const mightBeFromDepartment = keywords.some(keyword =>
          emailDomain.includes(keyword) || userName.includes(keyword)
        );

        if (mightBeFromDepartment) {
          const employee: IEmployee = {
            Id: user.Id,
            Title: user.Title,
            Department: `${selectedDepartment} (Assumed)`,
            JobTitle: 'Not specified',
            Email: user.Email,
            Phone: '',
            ProfilePicture: ''
          };

          enrichedEmployees.push(employee);
        }
      }
    }

    this._employees = enrichedEmployees;
    this._filteredEmployees = [...enrichedEmployees]; // نسخ جميع الموظفين للفلترة
  }

 

  private async _loadEmployeeImages(): Promise<void> {

    for (const employee of this._employees) {
      try {
        const imageId = `emp-img-${employee.Id}`;
        const loadingElement = document.getElementById(`${imageId}-loading`);
        const imgElement = document.getElementById(imageId) as HTMLImageElement;
        const defaultElement = document.getElementById(`${imageId}-default`);

        if (!imgElement || !loadingElement || !defaultElement) {
          continue;
        }


        await this._tryMultipleImageUrls(employee, imgElement, loadingElement, defaultElement);

      } catch (error) {
        console.error(`Error loading image for ${employee.Title}:`, error);

        const imageId = `emp-img-${employee.Id}`;
        const loadingElement = document.getElementById(`${imageId}-loading`);
        const defaultElement = document.getElementById(`${imageId}-default`);

        if (loadingElement) loadingElement.style.display = 'none';
        if (defaultElement) defaultElement.style.display = 'flex';
      }
    }
  }

  private async _getUserPhotoUrl(email: string): Promise<string> {
    if (!email) return '';

    try {
      const siteUrl = this.context.pageContext.site.absoluteUrl;


      try {
        const profileUrl = `${siteUrl}/_api/SP.UserProfiles.PeopleManager/GetUserProfilePropertyFor(accountName=@v,propertyName='PictureURL')?@v='${encodeURIComponent(email)}'`;


        const response = await this.context.spHttpClient.get(profileUrl, SPHttpClient.configurations.v1);
        if (response.ok) {
          const data = await response.json();
          if (data.value && data.value !== '' && data.value !== 'null') {
            return data.value;
          }
        }
      } catch (restError) {
      }

      const photoServiceUrl = `${siteUrl}/_layouts/15/userphoto.aspx?size=L&username=${encodeURIComponent(email)}&t=${Date.now()}`;

      return photoServiceUrl;

    } catch (error) {
      console.warn(`Error getting photo for ${email}:`, error);

      const siteUrl = this.context.pageContext.site.absoluteUrl;
      const fallbackUrl = `${siteUrl}/_layouts/15/userphoto.aspx?size=M&username=${encodeURIComponent(email)}`;
      return fallbackUrl;
    }
  }

  private _renderEmployees(noEmployeesText: string, errorText: string): string {
    if (!this._filteredEmployees || this._filteredEmployees.length === 0) {
      return `
        <div class="text-center py-5">
          <i class="bi bi-people icon-lg  text-color "></i>
          <p class=" text-color  mt-3">${noEmployeesText}</p>
          ${errorText ? `<small class="text-danger">${errorText}</small>` : ''}
        </div>
      `;
    }

    // حساب الـ pagination
    const totalItems = this._filteredEmployees.length;
    const totalPages = Math.ceil(totalItems / this._itemsPerPage);
    const startIndex = (this._currentPage - 1) * this._itemsPerPage;
    const endIndex = startIndex + this._itemsPerPage;
    const currentPageEmployees = this._filteredEmployees.slice(startIndex, endIndex);

    return `
      <div class="employees-grid">
        <div class="row g-4">
          ${currentPageEmployees.map(employee => this._renderEmployeeCard(employee)).join('')}
        </div>

        ${totalPages > 1 ? this._renderPagination(totalPages) : ''}
      </div>
    `;
  }

  private _renderEmployeeCard(employee: IEmployee): string {
    const sendMailText = this._isArabic ? 'إرسال بريد' : 'Send Mail';
    const startChatText = this._isArabic ? 'بدء محادثة' : 'Start Chat';

    const imageId = `emp-img-${employee.Id}`;


    return `
    <div class="col-lg-4 col-md-6">
      <div class="card card-color p-4 section-card bg-remover">
          <div class="d-flex justify-content-center align-items-center mb-3">
              <div class="image-container" style="position: relative; width: 120px; height: 120px; margin: 0 auto;">
                  <div id="${imageId}-loading" class="loading-avatar"
                      style="width: 120px; height: 120px; border-radius: 50%; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center; color: #6c757d; font-size: 1rem; border: 4px solid #e9ecef;">
                      <i class="bi bi-hourglass-split"></i> Loading...
                  </div>
                  <img class="img-fluid rounded-circle" id="${imageId}"
                      style="width: 120px; height: 120px; border-radius: 50%; object-fit: cover; border: 4px solid #e9ecef; display: none; position: absolute; top: 0; left: 0;"
                      alt="${employee.Title}" />
                  <div id="${imageId}-default" class="default-avatar"
                      style="display: none; position: absolute; top: 0; left: 0; width: 120px; height: 120px; border-radius: 50%; background-color: #e9ecef; align-items: center; justify-content: center; color: #6c757d; font-size: 4rem; border: 4px solid #e9ecef;">
                      <i class="bi bi-person-circle"></i>
                  </div>
              </div>
          </div>


          <h5 class="text-center head-color font-weight-bold">${employee.Title || ''}</h5>
          <p class="text-color text-center">${employee.JobTitle || ''}</p>


          <div class="text-center">

              ${employee.Email ? `
              <a href="mailto:${employee.Email}" class="btn text-color send-mail" title="${sendMailText}">
                  <svg xmlns="http://www.w3.org/2000/svg" width="26.291" height="25.249" viewBox="0 0 26.291 25.249">
                      <g id="Message_1" data-name="Message 1" transform="translate(0 0)">
                          <path id="Stroke_1" data-name="Stroke 1"
                              d="M7.366,6.1c-1.484,0-3.279-.909-5.335-2.7A26.173,26.173,0,0,1-.524.828.96.96,0,1,1,.944-.409C2.44,1.365,5.508,4.18,7.366,4.18S12.261,1.368,13.74-.4A.96.96,0,0,1,15.215.824,25.818,25.818,0,0,1,12.682,3.4C10.64,5.189,8.852,6.1,7.366,6.1Z"
                              transform="translate(5.794 8.641)" fill="var(--main-color)" />
                          <path id="Stroke_3" data-name="Stroke 3"
                              d="M12.4-.75c5.007,0,8.148.869,10.184,2.818s2.962,4.985,2.962,9.807-.913,7.845-2.962,9.806S17.4,24.5,12.4,24.5,4.248,23.63,2.212,21.681-.75,16.7-.75,11.875.163,4.029,2.212,2.068,7.388-.75,12.4-.75Zm0,23.33c8.6,0,11.226-2.5,11.226-10.705S21,1.169,12.4,1.169,1.169,3.671,1.169,11.875,3.792,22.58,12.4,22.58Z"
                              transform="translate(0.75 0.75)" fill="var(--main-color)" />
                      </g>
                  </svg>
                  <span>${sendMailText}</span>
              </a>
              ` : ''}

              ${employee.Email ? `
              <a href="https://teams.microsoft.com/l/chat/0/0?users=${employee.Email}" class="btn text-color start-chat"
                  title="${startChatText}" target="_blank">
                  <svg xmlns="http://www.w3.org/2000/svg" width="27.09" height="27.09" viewBox="0 0 27.09 27.09">
                      <g id="chatt_2" data-name="chatt 2" transform="translate(0.75 0.75)">
                          <path id="Path_77853" data-name="Path 77853"
                              d="M18.236,13h-.012a1,1,0,0,1,0-2h.012a1,1,0,0,1,0,2Z" transform="translate(-0.323 0.795)"
                              fill="var(--main-color)"></path>
                          <path id="Path_77853-2" data-name="Path 77853"
                              d="M18.236,13h-.012a1,1,0,0,1,0-2h.012a1,1,0,0,1,0,2Z" transform="translate(-5.323 0.795)"
                              fill="var(--main-color)"></path>
                          <path id="Path_77853-3" data-name="Path 77853"
                              d="M18.236,13h-.012a1,1,0,0,1,0-2h.012a1,1,0,0,1,0,2Z" transform="translate(-10.323 0.795)"
                              fill="var(--main-color)"></path>
                          <path id="Path_77854" data-name="Path 77854"
                              d="M14.8,28.34a13.408,13.408,0,0,1-6.031-1.414,1.354,1.354,0,0,0-.941-.118l-2.848.762a2.4,2.4,0,0,1-2.541-.865,2.392,2.392,0,0,1-.414-2.09l.762-2.848a1.343,1.343,0,0,0-.118-.941A13.548,13.548,0,0,1,14.8,1.25a13.545,13.545,0,0,1,9.578,23.123A13.456,13.456,0,0,1,14.8,28.34ZM8.126,25.27a2.951,2.951,0,0,1,1.306.314A12.048,12.048,0,0,0,26.84,14.8,12.045,12.045,0,0,0,6.278,6.278a12.055,12.055,0,0,0-2.271,13.88,2.839,2.839,0,0,1,.224,2L3.469,25a.892.892,0,0,0,.155.789.917.917,0,0,0,.72.362.944.944,0,0,0,.244-.033l2.848-.762A2.673,2.673,0,0,1,8.126,25.27Z"
                              transform="translate(-2 -2)" fill="var(--main-color)"></path>
                      </g>
                  </svg>
                  <span>${startChatText}</span>
              </a>
              ` : ''}
          </div>

      </div>
  </div>
  `;
  }

 
  
  private async _tryMultipleImageUrls(employee: IEmployee, imgElement: HTMLImageElement, loadingElement: HTMLElement, defaultElement: HTMLElement): Promise<void> {
    if (!employee.Email) {
      loadingElement.style.display = 'none';
      defaultElement.style.display = 'flex';
      return;
    }

    const siteUrl = this.context.pageContext.site.absoluteUrl;

    const imageUrls = [
      `${siteUrl}/_layouts/15/userphoto.aspx?size=L&username=${encodeURIComponent(employee.Email)}`,
      `${siteUrl}/_layouts/15/userphoto.aspx?size=M&username=${encodeURIComponent(employee.Email)}`,
      `${siteUrl}/_layouts/15/userphoto.aspx?size=S&username=${encodeURIComponent(employee.Email)}`,

      `${siteUrl}/_layouts/15/userphoto.aspx?size=L&accountname=${encodeURIComponent(employee.Email)}`,

      `${siteUrl}/_layouts/15/userphoto.aspx?username=${encodeURIComponent(employee.Email)}`,
    ];


    for (let i = 0; i < imageUrls.length; i++) {
      const imageUrl = imageUrls[i];

      try {
        const success = await this._testImageUrl(imageUrl, imgElement);
        if (success) {
          loadingElement.style.display = 'none';
          imgElement.style.display = 'block';
          defaultElement.style.display = 'none';
          return;
        }
      } catch (error) {
      }
    }

    loadingElement.style.display = 'none';
    defaultElement.style.display = 'flex';
  }

  private async _testImageUrl(imageUrl: string, imgElement: HTMLImageElement): Promise<boolean> {
    return new Promise(async (resolve) => {
      const directImg = new Image();
      directImg.onload = () => {
        imgElement.src = imageUrl;
        resolve(true);
      };

      directImg.onerror = async () => {

        try {
          const response = await this.context.spHttpClient.get(imageUrl, SPHttpClient.configurations.v1);

          if (response.ok) {
            const blob = await response.blob();

            if (blob.type.startsWith('image/') && blob.size > 0) {
              const objectUrl = URL.createObjectURL(blob);

              const blobImg = new Image();
              blobImg.onload = () => {
                imgElement.src = objectUrl;
                resolve(true);
              };

              blobImg.onerror = () => {
                URL.revokeObjectURL(objectUrl);
                resolve(false);
              };

              blobImg.src = objectUrl;
            } else {
              resolve(false);
            }
          } else {
            resolve(false);
          }
        } catch (spError) {
          resolve(false);
        }
      };

      directImg.src = imageUrl;

      setTimeout(() => {
        resolve(false);
      }, 5000);
    });
  }

  // إضافة الـ methods الجديدة
  

  private _attachEventListeners(): void {
    const searchInput = this.domElement.querySelector('#employeeSearch') as HTMLInputElement;
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        const target = e.target as HTMLInputElement;
        this._searchTerm = target.value.toLowerCase();
        this._currentPage = 1; // إعادة تعيين الصفحة للأولى عند البحث
        this._filterEmployees();
        this.render();
      });
    }
  }

  private _filterEmployees(): void {
    if (!this._searchTerm) {
      this._filteredEmployees = [...this._employees];
    } else {
      this._filteredEmployees = this._employees.filter(employee =>
        employee.Title.toLowerCase().includes(this._searchTerm) ||
        employee.Department.toLowerCase().includes(this._searchTerm) ||
        employee.JobTitle.toLowerCase().includes(this._searchTerm) ||
        employee.Email.toLowerCase().includes(this._searchTerm)
      );
    }
  }

  private _renderPagination(totalPages: number): string {
    if (totalPages <= 1) return '';

    const prevText = this._isArabic ? 'السابق' : 'Previous';
    const nextText = this._isArabic ? 'التالي' : 'Next';

    let paginationHtml = `
      <nav aria-label="Employee pagination" class="mt-4">
        <ul class="pagination justify-content-center">
    `;

    // Previous button
    paginationHtml += `
      <li class="page-item ${this._currentPage === 1 ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${this._currentPage - 1}">
          <i class="bi bi-chevron-left"></i> ${prevText}
        </a>
      </li>
    `;

    // Page numbers
    const startPage = Math.max(1, this._currentPage - 2);
    const endPage = Math.min(totalPages, this._currentPage + 2);

    if (startPage > 1) {
      paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>`;
      if (startPage > 2) {
        paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      paginationHtml += `
        <li class="page-item ${i === this._currentPage ? 'active' : ''}">
          <a class="page-link" href="#" data-page="${i}">${i}</a>
        </li>
      `;
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
      }
      paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a></li>`;
    }

    // Next button
    paginationHtml += `
      <li class="page-item ${this._currentPage === totalPages ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${this._currentPage + 1}">
          ${nextText} <i class="bi bi-chevron-right"></i>
        </a>
      </li>
    `;

    paginationHtml += `
        </ul>
      </nav>
    `;

    // إضافة event listeners للـ pagination
    setTimeout(() => {
      const paginationLinks = this.domElement.querySelectorAll('.pagination .page-link[data-page]');
      paginationLinks.forEach(link => {
        link.addEventListener('click', (e) => {
          e.preventDefault();
          const page = parseInt((e.target as HTMLElement).getAttribute('data-page') || '1');
          if (page >= 1 && page <= totalPages && page !== this._currentPage) {
            this._currentPage = page;
            this.render();
            // Scroll to top of employees section
            this.domElement.scrollIntoView({ behavior: 'smooth' });
          }
        });
      });
    }, 100);

    return paginationHtml;
  }

  protected get dataVersion(): Version {
    return Version.parse('1.0');
  }

  protected getPropertyPaneConfiguration(): IPropertyPaneConfiguration {
    return {
      pages: [
        {
          header: {
            description: this._isArabic ? 'إعدادات جميع الموظفين' : 'All Employees Settings'
          },
          groups: [
            {
              groupName: this._isArabic ? 'الإعدادات الأساسية' : 'Basic Settings',
              groupFields: [
                PropertyPaneTextField('description', {
                  label: this._isArabic ? 'الوصف' : 'Description'
                }),
                PropertyPaneDropdown('selectedDepartment', {
                  label: this._isArabic ? 'اختر القسم' : 'Select Department',
                  options: this._availableDepartments,
                  selectedKey: this.properties.selectedDepartment || (this._availableDepartments.length > 0 ? this._availableDepartments[0].key : 'IT')
                }),
                PropertyPaneTextField('titleEnglish', {
                  label: this._isArabic ? 'العنوان بالإنجليزية' : 'Title in English',
                  placeholder: this._isArabic ? 'مثال: All Employees' : 'Example: All Employees',
                  value: this.properties.titleEnglish || 'All Employees'
                }),
                PropertyPaneTextField('titleArabic', {
                  label: this._isArabic ? 'العنوان بالعربية' : 'Title in Arabic',
                  placeholder: this._isArabic ? 'مثال: كل الموظفين' : 'Example: كل الموظفين',
                  value: this.properties.titleArabic || 'كل الموظفين'
                })
              ]
            }
          ]
        }
      ]
    };
  }

  protected onPropertyPaneFieldChanged(propertyPath: string, oldValue: any, newValue: any): void {
    if (propertyPath === 'selectedDepartment') {
      this._loadEmployees().then(() => {
        this.render();
      });
    } else if (propertyPath === 'titleEnglish' || propertyPath === 'titleArabic') {
      // إعادة عرض الصفحة عند تغيير العناوين
      this.render();
    }
    super.onPropertyPaneFieldChanged(propertyPath, oldValue, newValue);
  }
}