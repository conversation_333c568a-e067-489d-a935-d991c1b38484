{"$schema": "https://developer.microsoft.com/json-schemas/spfx-build/package-solution.schema.json", "solution": {"name": "qatar-museums-client-side-solution", "id": "f667ed59-bdcf-4abe-a900-4e1af668694b", "version": "*******", "includeClientSideAssets": true, "skipFeatureDeployment": true, "isDomainIsolated": false, "webApiPermissionRequests": [{"resource": "Microsoft Graph", "scope": "Team.ReadBasic.All"}, {"resource": "Microsoft Graph", "scope": "Channel.ReadBasic.All"}, {"resource": "Microsoft Graph", "scope": "ChannelMessage.Send"}, {"resource": "Microsoft Graph", "scope": "TeamMember.Read.All"}, {"resource": "Microsoft Graph", "scope": "Group.Read.All"}, {"resource": "Microsoft Graph", "scope": "Group.ReadWrite.All"}, {"resource": "Microsoft Graph", "scope": "Community.ReadWrite.All"}, {"resource": "Microsoft Graph", "scope": "User.Read"}], "developer": {"name": "", "websiteUrl": "", "privacyUrl": "", "termsOfUseUrl": "", "mpnId": "Undefined-1.20.0"}, "metadata": {"shortDescription": {"default": "qatar-museums description"}, "longDescription": {"default": "qatar-museums description"}, "screenshotPaths": [], "videoUrl": "", "categories": []}, "features": [{"title": "qatar-museums Feature", "description": "The feature that activates elements of the qatar-museums solution.", "id": "8ef8ba53-d05e-42cb-aa93-e47419553c27", "version": "*******", "assets": {"elementManifests": ["elements.xml", "ClientSideInstance.xml"]}}]}, "paths": {"zippedPackage": "solution/qatar-museums.sppkg"}}