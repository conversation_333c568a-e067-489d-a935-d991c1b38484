import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient } from '@microsoft/sp-http';
import { Version } from '@microsoft/sp-core-library';
import {
  type IPropertyPaneConfiguration,
  PropertyPaneDropdown,
  PropertyPaneTextField
} from '@microsoft/sp-property-pane';

export interface IDepartmentDirectorWebPartProps {
  description: string;
  selectedDepartment: string;
  selectedJobTitle: string;
  titleEnglish: string;
  titleArabic: string;
}

interface IDepartmentDirector {
  Id: number;
  Title: string;
  Department: string;
  JobTitle: string;
  Email: string;
  Phone?: string;
  ProfilePicture?: string;
  AttachmentFiles?: any[];
}

export default class DepartmentDirectorWebPart extends BaseClientSideWebPart<IDepartmentDirectorWebPartProps> {
  private _director: IDepartmentDirector | null = null;
  private _isArabic: boolean = false;
  private _isLoading: boolean = false;
  private _availableDepartments: Array<{key: string, text: string}> = [];
  private _availableJobTitles: Array<{key: string, text: string}> = [];

  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().includes('/ar/');

    const loadingText = this._isArabic ? 'جاري التحميل...' : 'Loading...';
    const noDirectorText = this._isArabic ? 'لا يوجد مدير للقسم' : 'No department director found';

    this.domElement.innerHTML = `
      <section class="card department-director card-color py-2 px-3 section-card bg-remover">
        <div class="">
          <div class="director-container">
            ${this._isLoading ? `
              <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">${loadingText}</span>
                </div>
                <p class="mt-3">${loadingText}</p>
              </div>
            ` : this._renderDirector(noDirectorText)}
          </div>
        </div>
      </section>
    `;

    // تحميل الصورة بعد العرض إذا كان المدير موجود
    if (!this._isLoading && this._director) {
      setTimeout(() => this._loadDirectorImage(), 100);
    }
  }

  protected async onInit(): Promise<void> {
    await super.onInit();
    // تحميل الأقسام المتاحة أولاً
    await this._loadAvailableDepartments();
    // ثم تحميل Job Titles للقسم المحدد
    await this._loadAvailableJobTitles();
    // أخيراً تحميل المدير
    await this._loadDirector();
    this.render();
  }

  private _getDisplayTitle(): string {
    const defaultTitleArabic = 'رئيس القسم';
    const defaultTitleEnglish = 'Department Director';

    return this._isArabic ?
      (this.properties.titleArabic || defaultTitleArabic) :
      (this.properties.titleEnglish || defaultTitleEnglish);
  }

  private _isDepartmentMatch(userDepartment: string, targetDepartment: string): boolean {
    if (!userDepartment || !targetDepartment) {
      return false;
    }

    const userDept = userDepartment.toLowerCase().trim();
    const targetDept = targetDepartment.toLowerCase().trim();

    // مطابقة مباشرة
    if (userDept === targetDept) {
      return true;
    }

    // مطابقة جزئية - البحث عن الكلمات المفتاحية
    const departmentKeywords: { [key: string]: string[] } = {
      'it': ['it', 'information technology', 'تقنية المعلومات', 'tech', 'technology', 'information'],
      'hr': ['hr', 'human resources', 'الموارد البشرية', 'human', 'resources', 'personnel'],
      'finance': ['finance', 'financial', 'accounting', 'المالية', 'المحاسبة', 'مالية', 'محاسبة'],
      'marketing': ['marketing', 'communications', 'التسويق', 'الاتصالات', 'تسويق', 'اتصالات'],
      'administration': ['admin', 'administration', 'الإدارة', 'إدارة', 'administrative'],
      'legal': ['legal', 'القانونية', 'قانونية', 'law'],
      'security': ['security', 'الأمن', 'أمن'],
      'facilities': ['facilities', 'المرافق', 'مرافق'],
      'operations': ['operations', 'العمليات', 'عمليات']
    };

    // البحث في الكلمات المفتاحية
    const keywords = departmentKeywords[targetDept] || [targetDept];

    for (const keyword of keywords) {
      if (userDept.includes(keyword.toLowerCase())) {
        return true;
      }
    }

    // مطابقة عكسية - البحث عن الكلمة المستهدفة في قسم المستخدم
    if (userDept.includes(targetDept)) {
      return true;
    }

    return false;
  }

  private _isJobTitleMatch(userJobTitle: string, targetJobTitle: string): boolean {
    if (!userJobTitle || !targetJobTitle) {
      return false;
    }

    const userTitle = userJobTitle.toLowerCase().trim();
    const targetTitle = targetJobTitle.toLowerCase().trim();

    // مطابقة مباشرة
    if (userTitle === targetTitle) {
      return true;
    }

    // مطابقة جزئية
    if (userTitle.includes(targetTitle) || targetTitle.includes(userTitle)) {
      return true;
    }

    return false;
  }

  private _getJobTitleOptions(department: string): Array<{key: string, text: string}> {
    const jobTitlesByDepartment: { [key: string]: Array<{key: string, text: string}> } = {
      'IT': [
        { key: 'IT Manager', text: this._isArabic ? 'مدير تقنية المعلومات' : 'IT Manager' },
        { key: 'IT Director', text: this._isArabic ? 'مدير عام تقنية المعلومات' : 'IT Director' },
        { key: 'Head of IT', text: this._isArabic ? 'رئيس قسم تقنية المعلومات' : 'Head of IT' },
        { key: 'Chief Technology Officer', text: this._isArabic ? 'مدير التكنولوجيا التنفيذي' : 'Chief Technology Officer' },
        { key: 'Technical Lead', text: this._isArabic ? 'قائد تقني' : 'Technical Lead' },
        { key: 'Senior IT Manager', text: this._isArabic ? 'مدير تقنية معلومات أول' : 'Senior IT Manager' }
      ],
      'HR': [
        { key: 'HR Manager', text: this._isArabic ? 'مدير الموارد البشرية' : 'HR Manager' },
        { key: 'HR Director', text: this._isArabic ? 'مدير عام الموارد البشرية' : 'HR Director' },
        { key: 'Head of HR', text: this._isArabic ? 'رئيس قسم الموارد البشرية' : 'Head of HR' },
        { key: 'Chief Human Resources Officer', text: this._isArabic ? 'مدير الموارد البشرية التنفيذي' : 'Chief Human Resources Officer' },
        { key: 'Senior HR Manager', text: this._isArabic ? 'مدير موارد بشرية أول' : 'Senior HR Manager' },
        { key: 'HR Business Partner', text: this._isArabic ? 'شريك أعمال الموارد البشرية' : 'HR Business Partner' }
      ],
      'Finance': [
        { key: 'Finance Manager', text: this._isArabic ? 'مدير المالية' : 'Finance Manager' },
        { key: 'Finance Director', text: this._isArabic ? 'مدير عام المالية' : 'Finance Director' },
        { key: 'Head of Finance', text: this._isArabic ? 'رئيس قسم المالية' : 'Head of Finance' },
        { key: 'Chief Financial Officer', text: this._isArabic ? 'المدير المالي التنفيذي' : 'Chief Financial Officer' },
        { key: 'Senior Finance Manager', text: this._isArabic ? 'مدير مالية أول' : 'Senior Finance Manager' },
        { key: 'Financial Controller', text: this._isArabic ? 'مراقب مالي' : 'Financial Controller' }
      ],
      'Marketing': [
        { key: 'Marketing Manager', text: this._isArabic ? 'مدير التسويق' : 'Marketing Manager' },
        { key: 'Marketing Director', text: this._isArabic ? 'مدير عام التسويق' : 'Marketing Director' },
        { key: 'Head of Marketing', text: this._isArabic ? 'رئيس قسم التسويق' : 'Head of Marketing' },
        { key: 'Chief Marketing Officer', text: this._isArabic ? 'مدير التسويق التنفيذي' : 'Chief Marketing Officer' },
        { key: 'Senior Marketing Manager', text: this._isArabic ? 'مدير تسويق أول' : 'Senior Marketing Manager' },
        { key: 'Brand Manager', text: this._isArabic ? 'مدير العلامة التجارية' : 'Brand Manager' }
      ],
      'Administration': [
        { key: 'Administration Manager', text: this._isArabic ? 'مدير الإدارة' : 'Administration Manager' },
        { key: 'Administration Director', text: this._isArabic ? 'مدير عام الإدارة' : 'Administration Director' },
        { key: 'Head of Administration', text: this._isArabic ? 'رئيس قسم الإدارة' : 'Head of Administration' },
        { key: 'Chief Administrative Officer', text: this._isArabic ? 'مدير الإدارة التنفيذي' : 'Chief Administrative Officer' },
        { key: 'Senior Administration Manager', text: this._isArabic ? 'مدير إدارة أول' : 'Senior Administration Manager' },
        { key: 'Office Manager', text: this._isArabic ? 'مدير المكتب' : 'Office Manager' }
      ],
      'Legal': [
        { key: 'Legal Manager', text: this._isArabic ? 'مدير الشؤون القانونية' : 'Legal Manager' },
        { key: 'Legal Director', text: this._isArabic ? 'مدير عام الشؤون القانونية' : 'Legal Director' },
        { key: 'Head of Legal', text: this._isArabic ? 'رئيس قسم الشؤون القانونية' : 'Head of Legal' },
        { key: 'Chief Legal Officer', text: this._isArabic ? 'المستشار القانوني العام' : 'Chief Legal Officer' },
        { key: 'Senior Legal Manager', text: this._isArabic ? 'مدير شؤون قانونية أول' : 'Senior Legal Manager' },
        { key: 'General Counsel', text: this._isArabic ? 'المستشار العام' : 'General Counsel' }
      ],
      'Security': [
        { key: 'Security Manager', text: this._isArabic ? 'مدير الأمن' : 'Security Manager' },
        { key: 'Security Director', text: this._isArabic ? 'مدير عام الأمن' : 'Security Director' },
        { key: 'Head of Security', text: this._isArabic ? 'رئيس قسم الأمن' : 'Head of Security' },
        { key: 'Chief Security Officer', text: this._isArabic ? 'مدير الأمن التنفيذي' : 'Chief Security Officer' },
        { key: 'Senior Security Manager', text: this._isArabic ? 'مدير أمن أول' : 'Senior Security Manager' },
        { key: 'Security Supervisor', text: this._isArabic ? 'مشرف أمن' : 'Security Supervisor' }
      ],
      'Facilities': [
        { key: 'Facilities Manager', text: this._isArabic ? 'مدير المرافق' : 'Facilities Manager' },
        { key: 'Facilities Director', text: this._isArabic ? 'مدير عام المرافق' : 'Facilities Director' },
        { key: 'Head of Facilities', text: this._isArabic ? 'رئيس قسم المرافق' : 'Head of Facilities' },
        { key: 'Senior Facilities Manager', text: this._isArabic ? 'مدير مرافق أول' : 'Senior Facilities Manager' },
        { key: 'Facilities Supervisor', text: this._isArabic ? 'مشرف مرافق' : 'Facilities Supervisor' },
        { key: 'Building Manager', text: this._isArabic ? 'مدير المبنى' : 'Building Manager' }
      ],
      'Operations': [
        { key: 'Operations Manager', text: this._isArabic ? 'مدير العمليات' : 'Operations Manager' },
        { key: 'Operations Director', text: this._isArabic ? 'مدير عام العمليات' : 'Operations Director' },
        { key: 'Head of Operations', text: this._isArabic ? 'رئيس قسم العمليات' : 'Head of Operations' },
        { key: 'Chief Operations Officer', text: this._isArabic ? 'مدير العمليات التنفيذي' : 'Chief Operations Officer' },
        { key: 'Senior Operations Manager', text: this._isArabic ? 'مدير عمليات أول' : 'Senior Operations Manager' },
        { key: 'Operations Supervisor', text: this._isArabic ? 'مشرف عمليات' : 'Operations Supervisor' }
      ]
    };

    // إرجاع الخيارات للقسم المحدد أو خيارات عامة
    return jobTitlesByDepartment[department] || [
      { key: 'Manager', text: this._isArabic ? 'مدير' : 'Manager' },
      { key: 'Director', text: this._isArabic ? 'مدير عام' : 'Director' },
      { key: 'Head', text: this._isArabic ? 'رئيس' : 'Head' },
      { key: 'Senior Manager', text: this._isArabic ? 'مدير أول' : 'Senior Manager' }
    ];
  }

  private async _loadAvailableDepartments(): Promise<void> {
    try {
      const siteUrl = this.context.pageContext.site.absoluteUrl;
      const usersUrl = `${siteUrl}/_api/web/siteusers?$select=*&$filter=PrincipalType eq 1`;

      const response = await this.context.spHttpClient.get(usersUrl, SPHttpClient.configurations.v1);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const siteUsers = data.value || [];

      // فلترة المستخدمين (استبعاد system accounts)
      const filteredUsers = siteUsers.filter((user: any) =>
        user.Email &&
        !user.LoginName.includes('sharepoint') &&
        !user.LoginName.includes('system') &&
        !user.Title.includes('System') &&
        user.Title !== 'Everyone' &&
        user.Title !== 'NT AUTHORITY\\authenticated users'
      );

      const departmentsSet = new Set<string>();

      // جلب الأقسام من User Profiles
      for (const user of filteredUsers) {
        try {
          const profileUrl = `${this.context.pageContext.site.absoluteUrl}/_api/SP.UserProfiles.PeopleManager/GetPropertiesFor(accountName=@v)?@v='${encodeURIComponent(user.LoginName)}'`;
          const profileResponse = await this.context.spHttpClient.get(profileUrl, SPHttpClient.configurations.v1);

          if (profileResponse.ok) {
            const profileData = await profileResponse.json();

            if (profileData.UserProfileProperties) {
              const properties = profileData.UserProfileProperties;
              const deptProp = properties.find((p: any) => p.Key === 'Department');

              if (deptProp && deptProp.Value && deptProp.Value.trim() !== '') {
                departmentsSet.add(deptProp.Value.trim());
              }
            }
          }
        } catch (profileError) {
          // تجاهل الأخطاء والمتابعة
        }
      }

      // تحويل Set إلى Array مع الترجمة
      const departments = Array.from(departmentsSet).map(dept => ({
        key: dept,
        text: dept
      }));

      // إضافة أقسام افتراضية إذا لم نجد أي أقسام
      if (departments.length === 0) {
        this._availableDepartments = [
          { key: 'IT', text: this._isArabic ? 'تقنية المعلومات' : 'Information Technology' },
          { key: 'HR', text: this._isArabic ? 'الموارد البشرية' : 'Human Resources' },
          { key: 'Finance', text: this._isArabic ? 'المالية' : 'Finance' },
          { key: 'Marketing', text: this._isArabic ? 'التسويق' : 'Marketing' },
          { key: 'Administration', text: this._isArabic ? 'الإدارة' : 'Administration' }
        ];
      } else {
        this._availableDepartments = departments.sort((a, b) => a.text.localeCompare(b.text));
      }

    } catch (error) {
      console.error('Error loading departments:', error);
      // استخدام أقسام افتراضية في حالة الخطأ
      this._availableDepartments = [
        { key: 'IT', text: this._isArabic ? 'تقنية المعلومات' : 'Information Technology' },
        { key: 'HR', text: this._isArabic ? 'الموارد البشرية' : 'Human Resources' },
        { key: 'Finance', text: this._isArabic ? 'المالية' : 'Finance' },
        { key: 'Marketing', text: this._isArabic ? 'التسويق' : 'Marketing' },
        { key: 'Administration', text: this._isArabic ? 'الإدارة' : 'Administration' }
      ];
    }
  }

  private async _loadAvailableJobTitles(): Promise<void> {
    const selectedDepartment = this.properties.selectedDepartment;

    if (!selectedDepartment) {
      this._availableJobTitles = this._getJobTitleOptions('IT');
      return;
    }

    try {
      const siteUrl = this.context.pageContext.site.absoluteUrl;
      const usersUrl = `${siteUrl}/_api/web/siteusers?$select=*&$filter=PrincipalType eq 1`;

      const response = await this.context.spHttpClient.get(usersUrl, SPHttpClient.configurations.v1);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const siteUsers = data.value || [];

      // فلترة المستخدمين (استبعاد system accounts)
      const filteredUsers = siteUsers.filter((user: any) =>
        user.Email &&
        !user.LoginName.includes('sharepoint') &&
        !user.LoginName.includes('system') &&
        !user.Title.includes('System') &&
        user.Title !== 'Everyone' &&
        user.Title !== 'NT AUTHORITY\\authenticated users'
      );

      const jobTitlesSet = new Set<string>();

      // جلب Job Titles من User Profiles للقسم المحدد
      for (const user of filteredUsers) {
        try {
          const profileUrl = `${this.context.pageContext.site.absoluteUrl}/_api/SP.UserProfiles.PeopleManager/GetPropertiesFor(accountName=@v)?@v='${encodeURIComponent(user.LoginName)}'`;
          const profileResponse = await this.context.spHttpClient.get(profileUrl, SPHttpClient.configurations.v1);

          if (profileResponse.ok) {
            const profileData = await profileResponse.json();

            if (profileData.UserProfileProperties) {
              const properties = profileData.UserProfileProperties;

              const deptProp = properties.find((p: any) => p.Key === 'Department');
              const titleProp = properties.find((p: any) => p.Key === 'Title');

              if (deptProp && titleProp &&
                  deptProp.Value && titleProp.Value &&
                  this._isDepartmentMatch(deptProp.Value, selectedDepartment)) {
                jobTitlesSet.add(titleProp.Value.trim());
              }
            }
          }
        } catch (profileError) {
          // تجاهل الأخطاء والمتابعة
        }
      }

      // تحويل Set إلى Array
      const jobTitles = Array.from(jobTitlesSet).map(title => ({
        key: title,
        text: title
      }));

      // استخدام Job Titles من SharePoint أو الافتراضية
      if (jobTitles.length > 0) {
        this._availableJobTitles = jobTitles.sort((a, b) => a.text.localeCompare(b.text));
      } else {
        this._availableJobTitles = this._getJobTitleOptions(selectedDepartment);
      }

    } catch (error) {
      console.error('Error loading job titles:', error);
      // استخدام Job Titles افتراضية في حالة الخطأ
      this._availableJobTitles = this._getJobTitleOptions(selectedDepartment);
    }
  }

  private async _loadDirector(): Promise<void> {
    try {
      this._isLoading = true;
      this.render(); // إعادة عرض الصفحة لإظهار Loading

      // جلب المستخدمين من SharePoint Site Users
      await this._loadSiteUsers();

    } catch (error) {
      console.error('Error fetching department director:', error);
      this._director = null;
    } finally {
      this._isLoading = false;
    }
  }

  private async _loadSiteUsers(): Promise<void> {
    try {
      const siteUrl = this.context.pageContext.site.absoluteUrl;

      // جلب المستخدمين من الموقع
      const usersUrl = `${siteUrl}/_api/web/siteusers?$select=*&$filter=PrincipalType eq 1`;


      const response = await this.context.spHttpClient.get(usersUrl, SPHttpClient.configurations.v1);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const siteUsers = data.value || [];


      // فلترة المستخدمين وجلب معلومات إضافية من User Profiles
      await this._enrichUsersWithProfiles(siteUsers);

    } catch (error) {
      console.error('Error fetching site users:', error);
     
    }
  }

  private async _enrichUsersWithProfiles(siteUsers: any[]): Promise<void> {
    const enrichedEmployees: IDepartmentDirector[] = [];

    // فلترة المستخدمين (استبعاد system accounts)
    const filteredUsers = siteUsers.filter(user =>
      user.Email &&
      !user.LoginName.includes('sharepoint') &&
      !user.LoginName.includes('system') &&
      !user.Title.includes('System') &&
      user.Title !== 'Everyone' &&
      user.Title !== 'NT AUTHORITY\\authenticated users'
    );


    for (const user of filteredUsers) {
      try {
        const profileUrl = `${this.context.pageContext.site.absoluteUrl}/_api/SP.UserProfiles.PeopleManager/GetPropertiesFor(accountName=@v)?@v='${encodeURIComponent(user.LoginName)}'`;

        const profileResponse = await this.context.spHttpClient.get(profileUrl, SPHttpClient.configurations.v1);

        let jobTitle = '';
        let department = '';
        let phone = '';
        let profilePicture = '';

        if (profileResponse.ok) {
          const profileData = await profileResponse.json();

          // استخراج المعلومات من User Profile Properties
          if (profileData.UserProfileProperties) {
            const properties = profileData.UserProfileProperties;

            const titleProp = properties.find((p: any) => p.Key === 'Title');
            if (titleProp) jobTitle = titleProp.Value || jobTitle;

            const deptProp = properties.find((p: any) => p.Key === 'Department');
            if (deptProp) department = deptProp.Value || '';


          }

          // محاولة جلب صورة الملف الشخصي من عدة مصادر
          if (profileData.PictureUrl && profileData.PictureUrl !== '') {
            profilePicture = profileData.PictureUrl;
          } else {
            // جلب الصورة من User Photo Service
            profilePicture = await this._getUserPhotoUrl(user.Email);
          }
        } else {
          profilePicture = await this._getUserPhotoUrl(user.Email);
        }

        // فلترة بناءً على القسم والمنصب المحددين من الإعدادات
        const selectedDepartment = this.properties.selectedDepartment || 'IT';
        const selectedJobTitle = this.properties.selectedJobTitle || 'Manager';

        const isDepartmentMatch = this._isDepartmentMatch(department, selectedDepartment);
        const isJobTitleMatch = this._isJobTitleMatch(jobTitle, selectedJobTitle);

        if (isDepartmentMatch && isJobTitleMatch) {
          const employee: IDepartmentDirector = {
            Id: user.Id,
            Title: user.Title,
            Department: department,
            JobTitle: jobTitle,
            Email: user.Email,
            Phone: phone,
            ProfilePicture: profilePicture
          };

          enrichedEmployees.push(employee);

          // وجدنا المدير، توقف عن البحث
          break;
        }

      } catch (profileError) {
        console.warn('Could not fetch profile for user:', user.Title, profileError);

      }

      // إذا وجدنا مدير واحد، توقف
      if (enrichedEmployees.length >= 1) {
        break;
      }
    }

    // حفظ أول مدير تم العثور عليه
    this._director = enrichedEmployees.length > 0 ? enrichedEmployees[0] : null;
  }

  
  private async _loadDirectorImage(): Promise<void> {
    if (!this._director) return;


    try {
      const imageId = `director-img-${this._director.Id}`;
      const loadingElement = document.getElementById(`${imageId}-loading`);
      const imgElement = document.getElementById(imageId) as HTMLImageElement;
      const defaultElement = document.getElementById(`${imageId}-default`);

      if (!imgElement || !loadingElement || !defaultElement) {
        return;
      }


      await this._tryMultipleImageUrls(this._director, imgElement, loadingElement, defaultElement);

    } catch (error) {
      console.error(`Error loading image for ${this._director.Title}:`, error);

      const imageId = `director-img-${this._director.Id}`;
      const loadingElement = document.getElementById(`${imageId}-loading`);
      const defaultElement = document.getElementById(`${imageId}-default`);

      if (loadingElement) loadingElement.style.display = 'none';
      if (defaultElement) defaultElement.style.display = 'flex';
    }
  }

  private _renderDirector(noDirectorText: string): string {
    if (!this._director) {
      return `
        <div class="text-center py-5">
          <i class="bi bi-person-badge icon-lg  text-color "></i>
          <p class=" text-color  mt-3">${noDirectorText}</p>
        </div>
      `;
    }

    const sendMailText = this._isArabic ? 'إرسال بريد' : 'Send Mail';
    const startChatText = this._isArabic ? 'بدء محادثة' : 'Start Chat';

    const imageId = `director-img-${this._director.Id}`;


    return `
    <div class="row justify-content-center">
    <div class="col-lg-12">
    <div class="section-card text-center">
    <h2 class="head-color h5 mb-4 fw-bold tittle" style="text-align: left !important;">${this._getDisplayTitle()}</h2>
            <div class="d-flex justify-content-center align-items-center mb-3">
              <div class="image-container" style="position: relative; width: 150px; height: 150px; margin: 0 auto;">
                <div id="${imageId}-loading" class="loading-avatar"
                    style="width: 150px; height: 150px; border-radius: 50%; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center; color: #6c757d; font-size: 1rem; border: 4px solid #e9ecef;">
                    <i class="bi bi-hourglass-split"></i> Loading...
                </div>
                <img class="img-fluid rounded-circle" id="${imageId}"
                    style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid var(--main-color, #2c5aa0); display: none; position: absolute; top: 0; left: 0;"
                    alt="${this._director.Title}" />
                <div id="${imageId}-default" class="default-avatar"
                    style="display: none; position: absolute; top: 0; left: 0; width: 150px; height: 150px; border-radius: 50%; background-color: var(--main-color, #2c5aa0); align-items: center; justify-content: center; color: white; font-size: 4rem; border: 4px solid var(--main-color, #2c5aa0);">
                    <i class="bi bi-person-circle"></i>
                </div>
              </div>
            </div>

            <h3 class="head-color font-weight-bold mb-2">${this._director.Title || ''}</h3>
            <h5 class="text-color mb-2">${this._director.JobTitle || ''}</h5>

            <div class="d-flex justify-content-center gap-3 flex-wrap">
              ${this._director.Email ? `
              <a href="mailto:${this._director.Email}" class="btn text-color send-mail" title="${sendMailText}">
                  <svg xmlns="http://www.w3.org/2000/svg" width="26.291" height="25.249" viewBox="0 0 26.291 25.249">
                      <g id="Message_1" data-name="Message 1" transform="translate(0 0)">
                          <path id="Stroke_1" data-name="Stroke 1"
                              d="M7.366,6.1c-1.484,0-3.279-.909-5.335-2.7A26.173,26.173,0,0,1-.524.828.96.96,0,1,1,.944-.409C2.44,1.365,5.508,4.18,7.366,4.18S12.261,1.368,13.74-.4A.96.96,0,0,1,15.215.824,25.818,25.818,0,0,1,12.682,3.4C10.64,5.189,8.852,6.1,7.366,6.1Z"
                              transform="translate(5.794 8.641)" fill="var(--main-color)" />
                          <path id="Stroke_3" data-name="Stroke 3"
                              d="M12.4-.75c5.007,0,8.148.869,10.184,2.818s2.962,4.985,2.962,9.807-.913,7.845-2.962,9.806S17.4,24.5,12.4,24.5,4.248,23.63,2.212,21.681-.75,16.7-.75,11.875.163,4.029,2.212,2.068,7.388-.75,12.4-.75Zm0,23.33c8.6,0,11.226-2.5,11.226-10.705S21,1.169,12.4,1.169,1.169,3.671,1.169,11.875,3.792,22.58,12.4,22.58Z"
                              transform="translate(0.75 0.75)" fill="var(--main-color)" />
                      </g>
                  </svg>
                  <span>${sendMailText}</span>
              </a>
              ` : ''}

              ${this._director.Email ? `
              <a href="https://teams.microsoft.com/l/chat/0/0?users=${this._director.Email}" class="btn text-color start-chat"
                  title="${startChatText}" target="_blank">
                  <svg xmlns="http://www.w3.org/2000/svg" width="27.09" height="27.09" viewBox="0 0 27.09 27.09">
                      <g id="chatt_2" data-name="chatt 2" transform="translate(0.75 0.75)">
                          <path id="Path_77853" data-name="Path 77853"
                              d="M18.236,13h-.012a1,1,0,0,1,0-2h.012a1,1,0,0,1,0,2Z" transform="translate(-0.323 0.795)"
                              fill="var(--main-color)"></path>
                          <path id="Path_77853-2" data-name="Path 77853"
                              d="M18.236,13h-.012a1,1,0,0,1,0-2h.012a1,1,0,0,1,0,2Z" transform="translate(-5.323 0.795)"
                              fill="var(--main-color)"></path>
                          <path id="Path_77853-3" data-name="Path 77853"
                              d="M18.236,13h-.012a1,1,0,0,1,0-2h.012a1,1,0,0,1,0,2Z" transform="translate(-10.323 0.795)"
                              fill="var(--main-color)"></path>
                          <path id="Path_77854" data-name="Path 77854"
                              d="M14.8,28.34a13.408,13.408,0,0,1-6.031-1.414,1.354,1.354,0,0,0-.941-.118l-2.848.762a2.4,2.4,0,0,1-2.541-.865,2.392,2.392,0,0,1-.414-2.09l.762-2.848a1.343,1.343,0,0,0-.118-.941A13.548,13.548,0,0,1,14.8,1.25a13.545,13.545,0,0,1,9.578,23.123A13.456,13.456,0,0,1,14.8,28.34ZM8.126,25.27a2.951,2.951,0,0,1,1.306.314A12.048,12.048,0,0,0,26.84,14.8,12.045,12.045,0,0,0,6.278,6.278a12.055,12.055,0,0,0-2.271,13.88,2.839,2.839,0,0,1,.224,2L3.469,25a.892.892,0,0,0,.155.789.917.917,0,0,0,.72.362.944.944,0,0,0,.244-.033l2.848-.762A2.673,2.673,0,0,1,8.126,25.27Z"
                              transform="translate(-2 -2)" fill="var(--main-color)"></path>
                      </g>
                  </svg>
                  <span>${startChatText}</span>
              </a>
              ` : ''}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private async _getUserPhotoUrl(email: string): Promise<string> {
    if (!email) return '';

    try {
      const siteUrl = this.context.pageContext.site.absoluteUrl;


      try {
        const profileUrl = `${siteUrl}/_api/SP.UserProfiles.PeopleManager/GetUserProfilePropertyFor(accountName=@v,propertyName='PictureURL')?@v='${encodeURIComponent(email)}'`;


        const response = await this.context.spHttpClient.get(profileUrl, SPHttpClient.configurations.v1);
        if (response.ok) {
          const data = await response.json();
          if (data.value && data.value !== '' && data.value !== 'null') {
            return data.value;
          }
        }
      } catch (restError) {
      }

      const photoServiceUrl = `${siteUrl}/_layouts/15/userphoto.aspx?size=L&username=${encodeURIComponent(email)}&t=${Date.now()}`;

      return photoServiceUrl;

    } catch (error) {
      console.warn(`Error getting photo for ${email}:`, error);

      const siteUrl = this.context.pageContext.site.absoluteUrl;
      const fallbackUrl = `${siteUrl}/_layouts/15/userphoto.aspx?size=M&username=${encodeURIComponent(email)}`;
      return fallbackUrl;
    }
  }



  // private _getEmployeeImage(employee: IDepartmentDirector): string {

  //   // إذا كانت الصورة من User Profile Service أو تم جلبها مسبقاً
  //   if (employee.ProfilePicture && employee.ProfilePicture.startsWith('http')) {
  //     return employee.ProfilePicture;
  //   }

  //   // محاولة الحصول على الصورة من ProfilePicture (JSON format)
  //   if (employee.ProfilePicture) {
  //     try {
  //       const imgData = JSON.parse(employee.ProfilePicture);
  //       if (imgData?.serverUrl && imgData?.serverRelativeUrl) {
  //         const fullUrl = imgData.serverUrl + imgData.serverRelativeUrl;
  //         return fullUrl;
  //       }
  //     } catch (e) {
  //       // ليس JSON، ربما URL مباشر
  //       if (employee.ProfilePicture.includes('http')) {
  //         return employee.ProfilePicture;
  //       }
  //     }
  //   }

  //   // إذا لم نجد صورة في ProfilePicture، ابحث في AttachmentFiles
  //   if (employee.AttachmentFiles && employee.AttachmentFiles.length > 0) {
  //     const imageFile = employee.AttachmentFiles.find((file: any) => {
  //       const fileName = file.FileName?.toLowerCase() || '';
  //       return fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') ||
  //              fileName.endsWith('.png') || fileName.endsWith('.gif') ||
  //              fileName.endsWith('.webp');
  //     });

  //     if (imageFile) {
  //       return imageFile.ServerRelativeUrl;
  //     }
  //   }

  //   // محاولة الحصول على صورة من SharePoint
  //   if (employee.Email) {
  //     const siteUrl = this.context.pageContext.site.absoluteUrl;

  //     // جرب طرق مختلفة لجلب الصورة
  //     const photoUrls = [
  //       // طريقة 1: User Photo Service مع أحجام مختلفة
  //       `${siteUrl}/_layouts/15/userphoto.aspx?size=L&username=${encodeURIComponent(employee.Email)}`,
  //       `${siteUrl}/_layouts/15/userphoto.aspx?size=M&username=${encodeURIComponent(employee.Email)}`,

  //       // طريقة 2: استخدام AccountName بدلاً من Email
  //       `${siteUrl}/_layouts/15/userphoto.aspx?size=L&accountname=${encodeURIComponent(employee.Email)}`,

  //       // طريقة 3: استخدام User ID إذا كان متاح
  //       `${siteUrl}/_layouts/15/userphoto.aspx?size=L&userid=${employee.Id}`,

  //       // طريقة 4: Graph API style (قد يعمل في بعض البيئات)
  //       `${siteUrl}/_api/v2.0/users('${encodeURIComponent(employee.Email)}')/photo/$value`
  //     ];

  //     // استخدم الطريقة الأولى كافتراضي
  //     const selectedUrl = photoUrls[0];
  //     return selectedUrl;
  //   }

  //   return '';
  // }

  private async _tryMultipleImageUrls(employee: IDepartmentDirector, imgElement: HTMLImageElement, loadingElement: HTMLElement, defaultElement: HTMLElement): Promise<void> {
    if (!employee.Email) {
      loadingElement.style.display = 'none';
      defaultElement.style.display = 'flex';
      return;
    }

    const siteUrl = this.context.pageContext.site.absoluteUrl;

    const imageUrls = [
      `${siteUrl}/_layouts/15/userphoto.aspx?size=L&username=${encodeURIComponent(employee.Email)}`,
      `${siteUrl}/_layouts/15/userphoto.aspx?size=M&username=${encodeURIComponent(employee.Email)}`,
      `${siteUrl}/_layouts/15/userphoto.aspx?size=S&username=${encodeURIComponent(employee.Email)}`,

      `${siteUrl}/_layouts/15/userphoto.aspx?size=L&accountname=${encodeURIComponent(employee.Email)}`,

      `${siteUrl}/_layouts/15/userphoto.aspx?username=${encodeURIComponent(employee.Email)}`,
    ];


    for (let i = 0; i < imageUrls.length; i++) {
      const imageUrl = imageUrls[i];

      try {
        const success = await this._testImageUrl(imageUrl, imgElement);
        if (success) {
          loadingElement.style.display = 'none';
          imgElement.style.display = 'block';
          defaultElement.style.display = 'none';
          return;
        }
      } catch (error) {
      }
    }

    loadingElement.style.display = 'none';
    defaultElement.style.display = 'flex';
  }

  private async _testImageUrl(imageUrl: string, imgElement: HTMLImageElement): Promise<boolean> {
    return new Promise(async (resolve) => {
      const directImg = new Image();
      directImg.onload = () => {
        imgElement.src = imageUrl;
        resolve(true);
      };

      directImg.onerror = async () => {

        try {
          const response = await this.context.spHttpClient.get(imageUrl, SPHttpClient.configurations.v1);

          if (response.ok) {
            const blob = await response.blob();

            if (blob.type.startsWith('image/') && blob.size > 0) {
              const objectUrl = URL.createObjectURL(blob);

              const blobImg = new Image();
              blobImg.onload = () => {
                imgElement.src = objectUrl;
                resolve(true);
              };

              blobImg.onerror = () => {
                URL.revokeObjectURL(objectUrl);
                resolve(false);
              };

              blobImg.src = objectUrl;
            } else {
              resolve(false);
            }
          } else {
            resolve(false);
          }
        } catch (spError) {
          resolve(false);
        }
      };

      directImg.src = imageUrl;

      setTimeout(() => {
        resolve(false);
      }, 5000);
    });
  }

  protected get dataVersion(): Version {
    return Version.parse('1.0');
  }

  protected getPropertyPaneConfiguration(): IPropertyPaneConfiguration {
    return {
      pages: [
        {
          header: {
            description: this._isArabic ? 'إعدادات مدير القسم' : 'Department Director Settings'
          },
          groups: [
            {
              groupName: this._isArabic ? 'الإعدادات الأساسية' : 'Basic Settings',
              groupFields: [
                PropertyPaneTextField('description', {
                  label: this._isArabic ? 'الوصف' : 'Description'
                }),
                PropertyPaneDropdown('selectedDepartment', {
                  label: this._isArabic ? 'اختر القسم' : 'Select Department',
                  options: this._availableDepartments,
                  selectedKey: this.properties.selectedDepartment || (this._availableDepartments.length > 0 ? this._availableDepartments[0].key : 'IT')
                }),
                PropertyPaneDropdown('selectedJobTitle', {
                  label: this._isArabic ? 'اختر المنصب' : 'Select Job Title',
                  options: this._availableJobTitles,
                  selectedKey: this.properties.selectedJobTitle || (this._availableJobTitles.length > 0 ? this._availableJobTitles[0].key : 'Manager')
                }),
                PropertyPaneTextField('titleEnglish', {
                  label: this._isArabic ? 'العنوان بالإنجليزية' : 'Title in English',
                  placeholder: this._isArabic ? 'مثال: Department Director' : 'Example: Department Director',
                  value: this.properties.titleEnglish || 'Department Director'
                }),
                PropertyPaneTextField('titleArabic', {
                  label: this._isArabic ? 'العنوان بالعربية' : 'Title in Arabic',
                  placeholder: this._isArabic ? 'مثال: رئيس القسم' : 'Example: رئيس القسم',
                  value: this.properties.titleArabic || 'رئيس القسم'
                })
              ]
            }
          ]
        }
      ]
    };
  }

  protected async onPropertyPaneFieldChanged(propertyPath: string, oldValue: any, newValue: any): Promise<void> {
    if (propertyPath === 'selectedDepartment') {
      // إعادة تعيين Job Title عند تغيير القسم
      this.properties.selectedJobTitle = '';

      // إعادة تحميل Job Titles للقسم الجديد
      await this._loadAvailableJobTitles();

      // إعادة تحديث Property Pane لإظهار Job Titles الجديدة
      this.context.propertyPane.refresh();

      // إعادة تحميل البيانات
      await this._loadDirector();
      this.render();
    } else if (propertyPath === 'selectedJobTitle') {
      await this._loadDirector();
      this.render();
    } else if (propertyPath === 'titleEnglish' || propertyPath === 'titleArabic') {
      // إعادة عرض الصفحة عند تغيير العناوين
      this.render();
    }
    super.onPropertyPaneFieldChanged(propertyPath, oldValue, newValue);
  }
}