import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';

// import './newsDetails.css';

export interface INewsDetailsWebPartProps {
  description: string;
}
export interface IBreadcrumbItem {
  title: string;
  url?: string;
  isCurrent?: boolean;
}

export default class NewsDetailsWebPart extends BaseClientSideWebPart<INewsDetailsWebPartProps> {
  _isArabic: boolean = false;
  newsItems: {
    ID: number;
    Title: string;
    Title_AR: string;
    NewsDate: string;
    ViewCount: number;
    Image: string;
    Content: string;
    Content_AR: string;
    attachments: string[]; // Add this property to hold attachments
  }[] = [];
  latestThreeWithImages : any[] = []
  currentNews: {
    ID: number;
    Title: string;
    Title_AR: string;
    NewsDate: string;
    ViewCount: number;
    Image: string;
    Content: string;
    Content_AR: string;
  } | null = null;

  attachments: string[] = [];

  fetchRecentNews(): Promise<void> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('News%20Articles')/items?$top=3&$orderby=NewsDate desc`;
    return this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => response.json())
      .then((data) => {
        this.newsItems = data.value.map((item: any) => ({
          ID: item.ID,
          Title: item.Title || '',
          Title_AR: item.Title_AR || '',
          NewsDate: item.NewsDate,
          ViewCount: item.ViewCount || 0,
          Image: item.Image || '',
          Content: item.NewsBody_EN || '',
          Content_AR: item.NewsBody_AR || '',
          attachments: []
        }));
        // Now, fetch the attachments for each news item
        return Promise.all(this.newsItems.map(item => this.getAttachments(item.ID)));
      }).then((attachmentsArray) => {
        // Now we associate each item with its attachments
        this.newsItems = this.newsItems.map((item, index) => {
          item.attachments = attachmentsArray[index]; // Add the attachments to each item
          return item;
        });
        this.latestThreeWithImages = this.newsItems
        .filter(item => item.attachments.length > 0)
        .sort((a, b) => new Date(b.NewsDate).getTime() - new Date(a.NewsDate).getTime())
        .slice(0, 3);
        this.render();  // Now render the news with attachments
      }).catch(error => {
        console.error('Error fetching recent news:', error);
      });
    

  }

  getAttachments(itemId: number): Promise<string[]> {
    const listName = "News Articles";
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('${listName}')/items(${itemId})/AttachmentFiles`;

    return this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        return data.value.map((file: any) => {
          return `${file.ServerRelativeUrl}`;
        });
      })
      .catch(error => {
        console.error('Error fetching attachments:', error);
        return [];
      });
  }

  fetchNewsDetails(newsId: number): Promise<void> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('News%20Articles')/items?$filter=ID eq ${newsId}`;
    return this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => response.json())
      .then((data) => {
        if (data.value.length > 0) {
          const news = data.value[0];
          return this.getAttachments(news.ID).then((attachments) => {
            this.attachments = attachments;

            let imageUrl = '';
            const bannerImageData = news?.Image ? JSON.parse(news.Image) : null;

            if (bannerImageData?.serverUrl && bannerImageData?.serverRelativeUrl) {
              imageUrl = bannerImageData.serverUrl + bannerImageData.serverRelativeUrl;
            } else if (this.attachments.length > 0) {
              imageUrl = this.attachments[0];
            } else {
              imageUrl = require('./assets/img.jpg');
            }

            this.currentNews = {
              ID: news.ID,
              Title: news.Title || '',
              Title_AR: news.Title_AR || '',
              NewsDate: news.NewsDate,
              ViewCount: news.ViewCount || 0,
              Image: imageUrl,
              Content: news.NewsBody_EN || '',
              Content_AR: news.NewsBody_AR || ''
            };
            this.render();
          });
        }
      }).catch(error => {
        console.error('Error fetching news details:', error);
      });
  }

  onInit(): Promise<void> {
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;

    const urlParams = new URLSearchParams(window.location.search);
    const newsId = urlParams.get('newsid');

    return Promise.all([
      this.fetchNewsDetails(Number(newsId)),
      this.fetchRecentNews()  // Ensure recent news is fetched properly
    ]).then(() => {
      this.render();
    }).catch(error => {
      console.error('Error fetching news:', error);
    });
  }

  render(): void {
    if (this.currentNews) {
      const referrer = document.referrer;
      const isArabic = window.location.pathname.includes('/ar/');
      const backText = isArabic ? 'رجوع' : 'Back';
      const breadcrumbItems = this.generateDynamicBreadcrumb();
      const breadcrumbHtml = this.generateBreadcrumbHtml(breadcrumbItems);


      if (referrer) {
        const refUrl = new URL(referrer);
        const serverRelative = refUrl.pathname;

        this.getPageTitle(serverRelative).then(title => {
          this.domElement.querySelector('#referrerName')!.textContent = title;
        });
      }

      // const referrerName = document.title
      this.domElement.innerHTML = `
      <section class="bread-cramp mt-3 mb-3">
        <div class="card card-color">
          <div class="card-body d-flex justify-content-between align-items-center flex-wrap">
            <nav aria-label="breadcrumb">
              <ol class="breadcrumb breadcrumb-container m-0">
                ${breadcrumbHtml}
              </ol>
            </nav>
            <div class="d-flex align-items-center gap-3">
              


            <button class="btn btn-outline-dark d-flex align-items-center gap-1" onclick="history.back()">
              <i class="bi bi-arrow-left"></i> ${backText}
            </button>
            

            </div>
          </div>
        </div>
      </section>

        <div class="row news-details">
    <div class="col-lg-8">
        <article class="news-article card card-color section-card p-4 rounded-3 shadow-sm">
            <header class="mb-4">
                <h1 class="head-color">${this._isArabic ? this.currentNews.Title_AR : this.currentNews.Title}</h1>
                <hr>
                <div class="meta d-flex align-items-center gap-3 text-color">
                    <div>
                        <span><i class="icon-Calendar main-color"></i>
                            ${this.formatDateSimple(this.currentNews.NewsDate)}</span>
                        <span><i class="icon-Show main-color"></i> ${this.currentNews.ViewCount}</span>
                    </div>
                    <div class="dropdown ms-auto">
                        <a class="text-decoration-none text-color d-flex align-items-center" href="#" id="shareDropdown"
                            role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-share main-color me-1"></i> Share
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="shareDropdown">
                            <li>
                                <a class="dropdown-item d-flex align-items-center gap-2" href="#" id="sendEmail">
                                    <i class="bi bi-send main-color"></i> Send
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center gap-2" href="#" id="copyLink">
                                    <i class="bi bi-clipboard main-color"></i> Copy Link
                                </a>
                                </li>
                        </ul>
                    </div>
                </div>

            </header>

            <!-- Image / Carousel for Attachments -->
            <figure class="mb-4">
              <img src="${this.currentNews.Image}" alt="${this._isArabic ? this.currentNews.Title_AR : this.currentNews.Title}" class="img-fluid rounded w-100">
            </figure>

            <div class="article-content text-color">
                <p>${this._isArabic ? this.currentNews.Content_AR : this.currentNews.Content}</p>
            </div>
        </article>
    </div>

    <!-- Recent News Section -->
    <div class="col-lg-4">
        <aside class="p-4 rounded-3 shadow-sm section-card card card-color">
            <h2 class="head-color h5 mb-4 fw-bold tittle head-color ">Recent news</h2>
            <div class="recent-news">
                ${this.latestThreeWithImages.map(item => `
                <a href="/sites/intranet-qm/SitePages/NewsDetails.aspx?newsid=${item.ID}"
                    class="text-decoration-none mb-3 d-flex align-items-start gap-3">
                    <!-- Check if there are attachments for each news item -->
                    <img src="${item.attachments.length > 0 ? item.attachments[0] : require('./assets/img.jpg')}"
                        alt="News thumbnail" class="rounded" width="80">
                    <div>
                        <h3 class="h6 head-color">${this._isArabic ? item.Title_AR : item.Title}</h3>
                        <small class="text-color">${this.formatDateSimple(item.NewsDate)}</small>
                    </div>
                </a>
                `).join('')}
            </div>
        </aside>
    </div>
</div>
      `;

      this.addEventListeners();
    } else {
      this.domElement.innerHTML = `<div>خبر غير موجود</div>`;
    }
  }

  formatDateSimple(date: string): string {
    const parsedDate = new Date(date);
    const year = parsedDate.getFullYear();
    const month = (parsedDate.getMonth() + 1).toString();
    const day = parsedDate.getDate().toString();
    return `${year}-${month}-${day}`;
  }

  addEventListeners(): void {
    const sendEmailBtn = this.domElement.querySelector('#sendEmail');
    const copyLinkBtn = this.domElement.querySelector('#copyLink');

    if (sendEmailBtn) {
      sendEmailBtn.addEventListener('click', () => this.sendEmail(window.location.href));
    }

    if (copyLinkBtn) {
      copyLinkBtn.addEventListener('click', () => this.copyLink(window.location.href));
    }
  }

  copyLink(url: string): void {
    navigator.clipboard.writeText(url).then(() => {
    }).catch((error) => {
      console.error('Error copying link:', error);
    });
  }

  sendEmail(url: string): void {
    const subject = "Check this out!";
    const body = `Here is the link to the news article: ${url}`;
    window.location.href = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  }
  async getPageTitle(serverRelativeUrl: string): Promise<string> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/getfilebyserverrelativeurl('${serverRelativeUrl}')/ListItemAllFields?$select=Title`;

    const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
    const data = await response.json();

    return data?.Title || '';
  }
  generateDynamicBreadcrumb(): IBreadcrumbItem[] {
    const path = window.location.pathname;
    const segments = path.split('/').filter(seg => seg); // Remove empty strings
    const breadcrumbItems: IBreadcrumbItem[] = [];

    let accumulatedUrl = '';

    segments.forEach((segment, index) => {
      accumulatedUrl += `/${segment}`;

      const skipList = ['sitepages', 'pages', 'sites'];
      if (skipList.indexOf(segment.toLowerCase()) !== -1) return;


      const formattedTitle = decodeURIComponent(segment)
        .replace(/[-_]/g, ' ')
        .replace(/\.aspx/i, '')
        .replace(/\b\w/g, c => c.toUpperCase());

      if (index === segments.length - 1) {
        breadcrumbItems.push({
          title: formattedTitle,
          isCurrent: true
        });
      } else {
        breadcrumbItems.push({
          title: formattedTitle,
          url: accumulatedUrl
        });
      }
    });

    // Add home link at the beginning
    breadcrumbItems.unshift({
      title: this._isArabic ? 'الرئيسية' : 'Home',
      url: '/'
    });

    return breadcrumbItems;
  }
  private generateBreadcrumbHtml(items: IBreadcrumbItem[]): string {
    return `
      <ol class="breadcrumb m-0">
        ${items
        .map(item => {
          if (item.isCurrent) {
            return `<li class="breadcrumb-item active" aria-current="page">${item.title}</li>`;
          } else {
            return `<li class="breadcrumb-item"><a href="${item.url}" class="btn-main-link fw-bold">${item.title}</a></li>`;
          }
        })
        .join('')}
      </ol>
    `;
  }


}
