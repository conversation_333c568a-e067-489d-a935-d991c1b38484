@import '~@microsoft/sp-office-ui-fabric-core/dist/sass/SPFabricCore.scss';

.newsTicker {
  overflow: hidden;
  padding: 1em;
  color: "[theme:bodyText, default: #323130]";
  color: var(--bodyText);
  &.teams {
    font-family: $ms-font-family-fallbacks;
  }
}

.welcome {
  text-align: center;
}

.welcomeImage {
  width: 100%;
  max-width: 420px;
}

.links {
  a {
    text-decoration: none;
    color: "[theme:link, default:#03787c]";
    color: var(--link); // note: CSS Custom Properties support is limited to modern browsers only

    &:hover {
      text-decoration: underline;
      color: "[theme:linkHovered, default: #014446]";
      color: var(--linkHovered); // note: CSS Custom Properties support is limited to modern browsers only
    }
  }
}
.breakingNews {
  overflow: hidden;
  white-space: nowrap;
  background-color: #f8f8f8;
  padding: 10px 0;
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
}

.tickerWrapper {
  width: 100%;
}

.tickerTrack {
  display: inline-block;
  animation: ticker-scroll 20s linear infinite; // Fallback duration
}

.tickerItem {
  display: inline-block;
  padding: 0 20px;
  font-size: 16px;
  color: #333;
}

.tickerLabel {
  font-weight: bold;
  color: #d32f2f; // Red for emphasis
  margin-right: 10px;
}

.tickerDate {
  color: #666;
  margin-right: 10px;
}

.tickerText {
  margin-right: 10px;
}

.tickerSource {
  font-style: italic;
  color: #888;
}
.tickerLink{
  color: #888;

}
@keyframes ticker-scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%); // Move half the content width for seamless loop
  }
}

// Ensure visibility on both light and dark themes
:global(.sp-theme-dark) {
  .breakingNews {
    background-color: #333;
    border-color: #555;
  }

  .tickerItem {
    color: #fff;
  }

  .tickerLabel {
    color: #ef5350;
  }

  .tickerDate {
    color: #bbb;
  }

  .tickerSource {
    color: #aaa;
  }
  .tickerLink{
    color: #aaa;

  }
}