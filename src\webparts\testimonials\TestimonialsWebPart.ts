import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient } from '@microsoft/sp-http';
import { ResponseType } from '@microsoft/microsoft-graph-client';

export interface ITestimonialsWebPartProps {
  description: string;
}

export default class TestimonialsWebPart extends BaseClientSideWebPart<ITestimonialsWebPartProps> {
  private _isArabic: boolean = false;

  public async render(): Promise<void> {
    this._isArabic = window.location.pathname.toLowerCase().includes("/ar/");

    const urlParams = new URLSearchParams(window.location.search);
    const empId = urlParams.get("empId");
    const response = await this.context.spHttpClient.get(
      `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Testimonials')/items?$filter=ID eq ${empId}&$select=*,Author/Created,Author/Title,Author/JobTitle,Author/Department,Author/EMail,Question1_EN/Title,Question2_EN/Title,Question3_EN/Title,Question4_EN/Title,VideoLink&$expand=Question1_EN,Question2_EN,Question3_EN,Question4_EN,Author`,
      SPHttpClient.configurations.v1
    );

    const client = await this.context.msGraphClientFactory.getClient('3');

    if (!response.ok) {
      this.domElement.innerHTML = `<p>${this._isArabic ? 'فشل في جلب الشهادة.' : 'Failed to fetch testimonial.'}</p>`;
      return;
    }

    const data = await response.json();
    const item = data.value[0];
    const author = item.Author;

    const authorPhotoUrl = author.EMail
      ? await this.getUserPhoto(client, author.EMail)
      : 'https://via.placeholder.com/120';

    const profileDetails = await this.getUserProfileDetails(author.EMail);
    const outlookLink = `mailto:${author.EMail}`;
    const teamsLink = `https://teams.microsoft.com/l/chat/0/0?users=${author.EMail}`;

    // Transform VideoLink to embed format
    let videoLink = item.VideoLink?.Url || '';
    console.log('Original VideoLink:', item.VideoLink?.Url, 'Transformed VideoLink:', videoLink); // Debug
    if (videoLink.includes('youtube.com/watch?v=')) {
      const videoId = videoLink.split('v=')[1]?.split('&')[0];
      videoLink = videoId ? `https://www.youtube.com/embed/${videoId}` : '';
    } else if (videoLink.includes('youtu.be/')) {
      const videoId = videoLink.split('youtu.be/')[1]?.split('?')[0];
      videoLink = videoId ? `https://www.youtube.com/embed/${videoId}` : '';
    }
    if (!videoLink || !videoLink.includes('embed') && !videoLink.includes('player.vimeo.com')) {
      videoLink = ''; // Skip invalid URLs
    }

    const html = `
      <style>
        .custom-accordion .accordion-item {
          border: none;
          border-radius: 10px;
          margin-bottom: 10px;
          background-color: #f9f9f9;
        }
        .custom-accordion .accordion-button {
          background-color: #fff;
          border-radius: 10px;
          font-weight: 500;
          box-shadow: none;
          padding-right: 3rem;
          position: relative;
        }
        .custom-accordion .accordion-button::after {
          content: '+';
          font-size: 24px;
          position: absolute;
          right: 20px;
          top: 50%;
          transform: translateY(-50%);
          transition: all 0.3s ease;
        }
        .custom-accordion .accordion-button:not(.collapsed)::after {
          content: '–';
        }
        .custom-accordion .accordion-body {
          background-color: #fff;
          border-radius: 8px;
          padding: 15px;
          color: #555;
          font-size: 15px;
          border: 1px solid #eee;
        }
        .accordion-button:not(.collapsed):after,
        .accordion-button:after {
          background-image: none !important;
        }
        .custom-modal {
          display: none;
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0,0,0,0.5);
          z-index: 10000;
          align-items: center;
          justify-content: center;
          direction: ${this._isArabic ? 'rtl' : 'ltr'};
        }
        .custom-modal-content {
          background: #fff;
          width: 80%;
          max-width: 800px;
          border-radius: 10px;
          padding: 20px;
        }
        .custom-modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        }
        .custom-modal-close {
          border: none;
          background: none;
          font-size: 20px;
          cursor: pointer;
        }
        .icons img {
          width: 24px;
          margin-right: 5px;
        }
      </style>
      <div class="container-fluid testimonials-details">
        <div class="row">
          <div class="col-lg-4 mb-4 filter-testimonails card card-color">
            <div class="text-center p-3">
              <img src="${authorPhotoUrl}" alt="Profile Photo" class="rounded-circle mb-2" width="120" height="120" style="object-fit: cover; border: 3px solid #d91f5d;">
              <h6 class="fw-bold mb-0">${author.Title || item.Title || ''}</h6>
              <small class="main-color fw-semibold">${author.JobTitle || item.JobTitle || ''}</small>
              <div class="mt-1 mb-2  text-color  small"><i class="bi bi-calendar-event"></i> ${new Date(author.Created || item.Created).toLocaleDateString()}</div>
              <!-- About Me Section -->
              <div class="text-start small mb-3">
                <p class="fw-bold mb-1">${this._isArabic ? 'عني' : 'About Me'}</p>
                <p>${profileDetails.aboutMe}</p>
              </div>
              <hr>
              <div class="text-start small">
                <p><strong>${this._isArabic ? 'القسم:' : 'Department:'}</strong> ${author.Department || item.Department || ''}</p>
                <hr>
                <p><strong>${this._isArabic ? 'الجنسية:' : 'Nationality:'}</strong> ${profileDetails.Nationality || ''}</p>
                <hr>
                <p><strong>${this._isArabic ? 'هواياتي:' : 'My hobbies:'}</strong><br> ${profileDetails.hobbies}</p>
              </div>
              <div class="icons text-start">
                <a href="${outlookLink}" target="_blank" class="text-decoration-none">
               <svg xmlns="http://www.w3.org/2000/svg" id="Message" width="26.291" height="25.249" viewBox="0 0 26.291 25.249">
                    <path id="Stroke_1" data-name="Stroke 1" d="M7.366,6.1c-1.484,0-3.279-.909-5.335-2.7A26.173,26.173,0,0,1-.524.828.96.96,0,1,1,.944-.409C2.44,1.365,5.508,4.18,7.366,4.18S12.261,1.368,13.74-.4A.96.96,0,0,1,15.215.824,25.818,25.818,0,0,1,12.682,3.4C10.64,5.189,8.852,6.1,7.366,6.1Z" transform="translate(5.794 8.641)" fill="#ed3c8a"></path>
                    <path id="Stroke_3" data-name="Stroke 3" d="M12.4-.75c5.007,0,8.148.869,10.184,2.818s2.962,4.985,2.962,9.807-.913,7.845-2.962,9.806S17.4,24.5,12.4,24.5,4.248,23.63,2.212,21.681-.75,16.7-.75,11.875.163,4.029,2.212,2.068,7.388-.75,12.4-.75Zm0,23.33c8.6,0,11.226-2.5,11.226-10.705S21,1.169,12.4,1.169,1.169,3.671,1.169,11.875,3.792,22.58,12.4,22.58Z" transform="translate(0.75 0.75)" fill="#ed3c8a"></path>
                  </svg>  
                </a>
                <a href="${teamsLink}" target="_blank" class="text-decoration-none">
               <svg xmlns="http://www.w3.org/2000/svg" width="26.722" height="26.721" viewBox="0 0 26.722 26.721">
                    <g id="chat-ro" transform="translate(1 1)">
                      <path id="Path_77853" data-name="Path 77853" d="M18.2,12.937h-.011a.969.969,0,1,1,0-1.937H18.2a.969.969,0,0,1,0,1.937Z" transform="translate(-0.887 0.392)" fill="#ee3c8a"></path>
                      <path id="Path_77853-2" data-name="Path 77853" d="M18.2,12.937h-.011a.969.969,0,1,1,0-1.937H18.2a.969.969,0,0,1,0,1.937Z" transform="translate(-5.729 0.392)" fill="#ee3c8a"></path>
                      <path id="Path_77853-3" data-name="Path 77853" d="M18.2,12.937h-.011a.969.969,0,1,1,0-1.937H18.2a.969.969,0,0,1,0,1.937Z" transform="translate(-10.572 0.392)" fill="#ee3c8a"></path>
                      <path id="Path_77854" data-name="Path 77854" d="M14.361,27.721a13.226,13.226,0,0,1-5.949-1.395,1.068,1.068,0,0,0-.741-.1l-2.759.738A2.568,2.568,0,0,1,2.2,26.043a2.557,2.557,0,0,1-.443-2.234l.738-2.759a1.059,1.059,0,0,0-.1-.741A13.363,13.363,0,0,1,14.361,1a13.361,13.361,0,0,1,9.447,22.808A13.273,13.273,0,0,1,14.361,27.721ZM7.9,24.263a3.1,3.1,0,0,1,1.373.33,11.426,11.426,0,0,0,16.51-10.232,11.424,11.424,0,0,0-19.5-8.078A11.433,11.433,0,0,0,4.128,19.446a2.991,2.991,0,0,1,.234,2.1L3.625,24.31a.626.626,0,0,0,.108.554.644.644,0,0,0,.505.256.672.672,0,0,0,.173-.024l2.758-.738A2.831,2.831,0,0,1,7.9,24.263Z" transform="translate(-2 -2)" fill="#ee3c8a"></path>
                    </g>
                  </svg>  
                </a>
                ${videoLink ? `
                  <a href="#" class="text-decoration-none video-link" data-video="${videoLink}">
<svg xmlns="http://www.w3.org/2000/svg" width="20.462" height="15.346" viewBox="0 0 20.462 15.346">
  <g id="Video" transform="translate(0.75 0.75)">
    <path id="Stroke_1" data-name="Stroke 1" d="M4.5,10.411a4.648,4.648,0,0,1-1.93-.653A21.571,21.571,0,0,1-.461,7.772.75.75,0,0,1-.592,6.72a.75.75,0,0,1,1.053-.13,15.034,15.034,0,0,0,3.8,2.279,20.406,20.406,0,0,0,.311-4.026A18.563,18.563,0,0,0,4.268.784,6.1,6.1,0,0,0,2.913,1.4,19.646,19.646,0,0,0,.466,3.054.75.75,0,0,1-.588,2.932.75.75,0,0,1-.466,1.879,20.083,20.083,0,0,1,2.553-.115c.954-.5,2.131-.979,2.769-.289.218.235.464.675.62,2.154a30.2,30.2,0,0,1,.133,3.12c0,1.139-.058,2.241-.152,3.1-.158,1.455-.383,1.875-.6,2.11A1.061,1.061,0,0,1,4.5,10.411Z" transform="translate(13.637 2.092)" fill="#fd5675"/>
    <path id="Stroke_3" data-name="Stroke 3" d="M6.9-.75c2.9,0,4.726.535,5.922,1.735s1.728,3.03,1.728,5.938-.533,4.738-1.728,5.938S9.8,14.6,6.9,14.6,2.174,14.061.978,12.861-.75,9.83-.75,6.923-.217,2.184.978.985,4-.75,6.9-.75ZM6.9,13.1c2.469,0,3.968-.4,4.859-1.294s1.291-2.4,1.291-4.879-.4-3.984-1.29-4.879S9.37.75,6.9.75s-3.968.4-4.86,1.294S.75,4.444.75,6.923s.4,3.983,1.291,4.879S4.432,13.1,6.9,13.1Z" transform="translate(0 0)" fill="#fd5675"/>
  </g>
</svg>                    </a>
                ` : ''}
              </div>
            </div>
          </div>
          <div class="col-lg-8 testimonails">
            <div class="accordion custom-accordion" id="testimonialAccordion">
              ${this.renderAccordionItem(item.Question1_EN?.Title || (this._isArabic ? 'السؤال 1' : 'Question 1'), item.Answer1_EN, 'q1', true)}
              ${this.renderAccordionItem(item.Question2_EN?.Title || (this._isArabic ? 'السؤال 2' : 'Question 2'), item.Answer2_EN, 'q2')}
              ${this.renderAccordionItem(item.Question3_EN?.Title || (this._isArabic ? 'السؤال 3' : 'Question 3'), item.Answer3_EN, 'q3')}
              ${this.renderAccordionItem(item.Question4_EN?.Title || (this._isArabic ? 'السؤال 4' : 'Question 4'), item.Answer4_EN, 'q4')}
            </div>
          </div>
        </div>
        <!-- Custom Video Modal -->
        <div id="customVideoModal" class="custom-modal">
          <div class="custom-modal-content">
            <div class="custom-modal-header">
              <h5>${this._isArabic ? 'فيديو الشهادة' : 'Testimonial Video'}</h5>
              <button class="custom-modal-close">${this._isArabic ? 'إغلاق' : 'Close'}</button>
            </div>
            <iframe id="videoFrame" src="" frameborder="0" allowfullscreen style="width: 100%; height: 400px;"></iframe>
          </div>
        </div>
      </div>
    `;

    this.domElement.innerHTML = html;
    this.addVideoLinkEvents();
  }

  private async getUserPhoto(client: any, userId: string): Promise<string> {
    try {
      const photoResponse = await client
        .api(`/users/${userId}/photo/$value`)
        .responseType(ResponseType.BLOB)
        .get();

      const photoBlob = new Blob([photoResponse], { type: 'image/jpeg' });
      return URL.createObjectURL(photoBlob);
    } catch (error) {
      console.error('Error fetching user photo:', error);
      return 'https://via.placeholder.com/120';
    }
  }

  private async getUserProfileDetails(email: string): Promise<{ hobbies: string, skills: string, aboutMe: string, hireDate: string, Nationality: string }> {
    try {
      const encodedEmail = encodeURIComponent(`i:0#.f|membership|${email}`);
      const profileResp = await this.context.spHttpClient.get(
        `${this.context.pageContext.web.absoluteUrl}/_api/SP.UserProfiles.PeopleManager/GetPropertiesFor(accountName='${encodedEmail}')`,
        SPHttpClient.configurations.v1
      );

      const profileData = await profileResp.json();
      const getProp = (key: string): string => {
        const prop = profileData.UserProfileProperties.find((p: any) => p.Key === key);
        return prop?.Value || '-';
      };

      return {
        hobbies: getProp("SPS-Interests"),
        skills: getProp("SPS-Skills"),
        aboutMe: getProp("AboutMe"),
        hireDate: getProp("SPS-HireDate"),
        Nationality: getProp('Nationality')
      };
    } catch (error) {
      console.error("Error fetching user profile details:", error);
      return {
        hobbies: '-',
        skills: '-',
        aboutMe: '-',
        hireDate: '-',
        Nationality: '-'
      };
    }
  }

  private renderAccordionItem(question: string, answer: string, targetId: string, expanded: boolean = false): string {
    const showClass = expanded ? 'show' : '';
    const collapsedClass = expanded ? '' : 'collapsed';

    return `
      <div class="accordion-item">
        <h2 class="accordion-header">
          <button class="accordion-button ${collapsedClass}" type="button" data-bs-toggle="collapse" data-bs-target="#${targetId}" aria-expanded="${expanded}">
            ${question}
          </button>
        </h2>
        <div id="${targetId}" class="accordion-collapse collapse ${showClass}" data-bs-parent="#testimonialAccordion">
          <div class="accordion-body">${answer || '—'}</div>
        </div>
      </div>
    `;
  }

  private addVideoLinkEvents(): void {
    const videoLinks = this.domElement.querySelectorAll('.video-link');
    console.log('Video links found:', videoLinks.length); // Debug
    videoLinks.forEach(link => {
      link.addEventListener('click', (event) => {
        event.preventDefault();
        event.stopPropagation();
        const videoUrl = link.getAttribute('data-video');
        console.log('Video URL:', videoUrl); // Debug
        const videoFrame = this.domElement.querySelector('#videoFrame') as HTMLIFrameElement;
        const modal = this.domElement.querySelector('#customVideoModal') as HTMLElement;
        if (videoUrl && videoFrame && modal) {
          videoFrame.src = videoUrl;
          modal.style.display = 'flex';
        } else {
          console.error('Modal or video frame not found, or invalid video URL:', { videoUrl, videoFrame, modal });
          alert(this._isArabic ? 'لم يتم العثور على المودل أو إطار الفيديو، أو رابط الفيديو غير صالح.' : 'Modal or video frame not found, or invalid video URL.');
        }
      });
    });

    // Close modal when clicking outside or on close button
    const modal = this.domElement.querySelector('#customVideoModal') as HTMLElement;
    modal?.addEventListener('click', (event) => {
      if (event.target === modal || (event.target as HTMLElement).classList.contains('custom-modal-close')) {
        modal.style.display = 'none';
        const videoFrame = this.domElement.querySelector('#videoFrame') as HTMLIFrameElement;
        if (videoFrame) {
          videoFrame.src = '';
        }
      }
    });
  }
}