import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';



export interface IOurPeopleWebPartProps {
  description: string;
}

export default class OurPeopleWebPart extends BaseClientSideWebPart<IOurPeopleWebPartProps> {

  private _isArabic: boolean = false;

  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;
    this.loadData();
  }

  private async loadData(): Promise<void> {
    const listUrl = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Our People')/items?$filter=Active eq '1'&$orderby=Order0 asc`;


    try {
      const response: SPHttpClientResponse = await this.context.spHttpClient.get(listUrl, SPHttpClient.configurations.v1);
      const data: any = await response.json();
      const items: any[] = data.value;

      const enrichedItems = await Promise.all(
        items.map(async item => {
          const attachments = await this.getAttachments(item.Id);
          item.Attachments = attachments;
          return item;
        })
      );

      let html = `
      <section class="news-section our-people h-100">  
        <div class="card section-card card-color h-100 p-3">  
          <div class="container-fluid">  
            <h2 class="fw-bold tittle head-color">${this._isArabic ? 'فريقنا' : 'Our People'}</h2>  
            <div class="row g-3">`;

      enrichedItems.forEach(item => {
        const title = this._isArabic ? item.Title_AR || '' : item.Title || '';
        const link = this._isArabic ? item.Link_AR?.Url || '#' : item.Link_EN?.Url || '#';

       
        let imageUrl: string | undefined = undefined;
        try {
          if (item.Image && typeof item.Image === 'string') {
            const imgData = JSON.parse(item.Image);
            if (imgData?.serverUrl && imgData?.serverRelativeUrl) {
              imageUrl = imgData.serverUrl + imgData.serverRelativeUrl;
            }
          } else if (item.Image) {
            imageUrl = item.Image;
          } else if (typeof item.Image === 'string') {
            imageUrl = item.Image;
          }
        } catch (e) {
          console.warn("Invalid ImageUrl format:", item.ImageUrl);
        }

        const fallbackImage = item.Attachments?.[0] || require('./assets/img.jpg');
        const image = imageUrl || fallbackImage;

        html += `
          <a href="${link}" class="col-lg-4 text-decoration-none">  
            <div class="border overflow-hidden d-flex flex-column justify-content-center align-items-center p-3">  
              <img src="${image}" alt="${title}" class="img-fluid">  
              <h3 class="head-color mt-3 text-center">${title}</h3>  
            </div>  
          </a>`;
      });

      html += `</div></div></div></section>`;
      this.domElement.innerHTML = html;

    } catch (error) {
      console.error("Error loading data or attachments:", error);
      this.domElement.innerHTML = `<p class="text-danger">خطأ أثناء تحميل البيانات.</p>`;
    }
  }

  private getAttachments(itemId: number): Promise<string[]> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Our People')/items(${itemId})/AttachmentFiles`;

    return this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => {
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        return response.json();
      })
      .then(data => data.value.map((file: any) => file.ServerRelativeUrl))
      .catch(error => {
        console.error('Error fetching attachments:', error);
        return [];
      });
  }
}
