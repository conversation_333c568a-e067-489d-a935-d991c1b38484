import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';

export interface IHomeCollectionWebPartProps {
  description: string;
}

export default class HomeCollectionWebPart extends BaseClientSideWebPart<IHomeCollectionWebPartProps> {
  private _isArabic: boolean = false;

  public render(): void {
    this._isArabic = window.location.pathname.toLowerCase().includes('/ar/');

    this.domElement.innerHTML = `
    <style>.vertical-carousel-inner {
  position: relative;
  height: 400px;
  overflow: hidden;
}

.vertical-carousel-item {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
  transition: transform 0.5s ease-in-out;
}
</style>
      <div class="col-lg-12">
        <section class="f4-news collection-section mt-4 mt-lg-0">
          <div class="card section-card card-color h-100 p-3">
            <div class="container-fluid">
              <h2 class="fw-bold tittle head-color">${this._isArabic ? "المجموعة" : "The Collection"}</h2>
              <div class="news-hero-secondary custom-vertical-carousel">
                <div class="vertical-carousel-inner" id="carouselItems">
                  <div class=" text-color  px-2 py-5">${this._isArabic ? "جاري التحميل..." : "Loading..."}</div>
                </div>
                <div class="vertical-carousel-arrows">
                  <button class="carousel-arrow-up" aria-label="Previous slide">
                    <i class="bi bi-arrow-up-circle"></i>
                  </button>
                  <button class="carousel-arrow-down" aria-label="Next slide">
                    <i class="bi bi-arrow-down-circle"></i>
                  </button>
                </div>
              </div>
              <div class="text-end mt-3 card-footer card-footer">
                <a href="${this._isArabic ? "https://collections.qm.org.qa/ar/objects" : "https://collections.qm.org.qa/en/objects"}" class="text-decoration-none fw-bold btn-main-link">
                  ${this._isArabic ? "عرض الكل " + '<i class="bi bi-arrow-left-circle"></i>' : "View All " + '<i class="bi bi-arrow-right-circle"></i>'}
                </a>
              </div>
            </div>
          </div>
        </section>
      </div>
    `;

    fetch('https://vcld.ws/qmproxy.php?filterOnDisplay=true&pageSize=5&page=1')
      .then(response => response.json())
      .then(data => {
        const items = data.results || [];
        const container = this.domElement.querySelector('#carouselItems');
        if (!container) return;

        container.innerHTML = '';

        items.forEach((item: any, index: number) => {
          const title = this._isArabic ? item.titleAR : item.titleEN;
          const slug = item.slug;
          const link = `https://collections.qm.org.qa/objects/${slug}`;
          const image = item.objectImages?.original?.[0]?.url;
          const itemId = `item-${index}`;

          if (image && title && slug) {
            container.innerHTML += `
              <div class="vertical-carousel-item ${index === 0 ? 'active' : ''}" id="${itemId}" style="transform: translateY(${index * 100}%);">
                <div class="news-hero-item news-overlay d-block">
                  <img src="${image}" alt="${title}">
                  <div class="news-hero-caption">
                    <a href="${link}" class="text-decoration-none text-white main-color-hover">
                      <h2>${title}</h2>
                      <small><i class="bi bi-eye main-color"></i> <span class="view-count" data-slug="${slug}">0</span></small>
                    </a>
                  </div>
                </div>
              </div>
            `;

            this._fetchViewCount(slug);

            setTimeout(() => {
              const el = this.domElement.querySelector(`#${itemId} a`);
              if (el) {
                el.addEventListener('click', (e) => {
                  e.preventDefault();
                  this._incrementViewCount(slug, title);
                });
              }
            }, 0);
          }
        });

        // ✅ Call after all items are rendered
        this._initializeCarousel();
      })
      .catch(err => {
        console.error('Fetch error:', err);
        const container = this.domElement.querySelector('#carouselItems');
        if (container)
          container.innerHTML = `<div class="text-danger">${this._isArabic ? "فشل تحميل البيانات" : "Failed to load data"}</div>`;
      });
  }

  private _initializeCarousel(): void {
    const upButton = this.domElement.querySelector('.carousel-arrow-up');
    const downButton = this.domElement.querySelector('.carousel-arrow-down');
    const items = this.domElement.querySelectorAll('.vertical-carousel-item');

    if (!items.length) return;

    let currentIndex = 0;

    const updateCarousel = (index: number) => {
      items.forEach((item, i) => {
        const translateY = (i - index) * 100;
        item.classList.toggle('active', i === index);
        (item as HTMLElement).style.transform = `translateY(${translateY}%)`;
      });
    };

    if (upButton) {
      upButton.addEventListener('click', () => {
        currentIndex = (currentIndex - 1 + items.length) % items.length;
        updateCarousel(currentIndex);
      });
    }

    if (downButton) {
      downButton.addEventListener('click', () => {
        currentIndex = (currentIndex + 1) % items.length;
        updateCarousel(currentIndex);
      });
    }

    updateCarousel(0);
  }

  private _incrementViewCount(slug: string, title: string): void {
    const listName = "CollectionViews";
    const siteUrl = this.context.pageContext.web.absoluteUrl;

    this.context.spHttpClient.post(`${siteUrl}/_api/contextinfo`, SPHttpClient.configurations.v1, {
      headers: { 'Accept': 'application/json' }
    })
      .then((res: SPHttpClientResponse) => res.json())
      .then((contextInfo: any) => {
        const formDigestValue = contextInfo?.FormDigestValue;

        fetch(`${siteUrl}/_api/web/lists/getbytitle('${listName}')/items?$filter=Slug eq '${slug}'`, {
          headers: { 'Accept': 'application/json;odata=verbose' }
        })
          .then(res => res.json())
          .then(data => {
            const items = data.d.results;
            if (items.length > 0) {
              const itemId = items[0].Id;
              const currentViews = items[0].Views || 0;

              fetch(`${siteUrl}/_api/web/lists/getbytitle('${listName}')/items(${itemId})`, {
                method: "POST",
                headers: {
                  "Accept": "application/json;odata=verbose",
                  "Content-Type": "application/json;odata=verbose",
                  "X-RequestDigest": formDigestValue,
                  "IF-MATCH": "*",
                  "X-HTTP-Method": "MERGE"
                },
                body: JSON.stringify({ __metadata: { type: "SP.Data.CollectionViewsListItem" }, Views: currentViews + 1 })
              }).then(() => {
                this._updateViewCountDisplay(slug, currentViews + 1);
                const linkElement = document.createElement('a');
                linkElement.href = `https://collections.qm.org.qa/objects/${slug}`;
                linkElement.target = '_blank';
                linkElement.click();
              });
            } else {
              fetch(`${siteUrl}/_api/web/lists/getbytitle('${listName}')/items`, {
                method: "POST",
                headers: {
                  "Accept": "application/json;odata=verbose",
                  "Content-Type": "application/json;odata=verbose",
                  "X-RequestDigest": formDigestValue
                },
                body: JSON.stringify({
                  __metadata: { type: "SP.Data.CollectionViewsListItem" },
                  Slug: slug,
                  Title: title,
                  Views: 1
                })
              }).then(() => {
                this._updateViewCountDisplay(slug, 1);
                const linkElement = document.createElement('a');
                linkElement.href = `https://collections.qm.org.qa/objects/${slug}`;
                linkElement.target = '_blank';
                linkElement.click();
              });
            }
          });
      });
  }

  private _updateViewCountDisplay(slug: string, updatedViewCount: number): void {
    const viewCountElement = this.domElement.querySelector(`.view-count[data-slug="${slug}"]`);
    if (viewCountElement) {
      viewCountElement.textContent = updatedViewCount.toString();
    }
  }

  private _fetchViewCount(slug: string): void {
    const listName = "CollectionViews";
    const siteUrl = this.context.pageContext.web.absoluteUrl;

    fetch(`${siteUrl}/_api/web/lists/getbytitle('${listName}')/items?$filter=Slug eq '${slug}'`, {
      headers: { 'Accept': 'application/json;odata=verbose' }
    })
      .then(res => res.json())
      .then(data => {
        const items = data?.d?.results || [];
        if (items.length > 0 && items[0].Views != null) {
          this._updateViewCountDisplay(slug, items[0].Views);
        }
      });
  }
}
