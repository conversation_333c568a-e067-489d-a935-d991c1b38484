import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';
import flatpickr from "flatpickr";
import "flatpickr/dist/flatpickr.min.css";

export interface IInternalVacanciesWebPartProps {
  description: string;
  customPageTitle?: string;
}

export default class InternalVacanciesWebPart extends BaseClientSideWebPart<IInternalVacanciesWebPartProps> {
  private newsItems: any[] = [];
  private totalItems: number = 0;
  private _isArabic: boolean = false;
  private currentPage: number = 1;
  private itemsPerPage: number = 10;
  private currentView: 'grid' | 'list' = 'list';
  private currentSortOrder: string = "Newest First";
  private selectedDepartment: string = '';
  private selectedNationalitie: string = '';
  private departments: any[] = [];
  private nationalities: any[] = [];

  // Initialize date pickers
  private initializeDatePickers(): void {
    const fromInput = this.domElement.querySelector(".from-date") as HTMLInputElement;
    const toInput = this.domElement.querySelector(".to-date") as HTMLInputElement;

    if (fromInput) {
      flatpickr(fromInput, {
        dateFormat: "Y-m-d",
        allowInput: true
      });
    }

    if (toInput) {
      flatpickr(toInput, {
        dateFormat: "Y-m-d",
        allowInput: true
      });
    }
  }

  // Add filter listeners
  private addFilterListeners(): void {
    const applyBtn = this.domElement.querySelector(".apply-filter");
    const resetBtn = this.domElement.querySelector(".reset-filter");
    const searchBtn = this.domElement.querySelector(".search-btn");

    // Department selection
    this.domElement.querySelectorAll(".department-option").forEach(item => {
      item.addEventListener("click", (e) => {
        e.preventDefault();
        const value = item.getAttribute("data-value") || "";
        this.selectedDepartment = value;
        this.updateDepartmentDisplay();
        const departmentOptions = this.domElement.querySelector("#departmentOptions");
        departmentOptions?.classList.remove("show");
        this.applyFilters();
      });
    });

    // Nationalitie selection
    this.domElement.querySelectorAll(".Nationalitie-option").forEach(item => {
      item.addEventListener("click", (e) => {
        e.preventDefault();
        const value = item.getAttribute("data-value") || "";
        this.selectedNationalitie = value;
        this.updateNationalitieDisplay();
        const NationalitieOptions = this.domElement.querySelector("#NationalitieOptions");
        NationalitieOptions?.classList.remove("show");
        this.applyFilters();
      });
    });

    // Apply filter
    if (applyBtn) {
      applyBtn.addEventListener("click", () => {
        this.applyFilters();
      });
    }

    // Reset filter
    if (resetBtn) {
      resetBtn.addEventListener("click", () => {
        const fromDateInput = this.domElement.querySelector(".from-date") as HTMLInputElement;
        const toDateInput = this.domElement.querySelector(".to-date") as HTMLInputElement;
        const positionInput = this.domElement.querySelector("#positionSearch") as HTMLInputElement;

        if (fromDateInput) fromDateInput.value = '';
        if (toDateInput) toDateInput.value = '';
        if (positionInput) positionInput.value = '';

        this.selectedDepartment = '';
        this.selectedNationalitie = '';
        this.updateDepartmentDisplay();
        this.updateNationalitieDisplay();

        this.currentPage = 1;
        this.applyFilters();
      });
    }

    // Search button
    if (searchBtn) {
      searchBtn.addEventListener("click", () => {
        this.applyFilters();
      });
    }
  }

  // Apply all filters and sorting
  private applyFilters(): void {
    const fromDate = (this.domElement.querySelector(".from-date") as HTMLInputElement)?.value || '';
    const toDate = (this.domElement.querySelector(".to-date") as HTMLInputElement)?.value || '';
    const position = (this.domElement.querySelector("#positionSearch") as HTMLInputElement)?.value.trim() || '';
    this.currentPage = 1;
    this.fetchNewsData(position, fromDate, toDate, this.selectedDepartment, this.selectedNationalitie);
  }

  // Update department display
  private updateDepartmentDisplay(): void {
    const displayElement = this.domElement.querySelector("#departmentDisplay");
    if (displayElement) {
      displayElement.textContent = this.selectedDepartment
        ? this.selectedDepartment
        : (this._isArabic ? 'الكل' : 'All');
    }
  }

  // Update Nationalitie display
  private updateNationalitieDisplay(): void {
    const displayElement = this.domElement.querySelector("#NationalitieDisplay");
    if (displayElement) {
      displayElement.textContent = this.selectedNationalitie
        ? this.selectedNationalitie
        : (this._isArabic ? 'الكل' : 'All');
    }
  }

  // Main render method
  public async render(): Promise<void> {
    try {
      this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;
      const isSmallScreen = window.innerWidth < 768;
      if (isSmallScreen) { this.currentView = 'grid'; }

      // Load departments and nationalities if not already loaded
      if (this.departments.length === 0) {
        this.departments = await this.getDepartments();
      }
      if (this.nationalities.length === 0) {
        this.nationalities = await this.getNationalities();
      }

      const isArabic = this._isArabic;
      // const backText = isArabic ? 'رجوع' : 'Back';

      // Apply sorting before displaying items
      this.sortNewsItems();

      // Generate news items HTML
      const newsItemsHtml = this.getPagedNewsItems().map(item => {
        const jobTitle = isArabic ? (item.JobTitle_AR || item.Title) : (item.Title || '');
        const department = isArabic ? (item.Department__x0650_AR || item.Department_EN) : (item.Department_EN || '');
        const date = this.formatDateSimple(item.PublishingDate);

        if (this.currentView === 'list') {
          return `
            <div class="col-md-6 col-lg-4">
              <div class="card vacancy-card h-100">
                <div class="border-accent"></div>
                <div class="card-body" onclick="window.location.href='/sites/intranet-qm/SitePages/vacancayDetails.aspx?viewID=${item.ID}'" style="cursor: pointer;">
                  <div class="job-info">
                    <h4 class="job-title head-color">${jobTitle}</h4>
                    <p class="department text-color">${department}</p>
                  </div>
                  <div class="date-container">
                    <i class="bi bi-calendar3 date-icon"></i>
                    <span class="date">${date}</span>
                  </div>
                </div>
              </div>
            </div>`;
        } else {
          return `
            <div class="col-md-6 col-lg-4">
              <div class="card vacancy-card h-100">
                <div class="border-accent"></div>
                <div class="p-2 flex-grow-1" onclick="window.location.href='/sites/intranet-qm/SitePages/vacancayDetails.aspx?viewID=${item.ID}'" style="cursor: pointer;">
                  <h5 class="fw-bold">${jobTitle}</h5>
                  <p class="department text-color">${department}</p>
                </div>
                <div class="date-container">
                  <i class="bi bi-calendar3 date-icon"></i>
                  <span class="date">${date}</span>
                </div>
              </div>
            </div>`;
        }
      }).join('');

      const paginationHtml = this.getPaginationHtml();
      // const referrer = document.referrer;
      // const referrerName = document.title;

      // Render the main HTML
      this.domElement.innerHTML = `
        <main id="main-content" class="internal-vacancies-page">
   
          
          <div class="row">
            <div class="col-lg-3 mt-3">
              <div class="card card-color mb-4">
                <div class="card-body">
                  <h3 class="fw-bold head-color mb-3">${this._isArabic ? 'الفلترة' : 'Filters'}</h3>
                  <hr>

                  <!-- Position Search -->
                  <div class="mb-3">
                    <label for="positionSearch" class="form-label">${this._isArabic ? 'الوظيفة' : 'Position'}</label>
                    <div class="input-group">
                      <input type="text" class="form-control" id="positionSearch" placeholder="${this._isArabic ? 'ابحث' : 'Search'}">
                      <button class="btn btn-outline-secondary search-btn" aria-label="Search" type="button">
                        <i class="bi bi-search"></i>
                      </button>
                    </div>
                  </div>

                  <!-- Department Dropdown -->
                  <div class="mb-3">
                    <label class="form-label head-color">${this._isArabic ? 'الإدارات' : 'Departments'}</label>
                    <div class="custom-select-container">
                      <button class="btn btn-outline-secondary border btn-sm w-100 text-start d-flex justify-content-between align-items-center dropdown-toggle" 
                        type="button" id="departmentToggle">
                        <span id="departmentDisplay">${this._isArabic ? 'الكل' : 'All'}</span>
                      </button>
                      <div class="custom-select-options" id="departmentOptions">
                        <a href="#" class="dropdown-item department-option" data-value="">${this._isArabic ? 'الكل' : 'All'}</a>
                        ${this.departments.map(dept => `
                          <a href="#" class="dropdown-item department-option" 
                            data-value="${this._isArabic ? (dept.TitleAR || dept.Title) : dept.Title}">
                            ${this._isArabic ? (dept.TitleAR || dept.Title) : dept.Title}
                          </a>
                        `).join('')}
                      </div>
                    </div>
                  </div>

                  <!-- Nationalitie Dropdown -->
                  <div class="mb-3">
                    <label class="form-label head-color">${this._isArabic ? 'الجنسية' : 'Nationalitie'}</label>
                    <div class="custom-select-container">
                      <button class="btn btn-outline-secondary border btn-sm w-100 text-start d-flex justify-content-between align-items-center dropdown-toggle" 
                        type="button" id="NationalitieToggle">
                        <span id="NationalitieDisplay">${this._isArabic ? 'الكل' : 'All'}</span>
                      </button>
                      <div class="custom-select-options" id="NationalitieOptions">
                        <a href="#" class="dropdown-item Nationalitie-option" data-value="">${this._isArabic ? 'الكل' : 'All'}</a>
                        ${this.nationalities.map(nat => `
                          <a href="#" class="dropdown-item Nationalitie-option" 
                            data-value="${this._isArabic ? (nat.TitleAR || nat.Title) : nat.Title}">
                            ${this._isArabic ? (nat.TitleAR || nat.Title) : nat.Title}
                          </a>
                        `).join('')}
                      </div>
                    </div>
                  </div>

                  <!-- Publish Date Range -->
                  <div class="mb-3">
                    <label class="form-label">${this._isArabic ? 'تاريخ النشر' : 'Publish Date'}</label>
                    <div class="input-group mb-2">
                      <input type="text" class="form-control from-date" placeholder="${this._isArabic ? 'من' : 'From'}" aria-label="From Date">
                      <span class="input-group-text"><i class="bi bi-calendar"></i></span>
                    </div>
                    <div class="input-group">
                      <input type="text" class="form-control to-date" placeholder="${this._isArabic ? 'إلى' : 'To'}" aria-label="To Date">
                      <span class="input-group-text"><i class="bi bi-calendar"></i></span>
                    </div>
                  </div>

                  <!-- Filter Buttons -->
                  <div class="d-grid gap-2 mt-4">
                    <button type="button" class="btn btn-main text-white fw-bold apply-filter">${this._isArabic ? 'تطبيق الفلتر' : 'Apply Filter'}</button>
                    <button type="button" class="btn btn-outline-dark fw-bold reset-filter">${this._isArabic ? 'إعادة تعيين' : 'Reset'}</button>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-9">
              <section class="new-list card card-color p-3 mt-3">
                <div class="d-flex justify-content-between align-items-center">
                  <h4 class="fw-bold head-color"><strong>${this._isArabic ? 'النتيجة الكلية:' : 'Total Result:'}</strong> ${this.totalItems}</h4>
                  <div class="align-items-center gap-2 flex-wrap view-btns mb-3">
                    <span class="text-color">${this._isArabic ? 'عرض' : 'View'}</span>
                    <button class="btn btn-outline-secondary btn-sm ${this.currentView === 'list' ? 'selected' : ''}"
                      id="listViewBtn" aria-label="listView" data-view="list">
                      <i class="bi bi-list"></i>
                    </button>
                    <button class="btn btn-outline-secondary btn-sm ${this.currentView === 'grid' ? 'selected' : ''}"
                      id="gridViewBtn" aria-label="gridView" data-view="grid">
                      <i class="bi bi-grid-3x3-gap"></i>
                    </button>
                    <div class="dropdown ms-2">
                      <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="sortDropdown"
                        data-bs-toggle="dropdown" aria-expanded="false">
                        ${this.getSortButtonText()}
                      </button>
                      <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                        <li><a class="dropdown-item" href="#" data-sort="Newest First">${this._isArabic ? 'الأحدث أولاً' : 'Newest First'}</a></li>
                        <li><a class="dropdown-item" href="#" data-sort="Oldest First">${this._isArabic ? 'الأقدم أولاً' : 'Oldest First'}</a></li>
                        <li><a class="dropdown-item" href="#" data-sort="Most Viewed">${this._isArabic ? 'الأكثر مشاهدة' : 'Most Viewed'}</a></li>
                      </ul>
                    </div>
                  </div>
                </div>
                <hr>
                <div class="row g-4 ${this.currentView === 'grid' ? 'grid-view' : 'list-view'}">
                  ${newsItemsHtml}
                </div>
                ${paginationHtml}
              </section>
            </div>
          </div>
        </main>
        
        <style>
          .custom-select-container {
            position: relative;
          }
          
          .custom-select-options {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1000;
            background: white;
            border: 1px solid #ddd;
            border-radius: 0.25rem;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
            max-height: 200px;
            overflow-y: auto;
          }
          
          .custom-select-options.show {
            display: block;
          }
          
          .custom-select-options .dropdown-item {
            display: block;
            padding: 0.5rem 1rem;
            clear: both;
            font-weight: 400;
            color: #212529;
            text-align: inherit;
            text-decoration: none;
            white-space: nowrap;
            background-color: transparent;
            border: 0;
          }
          
          .custom-select-options .dropdown-item:hover {
            background-color: #f8f9fa;
          }
          
          .custom-select-container .dropdown-toggle {
            cursor: pointer;
          }
        </style>`;

      // Initialize UI components
      this.initializeUIComponents();

      const sortItems = this.domElement.querySelectorAll('.dropdown-menu [data-sort]');
      sortItems.forEach(item => {
        item.addEventListener('click', (e) => {
          e.preventDefault();
          const order = item.getAttribute('data-sort');
          if (order) {
            this.changeSortOrder(order);
          }
        });
      });

    } catch (error) {
      console.error('Error in render method:', error);
      this.domElement.innerHTML = `
        <div class="alert alert-danger">
          ${this._isArabic ? 'حدث خطأ أثناء تحميل البيانات' : 'An error occurred while loading data'}
        </div>`;
    }
  }

  // Helper method to get sort button text
  private getSortButtonText(): string {
    switch (this.currentSortOrder) {
      case 'Newest First':
        return this._isArabic ? 'الترتيب حسب: تاريخ جديد' : 'Sort by: New Date';
      case 'Oldest First':
        return this._isArabic ? 'الترتيب حسب: تاريخ قديم' : 'Sort by: Old Date';
      case 'Most Viewed':
        return this._isArabic ? 'الترتيب حسب: الأكثر مشاهدة' : 'Sort by: Most Viewed';
      default:
        return this._isArabic ? 'الترتيب حسب' : 'Sort by';
    }
  }

  // Sort news items based on current sort order
  private sortNewsItems(): void {
    if (!this.newsItems || this.newsItems.length === 0) return;

    switch (this.currentSortOrder) {
      case 'Newest First':
        this.newsItems.sort((a, b) =>
          new Date(b.PublishingDate).getTime() - new Date(a.PublishingDate).getTime()
        );
        break;
      case 'Oldest First':
        this.newsItems.sort((a, b) =>
          new Date(a.PublishingDate).getTime() - new Date(b.PublishingDate).getTime()
        );
        break;
      case 'Most Viewed':
        this.newsItems.sort((a, b) => (b.ViewCount || 0) - (a.ViewCount || 0));
        break;
      default:
        this.newsItems.sort((a, b) =>
          new Date(b.PublishingDate).getTime() - new Date(a.PublishingDate).getTime()
        );
    }
  }

  // Initialize UI components
  private initializeUIComponents(): void {
    // Initialize dropdown toggles
    const departmentToggle = this.domElement.querySelector("#departmentToggle");
    const NationalitieToggle = this.domElement.querySelector("#NationalitieToggle");
    const departmentOptions = this.domElement.querySelector("#departmentOptions");
    const NationalitieOptions = this.domElement.querySelector("#NationalitieOptions");

    // Department dropdown
    if (departmentToggle && departmentOptions) {
      departmentToggle.addEventListener("click", (e) => {
        e.preventDefault();
        e.stopPropagation();
        departmentOptions.classList.toggle("show");
        NationalitieOptions?.classList.remove("show");
      });
    }

    // Nationality dropdown
    if (NationalitieToggle && NationalitieOptions) {
      NationalitieToggle.addEventListener("click", (e) => {
        e.preventDefault();
        e.stopPropagation();
        NationalitieOptions.classList.toggle("show");
        departmentOptions?.classList.remove("show");
      });
    }

    const dropdownElements = this.domElement.querySelectorAll('.dropdown-toggle');

    dropdownElements.forEach(dropdown => {
      dropdown.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Close all other dropdowns first
        this.closeAllDropdowns();

        // Get the associated dropdown menu
        // const dropdownId = dropdown.getAttribute('data-bs-toggle') === 'dropdown';
        const menu = dropdown.nextElementSibling as HTMLElement;

        if (menu && menu.classList.contains('dropdown-menu')) {
          // Toggle the current dropdown
          if (menu.classList.contains('show')) {
            this.closeDropdown(menu);
          } else {
            this.openDropdown(menu);
          }
        }
      });
    });

    // Initialize other components
    this.addPaginationEventListeners();
    this.addViewToggleListeners();
    this.initializeDatePickers();
    this.addFilterListeners();
  }
  private openDropdown(menu: HTMLElement): void {
    menu.classList.add('show');
    menu.setAttribute('aria-expanded', 'true');
    menu.previousElementSibling?.classList.add('show'); // For the toggle button
  }

  private closeDropdown(menu: HTMLElement): void {
    menu.classList.remove('show');
    menu.setAttribute('aria-expanded', 'false');
    menu.previousElementSibling?.classList.remove('show'); // For the toggle button
  }

  private closeAllDropdowns(): void {
    const openMenus = this.domElement.querySelectorAll('.dropdown-menu.show');
    openMenus.forEach(menu => {
      this.closeDropdown(menu as HTMLElement);
    });
  }
  // View toggle listeners
  private addViewToggleListeners(): void {
    const listViewBtn = this.domElement.querySelector('#listViewBtn');
    const gridViewBtn = this.domElement.querySelector('#gridViewBtn');

    if (listViewBtn) {
      listViewBtn.addEventListener('click', () => {
        this.currentView = 'list';
        this.render();
      });
    }

    if (gridViewBtn) {
      gridViewBtn.addEventListener('click', () => {
        this.currentView = 'grid';
        this.render();
      });
    }
  }

  // Pagination event listeners
  private addPaginationEventListeners(): void {
    const pageLinks = this.domElement.querySelectorAll('.page-item .page-link');

    pageLinks.forEach((link: any) => {
      link.addEventListener('click', (event: Event) => {
        event.preventDefault();
        const pageText = link.textContent.trim().toLowerCase();
        if (pageText === 'next' || pageText === 'التالي') {
          this.changePage(this.currentPage + 1);
        } else if (pageText === 'previous' || pageText === 'السابق') {
          this.changePage(this.currentPage - 1);
        } else {
          const pageNumber = parseInt(pageText);
          if (!isNaN(pageNumber)) {
            this.changePage(pageNumber);
          }
        }
      });
    });
  }

  // Handle responsive view
  private handleResponsiveView(): void {
    const isSmallScreen = window.innerWidth < 768;

    if (isSmallScreen && this.currentView !== 'grid') {
      this.currentView = 'grid';
      this.render();
    }
  }

  // Initialize web part
  protected onInit(): Promise<void> {
    return super.onInit().then(() => {
      window.addEventListener('resize', this.handleResponsiveView.bind(this));
      this.handleResponsiveView();
      return this.fetchNewsData();
    });
  }

  // Fetch news data
  private fetchNewsData(searchText: string = '', fromDate?: string, toDate?: string, department?: string, Nationalitie?: string): Promise<void> {
    let filters: string[] = [];

    if (searchText) {
      const escapedSearch = searchText.replace(/'/g, "''");
      filters.push(`(substringof('${escapedSearch}', Title) or substringof('${escapedSearch}', JobTitle_AR))`);
    }

    if (department) {
      const cleanedDept = encodeURIComponent(department.trim().replace(/\r?\n|\r/g, '').replace(/'/g, "''"));
      filters.push(`(Department_EN/Title eq '${cleanedDept}' or Department__x0650_AR/Title eq '${cleanedDept}')`);

    }

    if (Nationalitie) {
      const escapedNationality = Nationalitie.replace(/\r?\n|\r/g, '').replace(/'/g, "''").trim();
      filters.push(`(Nationalitie_EN/Title eq '${escapedNationality}' or Nationalitie_AR/Title eq '${escapedNationality}')`);

    }

    const filterQuery = filters.length > 0 ? `$filter=${filters.join(' and ')}` : '';

    const selectFields = [
      '*',
      'Department_EN/Title',
      'Department__x0650_AR/Title',
      'Nationalitie_EN/Title',
      'Nationalitie_AR/Title',
    ].join(',');

    const expandFields = [
      'Department_EN',
      'Department__x0650_AR',
      'Nationalitie_EN',
      'Nationalitie_AR',
    ].join(',');

    let url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('CommINT')/items?`;
    url += `$select=${selectFields}`;
    url += `&$expand=${expandFields}`;

    if (filterQuery) {
      url += `&${filterQuery}`;
    }

    return this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => response.json())
      .then((data) => {
        let filteredData = data.value.map((item: any) => ({
          ...item,
          Department_EN: item.Department_EN?.Title || '',
          Department__x0650_AR: item.Department__x0650_AR?.Title || '',
          Nationalitie_EN: item.Nationalitie_EN?.Title || '',
          Nationalitie_AR: item.Nationalitie_AR?.Title || '',
          ViewCount: item.ViewCount || 0
        }));

        // Apply client-side date filtering
        if (fromDate) {
          const from = new Date(fromDate);
          from.setHours(0, 0, 0, 0);
          filteredData = filteredData.filter((item: any) => {
            const itemDate = new Date(item.PublishingDate);
            return itemDate >= from;
          });
        }

        if (toDate) {
          const to = new Date(toDate);
          to.setHours(23, 59, 59, 999);
          filteredData = filteredData.filter((item: any) => {
            const itemDate = new Date(item.PublishingDate);
            return itemDate <= to;
          });
        }

        this.newsItems = filteredData;
        this.totalItems = filteredData.length;
        this.render();
      })
      .catch(error => {
        console.error('Error fetching news data:', error);
        this.domElement.innerHTML = `
          <div class="alert alert-danger">
            ${this._isArabic ? 'حدث خطأ أثناء جلب البيانات' : 'Error fetching data'}
            <br>${error.message}
          </div>`;
      });
  }

  // Format date
  private formatDateSimple(date: string): string {
    try {
      if (!date) return '';
      const parsedDate = new Date(date);
      if (isNaN(parsedDate.getTime())) return '';

      const year = parsedDate.getFullYear();
      const month = (parsedDate.getMonth() + 1).toString().padStart(2, '0');
      const day = parsedDate.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  }

  // Get paged news items
  private getPagedNewsItems(): any[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    return this.newsItems.slice(startIndex, startIndex + this.itemsPerPage);
  }

  // Get pagination HTML
  private getPaginationHtml(): string {
    const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
    if (totalPages <= 1) return '';

    const pageNumbers = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Previous button
    pageNumbers.push(`
      <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
        <a class="page-link rounded-pill px-3" href="#" aria-label="Previous">
          <i class="bi bi-arrow-left"></i> ${this._isArabic ? 'السابق' : 'Previous'}
        </a>
      </li>
    `);

    // First page
    if (startPage > 1) {
      pageNumbers.push(`
        <li class="page-item ${this.currentPage === 1 ? 'active' : ''}">
          <a class="page-link" href="#">1</a>
        </li>
      `);
      if (startPage > 2) {
        pageNumbers.push('<li class="page-item disabled"><span class="page-link">...</span></li>');
      }
    }

    // Middle pages
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(`
        <li class="page-item ${this.currentPage === i ? 'active' : ''}">
          <a class="page-link" href="#">${i}</a>
        </li>
      `);
    }

    // Last page
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pageNumbers.push('<li class="page-item disabled"><span class="page-link">...</span></li>');
      }
      pageNumbers.push(`
        <li class="page-item ${this.currentPage === totalPages ? 'active' : ''}">
          <a class="page-link" href="#">${totalPages}</a>
        </li>
      `);
    }

    // Next button
    pageNumbers.push(`
      <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
        <a class="page-link px-3" href="#" aria-label="Next">
          ${this._isArabic ? 'التالي' : 'Next'} <i class="bi bi-arrow-right"></i>
        </a>
      </li>
    `);

    return `
      <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center flex-wrap gap-1">
          ${pageNumbers.join('')}
        </ul>
      </nav>
    `;
  }

  // Change sort order
  private changeSortOrder(order: string): void {
    this.currentSortOrder = order;
    this.sortNewsItems();
    this.currentPage = 1;
    this.render();
  }

  // Change page
  private changePage(pageNumber: number): void {
    const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
    if (pageNumber > 0 && pageNumber <= totalPages) {
      this.currentPage = pageNumber;
      this.render();
    }
  }

  // Get departments
  private async getDepartments(): Promise<{ Title: string, TitleAR: string }[]> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Departments')/items?$select=Title,TitleAR`;

    try {
      const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
      const data = await response.json();
      return data.value || [];
    } catch (error) {
      console.error('Error fetching departments:', error);
      return [];
    }
  }

  // Get nationalities
  private async getNationalities(): Promise<{ Title: string, TitleAR: string }[]> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('Nationalities')/items?$select=Title,TitleAR`;

    try {
      const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
      const data = await response.json();
      return data.value || [];
    } catch (error) {
      console.error('Error fetching nationalities:', error);
      return [];
    }
  }
}