import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { HttpClient, IHttpClientOptions, HttpClientResponse } from '@microsoft/sp-http';

export interface IMazayaDetailsWebPartProps {
  description: string;
}

interface Offer {
  id: string;
  title: string;
  description: string;
  howToUse: string;
  bannerUrl: string;
  amountPercentage: number;
  expiryDate: string;
  startDate: string;
  categoryId: string;
  supplier: { logo: string; name: string, id: string };
}

export default class MazayaDetailsWebPart extends BaseClientSideWebPart<IMazayaDetailsWebPartProps> {
  private token: string | null = null;
  private offer: Offer | null = null;
  private relatedOffers: Offer[] = [];

  public async render(): Promise<void> {
    const offerId = this.getOfferIdFromQuery();

    if (!offerId) {
      this.domElement.innerHTML = `<h3 class="text-danger">Offer ID not found in URL.</h3>`;
      return;
    }

    this.domElement.innerHTML = `<div>Loading offer details...</div>`;
    await this.login();

    if (this.token) {
      await this.fetchOfferDetails(offerId);
      if (this.offer) {
        await this.fetchRelatedOffers();
      } else {
        console.warn("⚠️ Offer has no categoryId. Skipping related offers.");
      }

      this.renderUI();
    } else {
      this.domElement.innerHTML = `<div class="text-danger">Failed to authenticate</div>`;
    }
  }

  getOfferIdFromQuery(): string | null {
    const params = new URLSearchParams(window.location.search);
    return params.get("offerId");
  }

  private async login(): Promise<void> {
    const url = 'https://mazaya-backend.qm.org.qa/api/Account/login';
    const body = JSON.stringify({
      username: '<EMAIL>',
      password: 'Qatar@2025'
    });

    const options: IHttpClientOptions = {
      headers: { 'Content-Type': 'application/json' },
      body
    };

    const resp: HttpClientResponse = await this.context.httpClient.post(url, HttpClient.configurations.v1, options);
    if (resp.ok) {
      const data = await resp.json();
      this.token = data.accessToken || data;
    }
  }

  private async fetchOfferDetails(id: string): Promise<void> {
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`
    };

    const resp = await this.context.httpClient.get(
      `https://mazaya-backend.qm.org.qa/api/Offers/${id}`,
      HttpClient.configurations.v1,
      { headers }
    );

    if (resp.ok) {
      const json = await resp.json();
      this.offer = json.data;
    } else {
      this.offer = null;
    }
  }

  private async fetchRelatedOffers(): Promise<void> {
    if (!this.offer) return;

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`
    };

    const body = JSON.stringify({
      pageNumber: 1,
      pageSize: 100,
      search: {
        supplierId: this.offer.supplier?.id || null,
        name: null
      },
      orderBy: '',
      isDescending: true
    });

    const options: IHttpClientOptions = { headers, body };

    const resp = await this.context.httpClient.post(
      'https://mazaya-backend.qm.org.qa/api/Offers/PaginatedList',
      HttpClient.configurations.v1,
      options
    );

    if (resp.ok) {
      const json = await resp.json();
      const items = json.data.items;
      const now = new Date();

      this.relatedOffers = items
        .filter((o: Offer) =>
          o.id !== this.offer!.id &&
          o.expiryDate &&
          new Date(o.expiryDate) > now
        )
        .slice(0, 5);

    }
  }


  private renderUI(): void {
    if (!this.offer) {
      this.domElement.innerHTML = `<div class="text-danger">Offer not found.</div>`;
      return;
    }

    const o = this.offer;

    const relatedHTML = this.relatedOffers.map(r => `
  <div class="p-2">
    <div class="card card-color rounded-4 overflow-hidden h-100">
      <div class="position-relative">
        <a href="?offerId=${r.id}">
          <img src="${r.bannerUrl || require('./assets/img.jpg')}" class="w-100 object-fit-cover rounded-4" style="height:180px;" alt="${r.title}" />
          <span class="position-absolute bottom-0 start-0 px-3 py-2 bg-dark bg-opacity-75 text-white w-100" style="font-size:1.5rem; font-weight:600;">
            ${r.amountPercentage}%
          </span>
        </a>
      </div>
      <div class="d-flex justify-content-between align-items-center px-3 py-2">
        <div class="d-flex align-items-center gap-2">
          <img class="rounded-5 p-1" src="${r.supplier.logo}" alt="${r.supplier.name}" style="height:28px;" />
          <span class="fw-bold head-color" style="font-size:1.1rem;">${r.title}</span>
        </div>
        ${r.expiryDate ? `<span class="small text-color">EX Date <span class="text-pink fw-bold">${new Date(r.expiryDate).toLocaleDateString()}</span></span>` : ''}
      </div>
    </div>
  </div>
`).join('');


    this.domElement.innerHTML = `
    <div class="row">
      <div class="col-md-8">
        <section class="offer-details mb-4">
          <div class="card card-color p-4 rounded-3 shadow-sm">
            <div class="row">
              <div class="col-lg-4">
                <h2 class="h5 head-color mb-3">Offer details</h2>
                <div class="d-flex list-details">
                  <div class="w-100">
                    <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                      <span class="fs-5 text-color">Offer Name:</span>
                      <span class="fw-bold fs-5 head-color">${o.title}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                      <span class="fs-5 text-color">Brand Name:</span>
                      <span class="fw-bold fs-5 head-color">${o.supplier.name}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center py-2">
                      <span class="fs-5 text-color">Brand Logo:</span>
                      <img class="rounded-5 p-1 brand-pic" src="${o.supplier.logo}" alt="${o.supplier.name} Logo" style="max-width: 80px; max-height: 50px;" />
                    </div>
                    <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                      <span class="fs-5 text-color">Offer Expire Date:</span>
                      <span class="fw-bold fs-5 head-color">${new Date(o.expiryDate).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-lg-8">
                <h3 class="h5 head-color">Description</h3>
                <p class="text-color">${o.description}</p>
               
                
              </div>
            </div>
          </div>
        </section>
      </div>
      <div class="col-md-4 related-offers">
        <div class="card-color card section-card">
          <h2 class="fw-bold head-color p-3 tittle">Related Offers</h2>
          ${relatedHTML}
        </div>
      </div>
    </div>
    `;
  }
}
