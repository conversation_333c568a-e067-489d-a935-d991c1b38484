import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';




import flatpickr from "flatpickr";
import "flatpickr/dist/flatpickr.min.css";

export interface IInternalVacanciesWebPartProps {
  description: string;
  customPageTitle?: string;
}

export default class InternalVacanciesWebPart extends BaseClientSideWebPart<IInternalVacanciesWebPartProps> {



  private newsItems: any[] = [];
  allNewsItems: any[] = [];
  private totalItems: number = 0;
  private _isArabic: boolean = false;
  private currentPage: number = 1;
  private itemsPerPage: number = 10;
  private currentView: 'grid' | 'list' = 'grid';
  currentSortOrder: string = "Newest First";
  private initializeDatePickers(): void {
    const fromInput = this.domElement.querySelector(".form-control[aria-label='From Date']") as HTMLInputElement;
    const toInput = this.domElement.querySelector(".form-control[aria-label='To Date']") as HTMLInputElement;

    if (fromInput) {
      flatpickr(fromInput, {
        dateFormat: "Y-m-d"
      });
    }

    if (toInput) {
      flatpickr(toInput, {
        dateFormat: "Y-m-d"
      });
    }
  }
  private addFilterListeners(): void {
    const fromDateInput = this.domElement.querySelector("input[aria-label='From Date']") as HTMLInputElement;
    const toDateInput = this.domElement.querySelector("input[aria-label='To Date']") as HTMLInputElement;
    const applyBtn = this.domElement.querySelector(".btn.btn-main");
    const resetBtn = this.domElement.querySelector(".btn.btn-outline-dark");
    const positionInput = this.domElement.querySelector("#positionSearch") as HTMLInputElement;

    const departmentBtn = this.domElement.querySelector("#departmentsDropdownBtn") as HTMLButtonElement;
    const nationalityBtn = this.domElement.querySelector("#nationalityDropdownBtn") as HTMLButtonElement;

    // 🟡 Department dropdown
    const departmentItems = this.domElement.querySelectorAll("#departmentsDropdownMenu .dropdown-item");
    departmentItems.forEach(item => {
      item.addEventListener("click", (e) => {
        e.preventDefault();
        const value = item.getAttribute("data-value");
        departmentBtn.querySelector("span")!.textContent = value!;
      });
    });

    // 🟡 Nationality dropdown
    const nationalityItems = this.domElement.querySelectorAll("#nationalityDropdownMenu .dropdown-item");
    nationalityItems.forEach(item => {
      item.addEventListener("click", (e) => {
        e.preventDefault();
        const value = item.getAttribute("data-value");
        nationalityBtn.querySelector("span")!.textContent = value!;
      });
    });

    // 🟢 Apply filter
    if (applyBtn) {
      applyBtn.addEventListener("click", () => {
        const fromDate = fromDateInput?.value || '';
        const toDate = toDateInput?.value || '';
        const position = positionInput?.value.trim() || '';

        const departmentValue = departmentBtn?.querySelector("span")?.textContent;
        const department: any = departmentValue === 'Select' || departmentValue === 'اختر' ? '' : departmentValue;

        const nationalityValue = nationalityBtn?.querySelector("span")?.textContent;
        const nationality: any = nationalityValue === 'Select' || nationalityValue === 'اختر' ? '' : nationalityValue;

        this.fetchNewsData(position, fromDate, toDate, department, nationality);
      });
    }

    // 🔁 Reset filter
    if (resetBtn) {
      resetBtn.addEventListener("click", () => {
        fromDateInput.value = '';
        toDateInput.value = '';
        positionInput.value = '';
        departmentBtn.querySelector("span")!.textContent = this._isArabic ? 'اختر' : 'Select';
        nationalityBtn.querySelector("span")!.textContent = this._isArabic ? 'اختر' : 'Select';

        this.fetchNewsData();
      });
    }
  }






  public render(): void {


    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;
    const isSmallScreen = window.innerWidth < 768; if (isSmallScreen) { this.currentView = 'grid'; }









    const isArabic = this._isArabic;
    const backText = isArabic ? 'رجوع' : 'Back';

    const newsItemsHtml = this.getPagedNewsItems().map(item => {
      if (this.currentView === 'list') {
        // const cardDirection = isArabic ? 'flex-row-reverse' : 'flex-row';
        return `
       
         <div class="col-md-6 col-lg-4">
           <div class="card vacancy-card h-100">
           <div class="border-accent"></div>
             <div class="card-body">
               <div class="job-info">
               <h4 class="job-title head-color">${isArabic ? item.JobTitle_AR || '' : item.Title || ''}</h4>
               <p class="department text-color">${isArabic ? item.Department_AR || '' : item.Department_EN || ''}</p>
               </div>
               <div class="date-container">
              <i class="bi bi-calendar3 date-icon"></i>
              <span class="date">${this.formatDateSimple(item.PublishingDate)}</span>
              </div>
           </div>
         </div>
       </div>`;
      } else {
        return `
         <div class="col-md-6 col-lg-4">
         <div class="card vacancy-card h-100">
           <div class="border-accent"></div>
           <div class="p-2 flex-grow-1">
             <h5 class="fw-bold">${isArabic ? item.JobTitle_AR : item.Title}</h5>
              <p class="department text-color">${isArabic ? item.Department_AR || '' : item.Department_EN || ''}</p>
              </div>
              <div class="date-container">
              <i class="bi bi-calendar3 date-icon"></i>
              <span class="date">${this.formatDateSimple(item.PublishingDate)}</span>
              </div>
            </div>
          
 
             
          
        
       </div>`;
      }
    }).join('');

    const paginationHtml = this.getPaginationHtml();
    const referrer = document.referrer;

    if (referrer) {
      const refUrl = new URL(referrer);
      const serverRelative = refUrl.pathname;

      this.getPageTitle(serverRelative).then(title => {
        this.domElement.querySelector('#referrerName')!.textContent = title;
      });

    }

    const referrerName = document.title






    this.domElement.innerHTML = `
 
      <main id="main-content" class="internal-vacancies-page">
           <section class="bread-cramp mt-3">
             <div class="card card-color">
               <div class="card-body d-flex justify-content-between align-items-center flex-wrap">
                 <h2 class="fw-bold m-0 head-color">${referrerName}</h2>
                 <div class="d-flex align-items-center gap-3">
                   <nav aria-label="breadcrumb">
                     <ol class="breadcrumb m-0">
                       <li class="breadcrumb-item">
                         <a href="/" class="btn-main-link fw-bold">Home</a>
                       </li>
                       <li class="breadcrumb-item">
                         <a href="${referrer}" class="btn-main-link fw-bold">${referrerName}</a>
                       </li>
 
                     </ol>
                   </nav>
                   <button class="btn btn-outline-dark d-flex align-items-center gap-1" onclick="history.back()">
                     <i class="bi bi-arrow-left"></i> ${backText}
                   </button>
                 </div>
               </div>
             </div>
           </section>
           <div class="row">
             <div class="col-lg-3">
  <div class="card card-color mb-4">
    <div class="card-body">
      <h3 class="fw-bold head-color mb-3">${this._isArabic ? 'الفلترة' : 'Filters'}</h3>
      <hr>

      <!-- Position Search -->
      <div class="mb-3">
        <label for="positionSearch" class="form-label">${this._isArabic ? 'الوظيفة' : 'Position'}</label>
        <div class="input-group">
          <input type="text" class="form-control" id="positionSearch" placeholder="${this._isArabic ? 'ابحث' : 'Search'}">
          <button class="btn btn-outline-secondary" type="button">
            <i class="bi bi-search"></i>
          </button>
        </div>
      </div>

      

      <!-- Departments Dropdown -->
<div class="mb-3">
  <label class="form-label head-color ">${this._isArabic ? 'الإدارات' : 'Departments'}</label>
  <div class="dropdown w-100">
    <button class="btn btn-outline-secondary border btn-sm dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center" type="button" id="departmentsDropdownBtn" data-bs-toggle="dropdown" aria-expanded="false">
      <span>${this._isArabic ? 'اختر' : 'Select'}</span>
    </button>
    <ul class="dropdown-menu w-100" id="departmentsDropdownMenu">
      <li><a class="dropdown-item" data-value="Information Management &amp; Decision Support" href="#">Information Management & Decision Support</a></li>
      <li><a class="dropdown-item" data-value="Learning and Outreach" href="#">Learning and Outreach</a></li>
      <li><a class="dropdown-item" data-value="CEO Office" href="#">CEO Office</a></li>
      <li><a class="dropdown-item" data-value="Human Resources" href="#">Human Resources</a></li>
      <li><a class="dropdown-item" data-value="Finance" href="#">Finance</a></li>
    </ul>
  </div>
</div>

<!-- Nationality Dropdown -->
<div class="mb-3">
  <label class="form-label head-color">${this._isArabic ? 'الجنسية' : 'Nationality'}</label>
  <div class="dropdown w-100">
    <button class="btn btn-outline-secondary border btn-sm dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center" type="button" id="nationalityDropdownBtn" data-bs-toggle="dropdown" aria-expanded="false">
      <span>${this._isArabic ? 'اختر' : 'Select'}</span>
    </button>
    <ul class="dropdown-menu w-100" id="nationalityDropdownMenu">
      <li><a class="dropdown-item" data-value="Qatari" href="#">Qatari</a></li>
      <li><a class="dropdown-item" data-value="Non-Qatari" href="#">Non-Qatari</a></li>
    </ul>
  </div>
</div>


      <!-- Publish Date Range -->
      <div class="mb-3">
        <label class="form-label">${this._isArabic ? 'تاريخ النشر' : 'Publish Date'}</label>
        <div class="input-group mb-2">
          <input type="text" class="form-control datepicker flatpickr-input" placeholder="${this._isArabic ? 'من' : 'From'}" aria-label="From Date">
          <span class="input-group-text"><i class="bi bi-calendar"></i></span>
        </div>
        <div class="input-group">
          <input type="text" class="form-control datepicker flatpickr-input" placeholder="${this._isArabic ? 'إلى' : 'To'}" aria-label="To Date">
          <span class="input-group-text"><i class="bi bi-calendar"></i></span>
        </div>
      </div>

      <!-- Filter Buttons -->
      <div class="d-grid gap-2 mt-4">
        <button type="button" class="btn btn-main text-white fw-bold">${this._isArabic ? 'تطبيق الفلتر' : 'Apply Filter'}</button>
        <button type="button" class="btn btn-outline-dark fw-bold">${this._isArabic ? 'إعادة تعيين' : 'Reset'}</button>
      </div>
    </div>
  </div>
</div>

             <div class="col-lg-9">
 
 
               <section class="new-list card card-color p-3 mt-3">
                 <div class="d-flex justify-content-between align-items-center">
                   <h4 class="fw-bold head-color"><strong>${this._isArabic ? 'النتيجة الكلية:' : 'Total Result:'}</strong> ${this.totalItems}</h4>
                   <div class=" align-items-center gap-2 flex-wrap view-btns mb-3">
                     <span class="text-color">${this._isArabic ? 'عرض' : 'View'}</span>
                     <button class="btn btn-outline-secondary btn-sm ${this.currentView === 'list' ? 'selected' : ''}"
                       id="listViewBtn" aria-label="listView" data-view="list">
                       <i class="bi bi-list"></i>
                     </button>
                     <button class="btn btn-outline-secondary btn-sm ${this.currentView === 'grid' ? 'selected' : ''}"
                       id="gridViewBtn" aria-label="gridView" data-view="grid">
                       <i class="bi bi-grid-3x3-gap"></i>
                     </button>
                     <div class="dropdown ms-2">
                       <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="sortDropdown"
                         data-bs-toggle="dropdown" aria-expanded="false">
                         ${this._isArabic ? 'الترتيب حسب: تاريخ جديد' : 'Sort by: New Date'}
                       </button>
                        <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                         <li><a class="dropdown-item" href="#" data-sort="Newest First">${this._isArabic ? 'الأحدث أولاً' : 'Newest First'}</a></li>
                         <li><a class="dropdown-item" href="#" data-sort="Oldest First">${this._isArabic ? 'الأقدم أولاً' : 'Oldest First'}</a></li>
                         <li><a class="dropdown-item" href="#" data-sort="Most Viewed">${this._isArabic ? 'الأكثر مشاهدة' : 'Most Viewed'}</a></li>
                       </ul>
                     </div>
                   </div>
                 </div>
                 <hr>
                 ${this.currentView === 'grid' ? `
                 <div class="row g-4 grid-view">
                   ${newsItemsHtml}
                 </div>
                 ` : `
                 <div class="row g-4 list-view">
                 ${newsItemsHtml}
                  </div>
                 `}
                 ${paginationHtml}
               </section>
             </div>
           </div>
         </div>
       </main>
       `;

    this.addPaginationEventListeners();
    this.addViewToggleListeners();
    this.initializeDatePickers();
    this.addFilterListeners();
    const sortDropdownBtn = this.domElement.querySelector('#sortDropdown');
    const sortMenu = this.domElement.querySelector('.dropdown-menu');

    sortDropdownBtn?.addEventListener('click', () => {
      sortMenu?.classList.toggle('show');
    });
    const sortItems = this.domElement.querySelectorAll('.dropdown-menu [data-sort]');
    sortItems.forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        const order = item.getAttribute('data-sort');
        if (order) {
          this.changeSortOrder(order);
        }
      });
    });


  }

  private addViewToggleListeners(): void {
    const listViewBtn = this.domElement.querySelector('#listViewBtn');
    const gridViewBtn = this.domElement.querySelector('#gridViewBtn');


    if (listViewBtn) {
      listViewBtn.addEventListener('click', () => {
        this.currentView = 'list';
        this.render();
      });
    }

    if (gridViewBtn) {
      gridViewBtn.addEventListener('click', () => {
        this.currentView = 'grid';
        this.render();
      });
    }


  }

  private addPaginationEventListeners(): void {
    const pageLinks = this.domElement.querySelectorAll('.page-item .page-link');

    pageLinks.forEach((link: any) => {
      link.addEventListener('click', (event: Event) => {
        event.preventDefault();
        const pageText = link.textContent.trim().toLowerCase();
        if (pageText === 'next') {
          this.changePage(this.currentPage + 1);
        } else if (pageText === 'previous') {
          this.changePage(this.currentPage - 1);
        } else {
          const pageNumber = parseInt(pageText);
          this.changePage(pageNumber);
        }
      });
    });
  }
  private handleResponsiveView(): void {
    const isSmallScreen = window.innerWidth < 768;

    if (isSmallScreen && this.currentView !== 'grid') {
      this.currentView = 'grid';
      this.render();
    }
  }


  protected onInit(): Promise<void> {
    return super.onInit().then(() => {


      window.addEventListener('resize', this.handleResponsiveView.bind(this));
      this.handleResponsiveView();
      return this.fetchNewsData();
    });
  }

  private fetchNewsData(searchText: string = '', fromDate?: string, toDate?: string, department?: string, nationality?: string): Promise<void> {
    let filters: string[] = [];

    if (searchText) {
      const escapedSearch = searchText.replace(/'/g, "''");
      filters.push(`(substringof('${escapedSearch}', Title) or substringof('${escapedSearch}', JobTitle_AR))`);
    }

    if (department) {
      filters.push(`Department_EN eq '${department}' or Department_AR eq '${department}'`);
    }

    if (nationality) {
      filters.push(`Nationality eq '${nationality}'`);
    }

    const filterQuery = filters.length > 0 ? `&$filter=${filters.join(' and ')}` : '';

    let orderQuery = '';
    switch (this.currentSortOrder) {
      case 'Newest First':
        orderQuery = '$orderby=PublishingDate desc';
        break;
      case 'Oldest First':
        orderQuery = '$orderby=PublishingDate asc';
        break;
      case 'Most Viewed':
        orderQuery = '$orderby=ViewCount desc';
        break;
      default:
        orderQuery = '$orderby=PublishingDate desc';
    }

    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('CommINT')/items?${orderQuery}${filterQuery}`;

    return this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => response.json())
      .then((data) => {
        let filteredData = data.value;

        // Apply client-side date filter
        if (fromDate) {
          const from = new Date(fromDate);
          from.setHours(0, 0, 0, 0);
          filteredData = filteredData.filter((item: any) => new Date(item.PublishingDate) >= from);
        }

        if (toDate) {
          const to = new Date(toDate);
          to.setDate(to.getDate() + 1);
          to.setHours(0, 0, 0, 0);
          filteredData = filteredData.filter((item: any) => new Date(item.PublishingDate) < to);
        }

        this.newsItems = filteredData;
        this.totalItems = filteredData.length;
        this.render();
      })
      .catch(error => {
        console.error('Error fetching news data:', error);
      });
  }







  private formatDateSimple(date: string): string {
    const parsedDate = new Date(date);
    const year = parsedDate.getFullYear();
    const month = (parsedDate.getMonth() + 1).toString();
    const day = parsedDate.getDate().toString();
    return `${year}-${month}-${day}`;
  }

  private getPagedNewsItems(): any[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    return this.newsItems.slice(startIndex, startIndex + this.itemsPerPage);
  }

  private getPaginationHtml(): string {
    const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
    const pageNumbers = [];

    for (let i = 1; i <= totalPages; i++) {
      pageNumbers.push(`
         <li class="page-item ${this.currentPage === i ? 'active' : ''}">
           <a class="page-link" href="#">${i}</a>
         </li>
       `);
    }

    return `
       <nav aria-label="Page navigation" class="mt-4">
         <ul class="pagination justify-content-center flex-wrap gap-1">
           <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
             <a class="page-link rounded-pill px-3" href="#" aria-label="Previous">
               <i class="bi bi-arrow-left"></i> Previous
             </a>
           </li>
           ${pageNumbers.join('')}
           <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
             <a class="page-link px-3" href="#" aria-label="Next">
               Next <i class="bi bi-arrow-right"></i>
             </a>
           </li>
         </ul>
       </nav>
     `;
  }


  changeSortOrder(order: string): void {
    const sortButton = this.domElement.querySelector('#sortDropdown');
    if (sortButton) {
      sortButton.textContent = this._isArabic
        ? (order === 'Newest First' ? 'الترتيب حسب: تاريخ جديد' :
          order === 'Oldest First' ? 'الترتيب حسب: تاريخ قديم' :
            'الترتيب حسب: الأكثر مشاهدة')
        : `Sort by: ${order}`;
    }

    this.currentSortOrder = order;
    this.currentPage = 1;
    this.fetchNewsData();
  }
  private changePage(pageNumber: number): void {
    const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
    if (pageNumber > 0 && pageNumber <= totalPages) {
      this.currentPage = pageNumber;
      this.fetchNewsData();
    }

  }
  async getPageTitle(serverRelativeUrl: string): Promise<string> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/getfilebyserverrelativeurl('${serverRelativeUrl}')/ListItemAllFields?$select=Title`;

    const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
    const data = await response.json();

    return data?.Title || '';
  }
}
