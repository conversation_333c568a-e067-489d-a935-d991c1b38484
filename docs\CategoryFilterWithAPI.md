# فلترة العروض بـ API الفئات - الطريقة النهائية

## الفكرة الأساسية
لما يختار المستخدم category، نعمل request على endpoint منفصل لجلب الـ suppliers بتوع الفئة دي، وبعدين نفلتر العروض بناءً على الـ suppliers دول.

## الـ Endpoint المستخدم
```
https://mazaya-backend.qm.org.qa/api/Suppliers/Categoires/{categoryID}
```

## كيفية العمل

### 1. اختيار الفئة
```javascript
public setCategoryFilter(categoryId: string): void {
  this.selectedCategory = categoryId;
  this.currentPage = 1;
  
  // جلب الـ suppliers الخاصة بالفئة أولاً
  this.fetchCategorySuppliers(categoryId).then(() => {
    // ثم جلب العروض مع الفلتر
    this.fetchOffers().then(() => {
      this.renderUI();
    });
  });
}
```

### 2. جلب الـ Suppliers للفئة
```javascript
private async fetchCategorySuppliers(categoryId: string): Promise<void> {
  const url = `https://mazaya-backend.qm.org.qa/api/Suppliers/Categoires/${categoryId}`;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${this.token}`
  };

  const response = await this.context.httpClient.get(url, HttpClient.configurations.v1, { headers });
  
  if (response.ok) {
    const data = await response.json();
    console.log('Category suppliers response:', data);
    
    // حفظ الـ suppliers الخاصة بالفئة
    this.categorySuppliersIds = this.extractSupplierIds(data);
    console.log('Extracted supplier IDs for category:', this.categorySuppliersIds);
  }
}
```

### 3. استخراج IDs الـ Suppliers
```javascript
private extractSupplierIds(data: any): string[] {
  // تحقق من بنية البيانات المختلفة
  if (Array.isArray(data)) {
    return data.map((supplier: any) => supplier.id || supplier.supplierId || supplier.Id).filter((id: any) => id);
  }
  
  if (data.data && Array.isArray(data.data)) {
    return data.data.map((supplier: any) => supplier.id || supplier.supplierId || supplier.Id).filter((id: any) => id);
  }
  
  if (data.suppliers && Array.isArray(data.suppliers)) {
    return data.suppliers.map((supplier: any) => supplier.id || supplier.supplierId || supplier.Id).filter((id: any) => id);
  }
  
  return [];
}
```

### 4. فلترة العروض
```javascript
private filterOffersByCategory(offers: any[]): any[] {
  if (!this.selectedCategory || this.categorySuppliersIds.length === 0) {
    return offers;
  }

  console.log('Filtering offers by category suppliers...');
  console.log('Category suppliers IDs:', this.categorySuppliersIds);

  const filteredOffers = offers.filter(offer => {
    if (!offer.supplier) return false;

    // جرب طرق مختلفة للحصول على supplier ID من العرض
    const offerSupplierId = offer.supplier.id || offer.supplier.supplierId || offer.supplier.Id;
    
    if (offerSupplierId) {
      const isMatch = this.categorySuppliersIds.includes(offerSupplierId.toString());
      
      if (isMatch) {
        console.log(`✓ Match found: ${offer.title} - Supplier ID: ${offerSupplierId}`);
      }
      
      return isMatch;
    }

    return false;
  });

  console.log('Filtered offers count:', filteredOffers.length);
  return filteredOffers;
}
```

### 5. تطبيق الفلترة في fetchOffers
```javascript
const resp: HttpClientResponse = await this.context.httpClient.post(url, HttpClient.configurations.v1, options);
if (resp.ok) {
  const data = await resp.json();
  let offers = data.data.items || [];
  
  console.log('Offers fetched from API:', offers.length);
  
  // تطبيق فلتر الفئة إذا كان محدد
  if (this.selectedCategory) {
    offers = this.filterOffersByCategory(offers);
    console.log('Offers after category filtering:', offers.length);
  }
  
  this.offers = offers;
  this.totalOffers = data.data.totalPages;
}
```

## دوال الاختبار والتشخيص

### 1. اختبار فلتر الفئة
```javascript
window.mazayaWebPart.testCategoryFilter('category-id-123');
```

**النتيجة المتوقعة:**
```
=== Testing Category Filter ===
Category ID: category-id-123
Fetching suppliers for category: category-id-123
Category suppliers response: [...]
Extracted supplier IDs for category: ['supplier1', 'supplier2', 'supplier3']
Offers fetched from API: 50
Filtering offers by category suppliers...
✓ Match found: Toyota Discount - Supplier ID: supplier1
✓ Match found: BMW Service - Supplier ID: supplier2
Filtered offers count: 8

=== Test Results ===
Selected category: category-id-123
Category suppliers IDs: ['supplier1', 'supplier2', 'supplier3']
Filtered offers count: 8
Sample filtered offers:
1. Toyota Discount - Supplier: Toyota Motors
2. BMW Service - Supplier: BMW Qatar
3. Car Rental Deal - Supplier: Hertz
```

### 2. عرض معلومات الفلتر الحالي
```javascript
window.mazayaWebPart.showCurrentFilter();
```

### 3. مسح الفلتر
```javascript
window.mazayaWebPart.clearCategoryFilter();
```

## مميزات النظام الجديد

### ✅ دقة عالية
- اعتماد على API رسمي للربط بين الفئات والـ suppliers
- مفيش تخمين أو mapping يدوي
- البيانات محدثة من قاعدة البيانات

### ✅ أداء محسن
- request واحد لجلب suppliers الفئة
- فلترة محلية سريعة للعروض
- تخزين مؤقت للـ supplier IDs

### ✅ مرونة
- يدعم بنية بيانات مختلفة للـ suppliers
- يتعامل مع أخطاء الـ API بشكل صحيح
- يمكن توسيعه بسهولة

### ✅ تشخيص شامل
- logging مفصل لكل خطوة
- دوال اختبار متقدمة
- معلومات واضحة عن النتائج

## سيناريوهات الاستخدام

### 1. المستخدم يختار فئة من الصفحة الرئيسية
```javascript
// في FeaturedDealsWebPart
const mazayaUrl = `/sites/intranet-qm/SitePages/Mazaya.aspx`;
window.location.href = mazayaUrl;

// في صفحة Mazaya
window.mazayaWebPart.setCategoryFilter('automobile-category-id');
```

### 2. فلترة مباشرة من الكود
```javascript
// تطبيق فلتر فئة معينة
window.mazayaWebPart.setCategoryFilter('food-category-id');

// مسح الفلتر
window.mazayaWebPart.clearCategoryFilter();
```

### 3. اختبار وتشخيص
```javascript
// اختبار شامل
window.mazayaWebPart.testCategoryFilter('electronics-category-id');

// عرض الحالة الحالية
window.mazayaWebPart.showCurrentFilter();
```

## معالجة الأخطاء

### إذا فشل جلب الـ Suppliers
```javascript
if (!response.ok) {
  console.error('Failed to fetch category suppliers:', response.statusText);
  this.categorySuppliersIds = [];
  // العروض ستظهر بدون فلترة
}
```

### إذا مفيش suppliers للفئة
```javascript
if (this.categorySuppliersIds.length === 0) {
  console.log('No suppliers found for category, showing all offers');
  return offers; // بدون فلترة
}
```

### إذا مفيش عروض مطابقة
```javascript
if (filteredOffers.length === 0) {
  console.log('No offers found for this category');
  // يمكن عرض رسالة للمستخدم
}
```

## الخلاصة

النظام الجديد:
- ✅ **دقيق**: يستخدم API رسمي للربط
- ✅ **سريع**: فلترة محلية بعد جلب الـ suppliers
- ✅ **موثوق**: معالجة شاملة للأخطاء
- ✅ **قابل للاختبار**: دوال تشخيص متقدمة

الآن لما يختار المستخدم category، النظام هيعمل request على الـ endpoint ويجيب الـ suppliers الصحيحين! 🚀
