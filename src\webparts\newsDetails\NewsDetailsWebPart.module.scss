@import '~@microsoft/sp-office-ui-fabric-core/dist/sass/SPFabricCore.scss';

.newsDetails {
  overflow: hidden;
  padding: 1em;
  color: "[theme:bodyText, default: #323130]";
  color: var(--bodyText);
  &.teams {
    font-family: $ms-font-family-fallbacks;
  }
}

.welcome {
  text-align: center;
}

.welcomeImage {
  width: 100%;
  max-width: 420px;
}

.links {
  a {
    text-decoration: none;
    color: "[theme:link, default:#03787c]";
    color: var(--link); // note: CSS Custom Properties support is limited to modern browsers only

    &:hover {
      text-decoration: underline;
      color: "[theme:linkHovered, default: #014446]";
      color: var(--linkHovered); // note: CSS Custom Properties support is limited to modern browsers only
    }
  }
}

// Media Slider Styles
.newsDetails {
  .newsDetailsImg {
    max-height: 400px;
    object-fit: cover;
  }

  .mediaContainer {
    position: relative;
    margin-bottom: 1.5rem;
  }

  .newsDetailsMedia {
    max-height: 500px;
    object-fit: cover;
    border-radius: 0.5rem;
  }

  .singleMedia {
    position: relative;
    display: inline-block;
    width: 100%;
  }

  .imageContainer, .videoContainer {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;

    &:hover .imageOverlay {
      opacity: 1;
    }
  }

  .imageOverlay, .videoOverlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 0.5rem;
  }

  .videoOverlay {
    opacity: 1;
    background: rgba(0, 0, 0, 0.5);

    .playButton {
      background: rgba(255, 255, 255, 0.9);
      border: none;
      border-radius: 50%;
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: white;
        transform: scale(1.1);
      }

      i {
        font-size: 2.5rem;
        color: #0078d4;
        margin-left: 4px;
      }
    }
  }

  .zoomBtn {
    opacity: 0.8;
    transition: opacity 0.3s ease;

    &:hover {
      opacity: 1;
    }
  }

  // Carousel Enhancements
  .carousel {
    .carouselIndicators {
      bottom: -40px;

      button {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin: 0 4px;
        background-color: rgba(255, 255, 255, 0.5);
        border: 2px solid transparent;

        &.active {
          background-color: #0078d4;
          border-color: #0078d4;
        }
      }
    }

    .carouselControlPrev, .carouselControlNext {
      width: 50px;
      height: 50px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      top: 50%;
      transform: translateY(-50%);
      opacity: 0;
      transition: opacity 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.7);
      }

      .carouselControlPrevIcon, .carouselControlNextIcon {
        width: 24px;
        height: 24px;
      }
    }

    &:hover {
      .carouselControlPrev, .carouselControlNext {
        opacity: 1;
      }
    }
  }

  .mediaCounter {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    z-index: 10;
  }

  // Video Specific Styles
  video {
    width: 100%;
    height: auto;
    max-height: 500px;

    &:focus {
      outline: 2px solid #0078d4;
      outline-offset: 2px;
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    .newsDetailsMedia {
      max-height: 300px;
    }

    .videoOverlay .playButton {
      width: 60px;
      height: 60px;

      i {
        font-size: 2rem;
      }
    }

    .mediaCounter {
      top: 10px;
      right: 10px;
      padding: 4px 8px;
      font-size: 0.75rem;
    }
  }
}