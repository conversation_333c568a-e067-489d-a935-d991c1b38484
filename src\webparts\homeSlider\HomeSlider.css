:root {
    --pink: #ed3b88;
    --orange: #FFC200;
    --blue: #1292E5;
    --black: #000;
    --white: #fff;
    --gray: #cfcdcd;
    --main-color: var(--pink);
    --bg-color: #F7F7F7;
    --text-color: #333;
    --border-color: var(--gray);
    --head-color: var(--black);
    --card-color: white;
    --selection-border: #333;
    --wb-color: #333;
    --bw-color: #fff;
    /* --bs-btn-active-bg: var(--main-color); */
    --bs-border-color: var(--border-color);
    --bs-nav-tabs-link-active-border-color: var(--border-color);
  
  }
  
  .head-color {
    color: var(--black);
  }
  
  .card {
    border-color: var(--border-color) !important;
  }
  
  .card-color {
    background-color: var(--card-color);
  }
  
  .main-color-hover:hover {
    color: var(--main-color);
  }
  
  
  
  .border-color {
    border-color: var(--border-color) !important;
  }
  
  .dark-theme {
    --bg-color: #1B1C1E;
    --text-color: #71737F;
    --border-color: #71737F;
    --head-color: var(--white);
    --card-color: #292A2C;
    --selection-border: var(--white);
    --wb-color: #fff;
    --bw-color: #333;
  
    .head-color {
        color: var(--white);
        --card-color: white
    }
  
  }
  
  h3 {
    font-size: 1.1rem;
  }
  
  .btn-outline-primary {
    --bs-btn-color: var(--main-color);
    --bs-btn-border-color: var(--main-color);
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: var(--main-color);
    --bs-btn-hover-border-color: var(--main-color);
    --bs-btn-focus-shadow-rgb: 13, 110, 253;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: var(--main-color);
    --bs-btn-active-border-color: var(--main-color);
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: var(--main-color);
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: var(--main-color);
  
  }
  
  .side-news,
  .maing-news {
    overflow: hidden;
  }
  
  .side-news:hover img {
    transform: scale(1.1);
    transition: all 0.3s ease;
    transform-origin: center;
  }
  
  .main-news {
    height: 285px;
  
  }
  
  .main-news img {
    height: 285px;
    object-fit: cover;
    width: 100%;
  }
  
  .main-news:hover img {
    transform: scale(1.1);
    transition: all 0.3s ease;
    transform-origin: center;
  }
  
  .btn-outline-secondary.selected {
    background-color: var(--wb-color) !important;
    color: var(--bw-color) !important;
    border-color: var(--bw-color) !important;
  }
  
  .btn-outline-dark {
    color: var(--wb-color) !important;
    border-color: var(--wb-color) !important;
    background-color: transparent !important;
    transition: all 0.3s ease;
  
    &:hover {
        background-color: var(--wb-color) !important;
        color: var(--bw-color) !important;
        border-color: var(--bw-color) !important;
    }
  }
  
  .bg-main {
    background-color: var(--main-color) !important;
  }
  
  .bg-main-50 {
  
    background-color: color-mix(in srgb, var(--main-color) 50%, transparent) !important;
  
  }
  
  .bg-main-30 {
  
    background-color: color-mix(in srgb, var(--main-color) 30%, transparent) !important;
  
  }
  
  .text-primary,
  .main-color {
    color: var(--main-color) !important;
  }
  
  hr {
  
    /* border-top: var(--bs-border-width) solid; */
    border-color: var(--selection-border);
  }
  
  #themeBtn {
    position: fixed;
  
    top: 20%;
    left: 0;
    z-index: 999;
    cursor: pointer;
    display: flex;
    justify-content: center;
    padding: 10px;
  }
  
  .teams-btn {
    position: fixed;
    bottom: 5%;
    right: 0;
    z-index: 999;
    cursor: pointer;
    display: flex;
    justify-content: center;
    padding: 10px;
  }
  
  body {
    background-color: var(--bg-color);
    color: var(--text-color);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    font-family: 'din', sans-serif;
  }
  
  body.canvas-active {
    overflow: hidden;
  }
  
  main {
    flex-grow: 1;
  }
  
  .text-color {
    color: var(--text-color);
  }
  
  .setting-section {
    .dropdown-panel {
        position: fixed;
        bottom: 72px;
        left: 0;
        z-index: 1055;
        /* Higher than Bootstrap carousel (which uses 1050) */
        width: 100%;
        transition: transform 0.3s ease-in-out;
  
    }
  
    .dropdown-panel .panel-container {
  
  
        border-radius: 5px;
        padding: 10px;
  
    }
  
    .dropdown-panel {
        position: fixed;
        top: 0;
        left: 0;
        height: 100%;
        width: 320px;
        background-color: var(--card-color);
        z-index: 1055;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
        display: flex;
        flex-direction: column;
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.2);
    }
  
    .dropdown-panel.show {
        transform: translateX(0);
    }
  
    .color-circle {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        cursor: pointer;
        /* border: 2px solid #fff; */
        box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
    }
  }
  
  
  
  .bg-pink {
    background-color: var(--pink);
  }
  
  .bg-orange {
    background-color: var(--orange);
  }
  
  .bg-blue {
    background-color: var(--blue);
  }
  
  /* @font-face {
    font-family: 'din';
    src: url('DINNextLTPro-Regular.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  } */
  
  .theme-choice {
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 6px;
    border: 1px solid transparent;
    transition: all 0.2s;
  }
  
  .theme-choice.selected {
    border-color: var(--main-color);
    background-color: var(--main-color);
    color: #fff;
  }
  
  /* ===== */
  
  
  
  
  .black-hover:hover {
    background-color: var(--black);
    color: var(--white);
  }
  
  
  
  .translate-top {
    transform: translate(-50%, -100%) !important;
  }
  
  .translate-bottom {
    transform: translateY(50%);
  }
  
  
  .btn-main {
    color: var(--white);
    background-color: var(--main-color) !important;
    transition: opacity 0.3s ease;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
  
  }
  
  
  .bg-main:hover {
    opacity: 0.7;
    /* adjust as needed */
  }
  
  .btn-main:active {
    background-color: var(--dark-gray);
    color: var(--light-gray);
  }
  
  .btn-main:focus {
    outline: none;
    box-shadow: 0 0 0 3px var(--light-gray);
  }
  
  .btn-main-link {
    text-decoration: none;
    color: var(--main-color);
    font-weight: bold;
  
  }
  
  .btn-main-link:hover {
    color: var(--head-color);
  }
  
  .btn-main-link:focus {
    outline: none;
    text-decoration: none;
  }
  
  .btn-main-link:active {
    border: none;
  }
  
  .btn-check:checked+.btn,
  .btn.active,
  .btn.show,
  .btn:first-child:active,
  :not(.btn-check)+.btn:active {
    color: color-mix(in srgb, var(--main-color) 50%, transparent) !important;
  }
  
  .btn {
  
    --bs-btn-font-family: 'din', sans-serif;
  }
  
  
  /* Icon Cheat Sheet Styles */
  .icon-cheat-sheet {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    overflow-y: auto;
    z-index: 1050;
  }
  
  .icon-demo-box {
    transition: all 0.2s ease;
  }
  
  .icon-demo-box:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
  }
  
  .icon-demo-box i {
    transition: transform 0.3s ease;
  }
  
  .icon-demo-box:hover i {
    transform: scale(1.2);
  }
  
  
  
  #fireworks-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1039;
    /* Behind the modal but above everything else */
  }
  
  @keyframes explode {
    0% {
        transform: scale(0.5);
        opacity: 1;
    }
  
    70% {
        transform: scale(2);
        opacity: 0.7;
    }
  
    100% {
        transform: scale(3);
        opacity: 0;
    }
  }
  
  
  .bg-gray {
    background-color: var(--black);
    color: var(--gray);
  }
  
  
  
  
 
  /* ===================== 
       Header Start
  ====================== */
  .responsive-menu {
    display: flex;
  }
  
  .nav-link {
    color: var(--head-color);
    font-weight: 700;
  }
  
  .nav-link:focus,
  .nav-link:hover,
  .nav-link.active,
  .nav-link.show {
    color: var(--main-color);
  }
  
  .navbar-nav .nav-link.active,
  .navbar-nav .nav-link.show {
    color: var(--main-color);
  }
  
  .logo-w {
    display: none;
  }
  
  .dark-theme .logo-b {
    display: none;
  }
  
  .dark-theme .logo-w {
    display: block;
  }
  
  /* ===================== 
  Home Slider
  ====================== */
 
  
  .dark-theme .slider-over {
  
    background-color: #000000e0;
  }
  
  .carousel-control-next,
  .carousel-control-prev {
  
    width: 5%;
    opacity: .6;
  }
  
  .carousel-control-next-icon,
  .carousel-control-prev-icon {
    background-color: var(--black);
    padding: 10px;
    border-radius: 5px;
  }
  .slider-over {
    right: 5% !important;
    left: auto !important;
    bottom: 50% !important;
    transform: translateY(50%) !important;
    background-color: #ffffffe0;
    border-top-left-radius: 20% !important;
    max-width: 500px !important;
    padding: 44px;
  }
  /* ===================== 
  Home Slider
  ====================== */


  

  
 
  
  