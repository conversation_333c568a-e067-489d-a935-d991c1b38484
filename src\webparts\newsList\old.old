import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';




import flatpickr from "flatpickr";
import "flatpickr/dist/flatpickr.min.css";

// import './news.css';

export interface INewsListWebPartProps {
  description: string;
  customPageTitle?: string;
}

export default class NewsListWebPart extends BaseClientSideWebPart<INewsListWebPartProps> {
  private newsItems: any[] = [];
  allNewsItems: any[] = [];
  private originalNewsItems: any[] = [];
  private totalItems: number = 0;
  private _isArabic: boolean = false;
  private currentPage: number = 1;
  private itemsPerPage: number = 10;
  private currentView: 'grid' | 'list' = 'grid';
  currentSortOrder: string = "Newest First";
  private _currentSiteUrl: string = '';
  private initializeDatePickers(): void {
    const fromInput = this.domElement.querySelector(".form-control[aria-label='From Date']") as HTMLInputElement;
    const toInput = this.domElement.querySelector(".form-control[aria-label='To Date']") as HTMLInputElement;

    if (fromInput) {
      flatpickr(fromInput, {
        dateFormat: "Y-m-d"
      });
    }

    if (toInput) {
      flatpickr(toInput, {
        dateFormat: "Y-m-d"
      });
    }
  }
  private addFilterListeners(): void {
    const searchInput = this.domElement.querySelector("input[aria-label='Search']") as HTMLInputElement;
    const fromDateInput = this.domElement.querySelector("input[aria-label='From Date']") as HTMLInputElement;
    const toDateInput = this.domElement.querySelector("input[aria-label='To Date']") as HTMLInputElement;
    const form = this.domElement.querySelector("form");

    if (form) {
      form.addEventListener('submit', (event) => {
        event.preventDefault();

        const searchText = searchInput?.value.trim().toLowerCase() || '';
        const fromDate = fromDateInput?.value ? new Date(fromDateInput.value) : null;
        const toDate = toDateInput?.value ? new Date(toDateInput.value) : null;
        if (!searchText && !fromDate && !toDate) {
          this.newsItems = [...this.originalNewsItems];
          this.totalItems = this.newsItems.length;
          this.currentPage = 1;
          this.render();
          return;
        }

        let filtered = [...this.newsItems];

        if (searchText) {
          filtered = filtered.filter(item =>
            item.Title.toLowerCase().includes(searchText) ||
            (item.Title_AR?.toLowerCase()?.includes(searchText))
          );
        }


        if (fromDate || toDate) {
          filtered = filtered.filter(item => {
            const newsDate = new Date(item.NewsDate);
            if (fromDate && newsDate < fromDate) return false;
            if (toDate && newsDate > toDate) return false;
            return true;
          });
        }

        this.totalItems = filtered.length;
        this.currentPage = 1;
        this.newsItems = filtered;
        this.fetchNewsData(searchText, fromDateInput?.value, toDateInput?.value);
      });

      const resetBtn = form.querySelector("button[type='reset']");
      if (resetBtn) {
        resetBtn.addEventListener('click', (event) => {
          event.preventDefault();
          this.currentPage = 1;
          this.fetchNewsData();
        });
      }
    }
  }



  public render(): void {


    this._isArabic = window.location.pathname.toLowerCase().indexOf('/ar/') !== -1;
    const isSmallScreen = window.innerWidth < 768; if (isSmallScreen) { this.currentView = 'grid'; } const
      featuredNews = this.originalNewsItems?.find(item => item.IsFeatured);
    const homepageNews: any = this.originalNewsItems
      ?.filter(item => item.ShowOnHomepage && !item.IsFeatured)
      .sort((a, b) => {
        const dateA = new Date(a.Date).getTime();
        const dateB = new Date(b.Date).getTime();
        return dateB - dateA;
      })
      .slice(0, 3);





    const featuredHtml = featuredNews ? `
      <a href="${this._currentSiteUrl}/SitePages/NewsDetails.aspx?newsid=${featuredNews.Id}" class="news-hero-main news-overlay">
        <img src="${featuredNews.Image}" alt="${featuredNews.Title}" class="img-fluid h-100 w-100">
        <div class="news-hero-caption p-3">
          <h2>${this._isArabic ? featuredNews.Title_AR : featuredNews.Title}</h2>
          <div class="news-meta d-flex justify-content-between mt-2">
            <span><i class="bi bi-calendar3 main-color"></i> ${this.formatDateSimple(featuredNews.NewsDate)}</span>
            <span><i class="bi bi-eye main-color"></i> ${featuredNews.ViewCount}</span>
          </div>
        </div>
      </a>
    ` : '';


    const rightHtml = `
      <div class="news-hero-secondary">
        ${homepageNews[0] ? `
          <a href="${this._currentSiteUrl}/SitePages/NewsDetails.aspx?newsid=${homepageNews[0].Id}"
            class="news-overlay news-hero-item">
            <img src="${homepageNews[0].Image}" alt="${homepageNews[0].Title}" class="img-fluid w-100 object-fit-cover">
            <div class="news-hero-caption p-2">
              <h2>${this._isArabic ? homepageNews[0].Title_AR : homepageNews[0].Title}</h2>
            </div>
          </a>
        ` : ''}

        <div class="row g-3">
        ${homepageNews.slice(1, 3).map((news: any) => `
            <a href="${this._currentSiteUrl}/SitePages/NewsDetails.aspx?newsid=${news.Id}" class="col-6">
              <div class="news-hero-item news-overlay">
                <img src="${news.Image}" alt="${news.Title}" class="img-fluid w-100 object-fit-cover">
                <div class="news-hero-caption p-2">
                  <h2>${this._isArabic ? news.Title_AR : news.Title}</h2>
                </div>
              </div>
            </a>
          `).join('')}
        </div>
      </div>
    `;





    const isArabic = this._isArabic;
    const backText = isArabic ? 'رجوع' : 'Back';

    const newsItemsHtml = this.getPagedNewsItems().map(item => {
      if (this.currentView === 'list') {
        const cardDirection = isArabic ? 'flex-row-reverse' : 'flex-row';
        const textAlign = isArabic ? 'text-end' : 'text-start';
        return `
      <div class="news-wrapper view-list g-3">
        <div class="news-item mb-3">
          <div class="card card-color p-2 h-100 d-flex ${cardDirection} align-items-stretch">
            <div class="news-image flex-shrink-0">
              <img src="${item.Image}" class="img-fluid rounded object-fit-cover" alt="${item.Title}">
            </div>
            <div class="p-3 d-flex flex-column justify-content-between ${textAlign}">
              <div>
                <h5 class="fw-bold">${isArabic ? item.Title_AR || '' : item.Title || ''}</h5>
               
              <p class="small">${isArabic ? item.NewsBrief_AR || '' : item.NewsBrief_EN || ''}</p>

              </div>
              <div class="d-flex justify-content-between align-items-center border-top pt-2 mt-2">
                <div class="${isArabic ? 'text-start' : 'text-end'}">
                  <a href="${this._currentSiteUrl}/SitePages/NewsDetails.aspx?newsid=${item.Id}"
                    class="btn-main-link fw-bold">
                    ${isArabic ? 'اقرأ المزيد' : 'Read More'} <i class="bi bi-arrow-${isArabic ? 'left' : 'right'}"></i>
                  </a>
                </div>
                <div class="d-flex align-items-center ${isArabic ? 'flex-row-reverse' : ''}">
                  <span class="mx-3"><i class="bi bi-calendar3 main-color"></i>
                    ${this.formatDateSimple(item.NewsDate)}</span>
                  <span><i class="bi bi-eye main-color"></i> ${item.ViewCount}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>`;
      } else {
        return `
      <div class="news-item">
        <div class="card card-color p-2 h-100">
          <img src="${item.Image}" class="img-fluid rounded" alt="${isArabic ? item.alt_AR || '' : item.alt_EN || ''}">
          <div class="p-2 flex-grow-1">
            <h5 class="fw-bold">${isArabic ? item.Title_AR : item.Title}</h5>
            <p class="small">${isArabic ? item.NewsBrief_AR || '' : item.NewsBrief_EN || ''}</p>

            <div class="d-flex justify-content-between align-items-center border-top">
              <div class="text-end mt-2">
                <a href="${this._currentSiteUrl}/SitePages/NewsDetails.aspx?newsid=${item.Id}" class="btn-main-link fw-bold">
                  ${isArabic ? 'اقرأ المزيد' : 'Read More'} <i class="bi bi-arrow-${isArabic ? 'left' : 'right'}"></i>
                </a>
              </div>
              <div class="d-flex align-items-center mt-2">
                <span class="mx-3"><i class="bi bi-calendar3 main-color"></i>
                  ${this.formatDateSimple(item.NewsDate)}</span>
                <span><i class="bi bi-eye main-color"></i> ${item.ViewCount}</span>
              </div>
            </div>
          </div>
        </div>
      </div>`;
      }
    }).join('');

    const paginationHtml = this.getPaginationHtml();
    const referrer = document.referrer;

    if (referrer) {
      const refUrl = new URL(referrer);
      const serverRelative = refUrl.pathname;

      this.getPageTitle(serverRelative).then(title => {
        this.domElement.querySelector('#referrerName')!.textContent = title;
      });

    }

    const referrerName = document.title


    // إنشاء الـ fullHtml فقط إذا كان فيه محتوى
    const hasContent = featuredNews || (homepageNews && homepageNews.length > 0);
    const fullHtml = hasContent ? `
      <div class="news-hero">
        ${featuredHtml}
        ${rightHtml}
      </div>
    ` : '';

    this.domElement.innerHTML = `

      <main class="news-page mt-3">
        <div class="container-fluid">
          ${fullHtml ? `
            <section class="card card-color p-3">
              ${fullHtml}
            </section>
          ` : ''}

          <section class="bread-cramp mt-3">
            <div class="card card-color">
              <div class="card-body d-flex justify-content-between align-items-center flex-wrap">
                <h2 class="fw-bold m-0 head-color">${referrerName}</h2>
                <div class="d-flex align-items-center gap-3">
                  <nav aria-label="breadcrumb">
                    <ol class="breadcrumb m-0">
                      <li class="breadcrumb-item">
                        <a href="/sites/intranet-qm/SitePages/TopicHome.aspx" class="btn-main-link fw-bold">Home</a>
                      </li>
                      <li class="breadcrumb-item  text-color fw-semibold" aria-current="page">QM in the News</li>

                    </ol>
                  </nav>
                  <button class="btn btn-outline-dark d-flex align-items-center gap-1" onclick="history.back()">
                    <i class="bi bi-arrow-left"></i> ${backText}
                  </button>
                </div>
              </div>
            </div>
          </section>
          <div class="row">
            <div class="col-lg-3">
              <section class="card fliter-card card-color mt-3 p-3 ">
                <div class="card-body">
                  <h3 class="fw-bold head-color mb-3">${this._isArabic ? 'الفلتر والبحث' : 'Filters &amp; Search'}</h3>
                  <hr>
                  <form>
                    <div class="input-group mb-3">
                      <input type="text" class="form-control" aria-label="Search" placeholder="${this._isArabic ? 'ابحث' : 'Search'}">
                      <button class="btn btn-outline-secondary" type="button" aria-label="Search">
                        <i class="bi bi-search"></i>
                      </button>
                    </div>
                    <div class="input-group mb-3">
                      <input type="text" class="form-control" aria-label="From Date" placeholder="${this._isArabic ? 'من تاريخ' : 'From Date'}">
                      <span class="input-group-text ">
                        <i class="bi bi-calendar3 text-color"></i>
                      </span>
                    </div>
                    <div class="input-group mb-3">
                      <input type="text" class="form-control" aria-label="To Date" placeholder=${this._isArabic ? 'إلى تاريخ' : 'To Date'}">
                      <span class="input-group-text ">
                        <i class="bi bi-calendar3 text-color"></i>
                      </span>
                    </div>
                    <button type="submit"
                      class="btn  btn bg-main bg-main-hover text-white text-white fw-bold w-100 mb-2">
                      ${this._isArabic ? 'تصفية' : 'Filter'} </button>
                    <button type="reset" class="btn btn-outline-dark fw-bold w-100">${this._isArabic ? 'إعادة تعيين' : 'Reset'}</button>
                  </form>
                </div>
              </section>
            </div>
            <div class="col-lg-9">


              <section class="new-list card card-color p-3 mt-3">
                <div class="d-flex justify-content-between align-items-center">
                  <h4 class="fw-bold head-color"><strong>${this._isArabic ? 'النتيجة الكلية:' : 'Total Result:'}</strong> ${this.totalItems}</h4>
                  <div class=" align-items-center gap-2 flex-wrap view-btns mb-3">
                    <span class="text-color">${this._isArabic ? 'عرض' : 'View'}</span>
                    <button class="btn btn-outline-secondary btn-sm ${this.currentView === 'list' ? 'selected' : ''}"
                      id="listViewBtn" aria-label="listView" data-view="list">
                      <i class="bi bi-list"></i>
                    </button>
                    <button class="btn btn-outline-secondary btn-sm ${this.currentView === 'grid' ? 'selected' : ''}"
                      id="gridViewBtn" aria-label="gridView" data-view="grid">
                      <i class="bi bi-grid-3x3-gap"></i>
                    </button>
                    <div class="dropdown ms-2">
                      <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="sortDropdown"
                        data-bs-toggle="dropdown" aria-expanded="false">
                        ${this._isArabic ? 'الترتيب حسب: تاريخ جديد' : 'Sort by: New Date'}
                      </button>
                       <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                        <li><a class="dropdown-item" href="#" data-sort="Newest First">${this._isArabic ? 'الأحدث أولاً' : 'Newest First'}</a></li>
                        <li><a class="dropdown-item" href="#" data-sort="Oldest First">${this._isArabic ? 'الأقدم أولاً' : 'Oldest First'}</a></li>
                        <li><a class="dropdown-item" href="#" data-sort="Most Viewed">${this._isArabic ? 'الأكثر مشاهدة' : 'Most Viewed'}</a></li>
                      </ul>
                    </div>
                  </div>
                </div>
                <hr>
                ${this.currentView === 'grid' ? `
                <div class="news-wrapper view-grid g-3">
                  ${newsItemsHtml}
                </div>
                ` : `
                ${newsItemsHtml}
                `}
                ${paginationHtml}
              </section>
            </div>
          </div>
        </div>
      </main>
      `;

    this.addPaginationEventListeners();
    this.addViewToggleListeners();
    this.initializeDatePickers();
    this.addFilterListeners()
  }

  private addViewToggleListeners(): void {
    const listViewBtn = this.domElement.querySelector('#listViewBtn');
    const gridViewBtn = this.domElement.querySelector('#gridViewBtn');
    const sortDropdownItems = this.domElement.querySelectorAll('.dropdown-item');

    if (listViewBtn) {
      listViewBtn.addEventListener('click', () => {
        this.currentView = 'list';
        this.render();
      });
    }

    if (gridViewBtn) {
      gridViewBtn.addEventListener('click', () => {
        this.currentView = 'grid';
        this.render();
      });
    }
    sortDropdownItems.forEach(item => {
      item.addEventListener('click', (event: any) => {
        const sortOption = event.target.getAttribute('data-sort');
        this.changeSortOrder(sortOption);
      });
    });

  }

  private addPaginationEventListeners(): void {
    const pageLinks = this.domElement.querySelectorAll('.page-item .page-link');

    pageLinks.forEach((link: any) => {
      link.addEventListener('click', (event: Event) => {
        event.preventDefault();
        const pageText = link.textContent.trim().toLowerCase();
        if (pageText === 'next') {
          this.changePage(this.currentPage + 1);
        } else if (pageText === 'previous') {
          this.changePage(this.currentPage - 1);
        } else {
          const pageNumber = parseInt(pageText);
          this.changePage(pageNumber);
        }
      });
    });
  }
  private handleResponsiveView(): void {
    const isSmallScreen = window.innerWidth < 768;

    if (isSmallScreen && this.currentView !== 'grid') {
      this.currentView = 'grid';
      this.render();
    }
  }


  protected onInit(): Promise<void> {
    // تحديد الـ site URL الحالي للـ NewsDetails links
    this._currentSiteUrl = this._getCurrentSiteUrl();

    window.addEventListener('resize', this.handleResponsiveView.bind(this));
    this.handleResponsiveView();
    return this.fetchNewsData();
  }

  private _getCurrentSiteUrl(): string {
    try {
      const currentUrl = window.location.pathname.toLowerCase();

      // إذا كان في الموقع الرئيسي
      if (currentUrl.includes('/sites/intranet-qm/sitepages/') && !this._isInSubsite(currentUrl)) {
        return '/sites/intranet-qm';
      }

      // إذا كان في subsite
      const subsiteMatch = currentUrl.match(/\/sites\/intranet-qm\/([^\/]+)\//);
      if (subsiteMatch && subsiteMatch[1] !== 'sitepages') {
        const subsitePath = `/sites/intranet-qm/${subsiteMatch[1]}`;
        return subsitePath;
      }

      // افتراضي: الموقع الرئيسي
      return '/sites/intranet-qm';
    } catch (error) {
      console.error('News List - Error determining site URL:', error);
      return '/sites/intranet-qm';
    }
  }

  private _isInSubsite(url: string): boolean {
    // تحقق من وجود subsite في الـ URL
    const subsitePattern = /\/sites\/intranet-qm\/([^\/]+)\/sitepages\//i;
    const match = url.match(subsitePattern);

    if (match && match[1]) {
      const potentialSubsite = match[1].toLowerCase();
      // تجاهل 'ar' لأنه language code وليس subsite
      return potentialSubsite !== 'ar';
    }

    return false;
  }


  private fetchNewsData(searchText: string = '', fromDate?: string, toDate?: string): Promise<void> {
    let filters = [];

    if (searchText) {
      const escapedSearch = searchText.replace(/'/g, "''");
      const textFilter = `(substringof('${escapedSearch}', Title) or substringof('${escapedSearch}', Title_AR))`;
      filters.push(textFilter);
    }

    const filterQuery = filters.length > 0 ? `&$filter=${filters.join(' and ')}` : '';

    let orderQuery = '';
    switch (this.currentSortOrder) {
      case 'Newest First':
        orderQuery = '$orderby=NewsDate desc';
        break;
      case 'Oldest First':
        orderQuery = '$orderby=NewsDate asc';
        break;
      case 'Most Viewed':
        orderQuery = '$orderby=ViewCount desc';
        break;
      default:
        orderQuery = '$orderby=NewsDate desc';
    }

    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('News Articles')/items?${orderQuery}${filterQuery}`;

    return this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => response.json())
      .then((data) => {
        let filteredData = data.value;

        if (fromDate && toDate && fromDate === toDate) {
          const selectedDate = new Date(fromDate);
          selectedDate.setHours(0, 0, 0, 0);

          const nextDay = new Date(selectedDate);
          nextDay.setDate(nextDay.getDate() + 1);

          filteredData = filteredData.filter((item: any) => {
            const newsDate = new Date(item.NewsDate);
            return newsDate >= selectedDate && newsDate < nextDay;
          });
        } else {
          if (fromDate) {
            const from = new Date(fromDate);
            from.setHours(0, 0, 0, 0);
            filteredData = filteredData.filter((item: any) => new Date(item.NewsDate) >= from);
          }
          if (toDate) {
            const to = new Date(toDate);
            to.setDate(to.getDate() + 1);
            to.setHours(0, 0, 0, 0);
            filteredData = filteredData.filter((item: any) => new Date(item.NewsDate) < to);
          }
        }

        return Promise.all(filteredData.map((item: any) => {
          return this.getAttachments(item.Id).then((attachments) => {
            let ImageNews = '';
            const bannerImageData = item?.Image ? JSON.parse(item?.Image) : null;

            if (bannerImageData?.serverUrl && bannerImageData?.serverRelativeUrl) {
              ImageNews = bannerImageData.serverRelativeUrl;
            } else if (attachments.length > 0) {
              ImageNews = attachments[0];
            } else {
              ImageNews = require('./assets/img.jpg');
            }

            return {
              Id: item.Id,
              Image: ImageNews,
              Title: item.Title || '',
              Title_AR: item.Title_AR || '',
              NewsDate: item.NewsDate || '',
              ViewCount: item.ViewCount || 0,
              ShowOnHomepage: item.ShowOnHomepage,
              IsFeatured: item.IsFeatured,
              NewsBody_AR: item.NewsBody_AR || '',
              NewsBody_EN: item.NewsBody_EN || '',
              NewsBrief_EN: item.NewsBrief_EN || '',
              NewsBrief_AR: item.NewsBrief_AR || '',
              alt_EN: item.alt_EN || '',
              alt_AR: item.alt_AR || '',
            };
          });
        }));
      })
      .then((newsItemsWithImages) => {
        const isInitialLoad = !searchText && !fromDate && !toDate && this.currentSortOrder === 'Newest First';

        if (isInitialLoad) {
          this.originalNewsItems = [...newsItemsWithImages];
          this.allNewsItems = newsItemsWithImages;
        }

        if (!searchText && !fromDate && !toDate) {
          this.allNewsItems = newsItemsWithImages;
        }


        this.newsItems = newsItemsWithImages;
        this.totalItems = this.newsItems.length;
        this.render();
      })
      .catch(error => {
        console.error('Error fetching news data or attachments:', error);
      });
  }




  private getAttachments(itemId: number): Promise<string[]> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/lists/getbytitle('News Articles')/items(${itemId})/AttachmentFiles`;

    return this.context.spHttpClient.get(url, SPHttpClient.configurations.v1)
      .then((response: SPHttpClientResponse) => response.json())
      .then((data) => {
        return data.value.map((file: any) => file.ServerRelativeUrl);
      })

  }

  private formatDateSimple(date: string): string {
    const parsedDate = new Date(date);
    const year = parsedDate.getFullYear();
    const month = (parsedDate.getMonth() + 1).toString();
    const day = parsedDate.getDate().toString();
    return `${year}-${month}-${day}`;
  }

  private getPagedNewsItems(): any[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    return this.newsItems.slice(startIndex, startIndex + this.itemsPerPage);
  }

  private getPaginationHtml(): string {
    const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
    const pageNumbers = [];

    for (let i = 1; i <= totalPages; i++) {
      pageNumbers.push(`
        <li class="page-item ${this.currentPage === i ? 'active' : ''}">
          <a class="page-link" href="#">${i}</a>
        </li>
      `);
    }

    return `
      <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center flex-wrap gap-1">
          <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link rounded-pill px-3" href="#" aria-label="Previous">
              <i class="bi bi-arrow-left"></i> ${this._isArabic ? 'السابق' : 'Previous'}
            </a>
          </li>
          ${pageNumbers.join('')}
          <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link px-3" href="#" aria-label="Next">
              ${this._isArabic  ? 'التالي' : 'Next'} <i class="bi bi-arrow-right"></i>
            </a>
          </li>
        </ul>
      </nav>
    `;
  }


  private changeSortOrder(order: string): void {
    this.currentSortOrder = order;
    this.currentPage = 1;
    this.fetchNewsData();
  }
  private changePage(pageNumber: number): void {
    const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
    if (pageNumber > 0 && pageNumber <= totalPages) {
      this.currentPage = pageNumber;
      this.fetchNewsData();
    }

  }
  async getPageTitle(serverRelativeUrl: string): Promise<string> {
    const url = `${this.context.pageContext.web.absoluteUrl}/_api/web/getfilebyserverrelativeurl('${serverRelativeUrl}')/ListItemAllFields?$select=Title`;

    const response = await this.context.spHttpClient.get(url, SPHttpClient.configurations.v1);
    const data = await response.json();

    return data?.Title || '';
  }
}
