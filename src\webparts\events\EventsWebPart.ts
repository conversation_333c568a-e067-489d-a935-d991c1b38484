import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';

export interface IEventsWebPartProps {
  description: string;
}

export default class EventsWebPart extends BaseClientSideWebPart<IEventsWebPartProps> {
  private calendarStates = {
    tab1: { currentDate: new Date() },
    tab2: { currentDate: new Date() }
  };


  public render(): void {
    this.domElement.innerHTML = `
      <div class="col-lg-12 mt-4 mt-lg-0">
        <section class="events-section h-100">
          <div class="card section-card card-color px-3 pt-3 h-100">
            <!-- Tabs -->
            <ul class="nav nav-tabs mb-3" id="eventTabs" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="nav-link active fw-bold" id="whatson-tab" data-bs-toggle="tab" data-bs-target="#whatson"
                  type="button" role="tab" aria-controls="whatson" aria-selected="true">
                  WHAT’S ON
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link fw-bold" id="preregister-tab" data-bs-toggle="tab" data-bs-target="#preregister"
                  type="button" role="tab" aria-controls="preregister" aria-selected="false">
                  Pre-Register Events
                </button>
              </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="eventTabsContent">

              <!-- WHAT’S ON Tab -->
              <div class="tab-pane fade show active" id="whatson" role="tabpanel" aria-labelledby="whatson-tab">
                <div class="text-center flex-grow-1">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <h3 class="fw-bold mb-1 head-color" id="calendarMonthLabel">date</h3>
                    <div class="d-flex">
                      <button class="btn btn-sm btn-outline-primary px-2 py-1 mx-2" aria-label="prevWeekBtn" id="prevWeekBtn">
                        <i class="bi bi-chevron-left"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-primary px-2 py-1" aria-label="prevWeekBtn" id="nextWeekBtn">
                        <i class="bi bi-chevron-right"></i>
                      </button>
                    </div>
                  </div>

                  <div class="d-flex justify-content-between text-center text-color mb-2" id="weekDaysContainer">
                    <!-- JS will inject weekdays here -->
                  </div>
                </div>

                <div style="; overflow-y: auto;" class="flex-grow-1 event-list">
                  <!-- Event Cards -->
                  <div class="d-flex mb-3 align-items-start">
                    <div class="bg-orange text-white text-center rounded p-2 mx-3" style="width: 60px;">
                      <div class="small">Dec</div>
                      <div class="fw-bold">26</div>
                    </div>
                    <div>
                      <h3 class="fw-bold mb-1 head-color">Interactive Art Night</h3>
                      <small class="text-color">Sat, Feb 15, 7:00 PM</small>
                    </div>
                  </div>

                  <div class="d-flex mb-3 align-items-start">
                    <div class="bg-blue text-white text-center rounded p-2 mx-3" style="width: 60px;">
                      <div class="small">Dec</div>
                      <div class="fw-bold">27</div>
                    </div>
                    <div>
                      <h3 class="fw-bold mb-1 head-color">Cultural Storytelling</h3>
                      <small class="text-color">Sun, Feb 16, 6:30 PM</small>
                    </div>
                  </div>
                </div>

                <div class="card-footer text-end">
                  <a href="events.html" class="text-decoration-none fw-bold btn-main-link">View All <i class="bi bi-arrow-right-circle"></i></a>
                </div>
              </div>

              <!-- Pre-Register Events Tab -->
              <div class="tab-pane fade" id="preregister" role="tabpanel" aria-labelledby="preregister-tab">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <h3 class="fw-bold mb-1 head-color" id="calendarMonthLabel2">date</h3>
                  <div class="d-flex">
                    <button class="btn btn-sm btn-outline-primary px-2 py-1 mx-2" id="prevWeekBtn2">
                      <i class="bi bi-chevron-left"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary px-2 py-1" id="nextWeekBtn2">
                      <i class="bi bi-chevron-right"></i>
                    </button>
                  </div>
                </div>

                <div class="text-center flex-grow-1">
                  <div class="d-flex justify-content-between text-center text-color mb-2" id="weekDaysContainer2">
                    <!-- JS will inject weekdays here -->
                  </div>
                </div>

                <div class-"event-list" style=" overflow-y: auto;">
                  <div class="d-flex no-data justify-content-center">
                    <img src="asset/imgs/Empty File V5-3.svg" alt="">
                  </div>
                  <div class="text-color text-center py-2">No Pre-Register Events at the moment.</div>
                </div>

                <div class="card-footer text-end">
                  <a href="events.html" class="text-decoration-none fw-bold btn-main-link">View All <i class="bi bi-arrow-right-circle"></i></a>
                </div>
              </div>

            </div>
          </div>
        </section>
      </div>
    `;

    // Initialize calendars for both tabs
    this.initializeCalendarTab('weekDaysContainer', 'calendarMonthLabel', 'prevWeekBtn', 'nextWeekBtn', 'tab1');
    this.initializeCalendarTab('weekDaysContainer2', 'calendarMonthLabel2', 'prevWeekBtn2', 'nextWeekBtn2', 'tab2');

  }

  private initializeCalendarTab(
    weekContainerId: string,
    monthLabelId: string,
    prevBtnId: string,
    nextBtnId: string,
    tabKey: 'tab1' | 'tab2'
  ): void {
    const weekContainer = document.getElementById(weekContainerId) as HTMLDivElement;
    const monthLabel = document.getElementById(monthLabelId) as HTMLElement;
    const prevBtn = document.getElementById(prevBtnId) as HTMLButtonElement;
    const nextBtn = document.getElementById(nextBtnId) as HTMLButtonElement;

    if (weekContainer && monthLabel && prevBtn && nextBtn) {
      const getStartOfWeek = (date: Date): Date => {
        const day = date.getDay();
        const newDate = new Date(date);
        newDate.setDate(newDate.getDate() - day);
        return newDate;
      };

      const renderWeek = (date: Date): void => {
        const start = getStartOfWeek(new Date(date));
        const dayNames = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
        weekContainer.innerHTML = '';

        monthLabel.textContent = start.toLocaleDateString('en-US', {
          month: 'long',
          year: 'numeric'
        });

        for (let i = 0; i < 7; i++) {
          const day = new Date(start);
          day.setDate(start.getDate() + i);

          const dayBlock = document.createElement('div');
          dayBlock.className = 'cursor-pointer';
          const isToday = new Date().toDateString() === day.toDateString();

          dayBlock.innerHTML = `
            <div class="fw-bold small">${dayNames[i]}</div>
            <div class="fw-semibold px-2 py-1 rounded ${isToday ? 'bg-main text-white' : ''}">
              ${day.getDate()}
            </div>
          `;
          weekContainer.appendChild(dayBlock);
        }
      };

      prevBtn.addEventListener('click', () => {
        this.calendarStates[tabKey].currentDate.setDate(this.calendarStates[tabKey].currentDate.getDate() - 7);
        renderWeek(this.calendarStates[tabKey].currentDate);
      });

      nextBtn.addEventListener('click', () => {
        this.calendarStates[tabKey].currentDate.setDate(this.calendarStates[tabKey].currentDate.getDate() + 7);
        renderWeek(this.calendarStates[tabKey].currentDate);
      });

      renderWeek(this.calendarStates[tabKey].currentDate);
    }
  }

}
