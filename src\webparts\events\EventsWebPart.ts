import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { HttpClient, HttpClientResponse } from '@microsoft/sp-http';

export interface IEventsWebPartProps {
  description: string;
}

interface Event {
  id: number;
  title: string;
  meta: {
    start_date: string;
    end_date: string;
    location: string;
    event_type: string;
    audience: string;
  };
  excerpt: string;
  featured_image?: {
    url: string;
  };
}

interface EventsApiResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Event[];
  tags: {
    event_types: string[];
    exhibition_types: string[];
    locations: string[];
    organisers: string[];
    audience: string[];
  };
  page_types: string[];
}

export default class EventsWebPart extends BaseClientSideWebPart<IEventsWebPartProps> {
  private events: Event[] = [];
  private publicEvents: Event[] = [];
  private staffEvents: Event[] = [];
  private currentPage: number = 1;
  private totalCount: number = 0;
  private isLoading: boolean = false;
  private selectedQuarter: string = 'Q1';
  private activeTab: string = 'public';


  public async render(): Promise<void> {
    this.domElement.innerHTML = `<div class="text-center py-4">Loading events...</div>`;
    await this.loadEvents();
    this.renderNewUI();
    this.setupEventListeners();
  }

  private async loadEvents(): Promise<void> {
    try {
      this.isLoading = true;
      console.log('Loading events from API...');

      const url = `https://qm.org.qa/api/v2/pages/events/?format=json&page=${this.currentPage}`;

      const response: HttpClientResponse = await this.context.httpClient.get(
        url,
        HttpClient.configurations.v1
      );

      if (response.ok) {
        const data: EventsApiResponse = await response.json();
        console.log('Events API response:', data);

        this.events = data.results || [];
        this.totalCount = data.count || 0;
        this.separateEventsByAudience();

        console.log(`Loaded ${this.events.length} events. Total: ${this.totalCount}`);
      } else {
        console.error('Failed to load events:', response.statusText);
        this.events = [];
      }

    } catch (error) {
      console.error('Error loading events:', error);
      this.events = [];
    } finally {
      this.isLoading = false;
    }
  }

  private separateEventsByAudience(): void {
    this.publicEvents = this.events.filter(event =>
      !event.meta.audience ||
      event.meta.audience.toLowerCase().includes('public') ||
      event.meta.audience.toLowerCase().includes('general')
    );

    this.staffEvents = this.events.filter(event =>
      event.meta.audience &&
      (event.meta.audience.toLowerCase().includes('staff') ||
       event.meta.audience.toLowerCase().includes('employee'))
    );

    console.log(`Public events: ${this.publicEvents.length}, Staff events: ${this.staffEvents.length}`);
  }

 

  // دالة renderUI جديدة
  private renderNewUI(): void {
    this.domElement.innerHTML = `
      <section class="events-section h-100">
        <div class="card section-card card-color px-3 pt-3 h-100">
          <h2 class="fw-bold tittle head-color">What's On</h2>

          <ul class="nav nav-tabs mb-3" id="eventTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link fw-bold ${this.activeTab === 'public' ? 'active' : ''}"
                      id="public-tab" data-tab="public" type="button">
                Public Events (${this.publicEvents.length})
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link fw-bold ${this.activeTab === 'staff' ? 'active' : ''}"
                      id="staff-tab" data-tab="staff" type="button">
                Staff Events (${this.staffEvents.length})
              </button>
            </li>
          </ul>

          <div class="d-flex justify-content-center align-items-center mb-3 gap-2">
            <button class="btn btn-outline-primary quarter-btn ${this.selectedQuarter === 'Q1' ? 'active' : ''}" data-quarter="Q1">Q1</button>
            <button class="btn btn-outline-primary quarter-btn ${this.selectedQuarter === 'Q2' ? 'active' : ''}" data-quarter="Q2">Q2</button>
            <button class="btn btn-outline-primary quarter-btn ${this.selectedQuarter === 'Q3' ? 'active' : ''}" data-quarter="Q3">Q3</button>
            <button class="btn btn-outline-primary quarter-btn ${this.selectedQuarter === 'Q4' ? 'active' : ''}" data-quarter="Q4">Q4</button>
          </div>

          <div class="events-content">
            ${this.renderEventsContent()}
          </div>

          ${this.renderPagination()}
        </div>
      </section>
    `;
  }

  // دوال جديدة للـ API
  private renderEventsContent(): string {
    if (this.isLoading) {
      return '<div class="text-center py-4"><div class="spinner-border" role="status"></div><p>Loading events...</p></div>';
    }

    const eventsToShow = this.activeTab === 'public' ? this.publicEvents : this.staffEvents;
    const filteredEvents = this.filterEventsByQuarter(eventsToShow);

    if (filteredEvents.length === 0) {
      return '<div class="text-center py-4"><h5 class="text-muted">No events found for this quarter</h5></div>';
    }

    return `
      <div class="row">
        ${filteredEvents.map(event => this.renderEventCard(event)).join('')}
      </div>
    `;
  }

  private renderEventCard(event: Event): string {
    const startDate = new Date(event.meta.start_date);
    const endDate = new Date(event.meta.end_date);
    const imageUrl = event.featured_image?.url || '/sites/intranet-qm/SiteAssets/default-event.jpg';

    return `
      <div class="col-md-6 col-lg-4 mb-3">
        <div class="card h-100 event-card" data-event-id="${event.id}">
          <img src="${imageUrl}" class="card-img-top" alt="${event.title}" style="height: 200px; object-fit: cover;">
          <div class="card-body d-flex flex-column">
            <h6 class="card-title fw-bold">${event.title}</h6>
            <p class="card-text text-muted small flex-grow-1">${event.excerpt || 'No description available'}</p>
            <div class="mt-auto">
              <small class="text-muted">
                <i class="fas fa-calendar"></i> ${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}
              </small>
              ${event.meta.location ? `<br><small class="text-muted"><i class="fas fa-map-marker-alt"></i> ${event.meta.location}</small>` : ''}
              ${event.meta.event_type ? `<br><small class="badge bg-primary">${event.meta.event_type}</small>` : ''}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private filterEventsByQuarter(events: Event[]): Event[] {
    const currentYear = new Date().getFullYear();

    const quarterRanges = {
      Q1: { start: new Date(currentYear, 0, 1), end: new Date(currentYear, 2, 31) },
      Q2: { start: new Date(currentYear, 3, 1), end: new Date(currentYear, 5, 30) },
      Q3: { start: new Date(currentYear, 6, 1), end: new Date(currentYear, 8, 30) },
      Q4: { start: new Date(currentYear, 9, 1), end: new Date(currentYear, 11, 31) }
    };

    const range = quarterRanges[this.selectedQuarter as keyof typeof quarterRanges];

    return events.filter(event => {
      const eventDate = new Date(event.meta.start_date);
      return eventDate >= range.start && eventDate <= range.end;
    });
  }

  private renderPagination(): string {
    if (this.totalCount <= 20) return '';

    const totalPages = Math.ceil(this.totalCount / 20);

    return `
      <nav aria-label="Events pagination" class="mt-4">
        <ul class="pagination justify-content-center">
          <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
            <button class="page-link" data-page="${this.currentPage - 1}" ${this.currentPage === 1 ? 'disabled' : ''}>Previous</button>
          </li>
          ${Array.from({length: Math.min(5, totalPages)}, (_, i) => {
            const page = i + 1;
            return `
              <li class="page-item ${this.currentPage === page ? 'active' : ''}">
                <button class="page-link" data-page="${page}">${page}</button>
              </li>
            `;
          }).join('')}
          <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
            <button class="page-link" data-page="${this.currentPage + 1}" ${this.currentPage === totalPages ? 'disabled' : ''}>Next</button>
          </li>
        </ul>
      </nav>
    `;
  }

  private setupEventListeners(): void {
    // Tab switching
    this.domElement.querySelectorAll('[data-tab]').forEach(tab => {
      tab.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        const tabType = target.getAttribute('data-tab');
        if (tabType) {
          this.activeTab = tabType;
          this.renderNewUI();
          this.setupEventListeners();
        }
      });
    });

    // Quarter switching
    this.domElement.querySelectorAll('[data-quarter]').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        const quarter = target.getAttribute('data-quarter');
        if (quarter) {
          this.selectedQuarter = quarter;
          this.renderNewUI();
          this.setupEventListeners();
        }
      });
    });

    // Pagination
    this.domElement.querySelectorAll('[data-page]').forEach(btn => {
      btn.addEventListener('click', async (e) => {
        const target = e.target as HTMLElement;
        const page = parseInt(target.getAttribute('data-page') || '1');
        if (page !== this.currentPage) {
          this.currentPage = page;
          await this.loadEvents();
          this.renderNewUI();
          this.setupEventListeners();
        }
      });
    });

    // Event card clicks
    this.domElement.querySelectorAll('.event-card').forEach(card => {
      card.addEventListener('click', (e) => {
        const eventId = (e.currentTarget as HTMLElement).getAttribute('data-event-id');
        if (eventId) {
          console.log('Event clicked:', eventId);
        }
      });
    });
  }

  // دوال للاختبار
  public showEventsInfo(): void {
    console.log('=== Events Info ===');
    console.log('Total events:', this.events.length);
    console.log('Public events:', this.publicEvents.length);
    console.log('Staff events:', this.staffEvents.length);
    console.log('Current page:', this.currentPage);
    console.log('Total count:', this.totalCount);
    console.log('Active tab:', this.activeTab);
    console.log('Selected quarter:', this.selectedQuarter);
  }

  public switchToQuarter(quarter: string): void {
    if (['Q1', 'Q2', 'Q3', 'Q4'].includes(quarter)) {
      this.selectedQuarter = quarter;
      this.renderNewUI();
      this.setupEventListeners();
      console.log(`Switched to ${quarter}`);
    } else {
      console.log('Invalid quarter. Use Q1, Q2, Q3, or Q4');
    }
  }

  public switchTab(tab: string): void {
    if (['public', 'staff'].includes(tab)) {
      this.activeTab = tab;
      this.renderNewUI();
      this.setupEventListeners();
      console.log(`Switched to ${tab} tab`);
    } else {
      console.log('Invalid tab. Use "public" or "staff"');
    }
  }

  public async refreshEvents(): Promise<void> {
    console.log('Refreshing events...');
    this.currentPage = 1;
    await this.loadEvents();
    this.renderNewUI();
    this.setupEventListeners();
  }

  protected onInit(): Promise<void> {
    (window as any).eventsWebPart = this;
    return Promise.resolve();
  }

}
