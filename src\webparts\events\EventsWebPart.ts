import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';

export interface IEventsWebPartProps {
  description: string;
}

export default class EventsWebPart extends BaseClientSideWebPart<IEventsWebPartProps> {
  private calendarStates = {
    tab1: { currentDate: new Date() },
    tab2: { currentDate: new Date() }
  };


  public render(): void {
    this.domElement.innerHTML = ` <section class="events-section h-100">
              <div class="card section-card card-color px-3 pt-3 h-100">
                                  <h2 class="fw-bold tittle head-color ">Whats On  </h2>


                <!-- Tabs -->
                <ul class="nav nav-tabs mb-3" id="eventTabs" role="tablist">
                       <li class="nav-item" role="presentation">
                    <button class="nav-link fw-bold active" aria-label="preregister-tab" id="preregister-tab"
                      data-bs-toggle="tab" data-bs-target="#preregister" type="button" role="tab"
                      aria-controls="preregister" aria-selected="false">
                     puplic
                    </button>
                  </li>
                  <li class="nav-item" role="presentation">
                    <button class="nav-link  fw-bold" aria-label="whatson-tab" id="whatson-tab"
                      data-bs-toggle="tab" data-bs-target="#whatson" type="button" role="tab" aria-controls="whatson"
                      aria-selected="true">
                      Staff 
                    </button>
                  </li>
             
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="eventTabsContent">

                  <!-- puplic -->
                  <div class="tab-pane fade " id="whatson" role="tabpanel" aria-labelledby="whatson-tab">

                    <div class="text-center flex-grow-1">
                      <div class="d-flex justify-content-center align-items-center mb-3 gap-2">
                        <button class="btn btn-outline-primary quarter-btn active" data-quarter="Q1">Q1</button>
                        <button class="btn btn-outline-primary quarter-btn" data-quarter="Q2">Q2</button>
                        <button class="btn btn-outline-primary quarter-btn" data-quarter="Q3">Q3</button>
                        <button class="btn btn-outline-primary quarter-btn" data-quarter="Q4">Q4</button>
                      </div>
                    </div>

                    <div id="quarterContents">
                      <div class="quarter-content" data-quarter="Q1">
                        <!-- Q1 events -->
                        <div class="d-flex mb-3 align-items-center">
                          <div class="bg-orange text-white text-center rounded p-2 me-3" style="width: 60px;">
                            <div class="small">Jan</div>
                            <div class="fw-bold">15</div>
                          </div>
                          <div>
                            <h3 class="fw-bold mb-1 head-color">Q1 Event Example 1</h3>
                            <small class="text-color">Jan 15 – Jan 18</small>
                          </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                          <div class="bg-blue text-white text-center rounded p-2 me-3" style="width: 60px;">
                            <div class="small">Feb</div>
                            <div class="fw-bold">2</div>
                          </div>
                          <div>
                            <h3 class="fw-bold mb-1 head-color">Q1 Event Example 2</h3>
                            <small class="text-color">Feb 2 – Feb 5</small>
                          </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                          <div class="bg-orange text-white text-center rounded p-2 me-3" style="width: 60px;">
                            <div class="small">Mar</div>
                            <div class="fw-bold">10</div>
                          </div>
                          <div>
                            <h3 class="fw-bold mb-1 head-color">Q1 Event Example 3</h3>
                            <small class="text-color">Mar 10 – Mar 12</small>
                          </div>
                        </div>
                      </div>
                      <div class="quarter-content d-none" data-quarter="Q2">
                        <!-- Q2 events -->
                        <div class="d-flex mb-3 align-items-center">
                          <div class="bg-blue text-white text-center rounded p-2 me-3" style="width: 60px;">
                            <div class="small">Apr</div>
                            <div class="fw-bold">10</div>
                          </div>
                          <div>
                            <h3 class="fw-bold mb-1 head-color">Q2 Event Example 1</h3>
                            <small class="text-color">Apr 10 – Apr 12</small>
                          </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                          <div class="bg-orange text-white text-center rounded p-2 me-3" style="width: 60px;">
                            <div class="small">May</div>
                            <div class="fw-bold">5</div>
                          </div>
                          <div>
                            <h3 class="fw-bold mb-1 head-color">Q2 Event Example 2</h3>
                            <small class="text-color">May 5 – May 8</small>
                          </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                          <div class="bg-blue text-white text-center rounded p-2 me-3" style="width: 60px;">
                            <div class="small">Jun</div>
                            <div class="fw-bold">18</div>
                          </div>
                          <div>
                            <h3 class="fw-bold mb-1 head-color">Q2 Event Example 3</h3>
                            <small class="text-color">Jun 18 – Jun 20</small>
                          </div>
                        </div>
                      </div>
                      <div class="quarter-content d-none" data-quarter="Q3">
                        <!-- Q3 events -->
                        <div class="d-flex mb-3 align-items-center">
                          <div class="bg-orange text-white text-center rounded p-2 me-3" style="width: 60px;">
                            <div class="small">Jul</div>
                            <div class="fw-bold">22</div>
                          </div>
                          <div>
                            <h3 class="fw-bold mb-1 head-color">Q3 Event Example 1</h3>
                            <small class="text-color">Jul 22 – Jul 25</small>
                          </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                          <div class="bg-blue text-white text-center rounded p-2 me-3" style="width: 60px;">
                            <div class="small">Aug</div>
                            <div class="fw-bold">8</div>
                          </div>
                          <div>
                            <h3 class="fw-bold mb-1 head-color">Q3 Event Example 2</h3>
                            <small class="text-color">Aug 8 – Aug 10</small>
                          </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                          <div class="bg-orange text-white text-center rounded p-2 me-3" style="width: 60px;">
                            <div class="small">Sep</div>
                            <div class="fw-bold">14</div>
                          </div>
                          <div>
                            <h3 class="fw-bold mb-1 head-color">Q3 Event Example 3</h3>
                            <small class="text-color">Sep 14 – Sep 16</small>
                          </div>
                        </div>
                      </div>
                      <div class="quarter-content d-none" data-quarter="Q4">
                        <!-- Q4 events -->
                        <div class="d-flex mb-3 align-items-center">
                          <div class="bg-blue text-white text-center rounded p-2 me-3" style="width: 60px;">
                            <div class="small">Oct</div>
                            <div class="fw-bold">5</div>
                          </div>
                          <div>
                            <h3 class="fw-bold mb-1 head-color">Q4 Event Example 1</h3>
                            <small class="text-color">Oct 5 – Oct 8</small>
                          </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                          <div class="bg-orange text-white text-center rounded p-2 me-3" style="width: 60px;">
                            <div class="small">Nov</div>
                            <div class="fw-bold">12</div>
                          </div>
                          <div>
                            <h3 class="fw-bold mb-1 head-color">Q4 Event Example 2</h3>
                            <small class="text-color">Nov 12 – Nov 15</small>
                          </div>
                        </div>
                        <div class="d-flex mb-3 align-items-center">
                          <div class="bg-blue text-white text-center rounded p-2 me-3" style="width: 60px;">
                            <div class="small">Dec</div>
                            <div class="fw-bold">20</div>
                          </div>
                          <div>
                            <h3 class="fw-bold mb-1 head-color">Q4 Event Example 3</h3>
                            <small class="text-color">Dec 20 – Dec 22</small>
                          </div>
                        </div>
                      </div>
                    </div>
                    <script>
                      // Quarter tab logic
                      document.querySelectorAll('.quarter-btn').forEach(btn => {
                        btn.addEventListener('click', function() {
                          // Tab button active state
                          document.querySelectorAll('.quarter-btn').forEach(b => b.classList.remove('active'));
                          this.classList.add('active');
                          // Show/hide quarter content
                          const selected = this.dataset.quarter;
                          document.querySelectorAll('.quarter-content').forEach(div => {
                            if (div.dataset.quarter === selected) {
                              div.classList.remove('d-none');
                            } else {
                              div.classList.add('d-none');
                            }
                          });
                        });
                      });
                    </script>
                   
                  </div>

                  <!-- staff -->
                  <div class="tab-pane fade show active" id="preregister" role="tabpanel" aria-labelledby="preregister-tab">



                    <style>
#monthTabsWrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-bottom: 1rem;
}
#monthTabsArrows {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}
#monthTabsContainer {
  display: flex;
  gap: 0.5rem;
  width: 100%;
  overflow: hidden;
}
.month-btn {
  min-width: 56px;

  border-radius: 12px;
  border: 2px solid var(--border-color);
  background: transparent;
  color: var(--text-color);
  font-weight: 600;
  font-size: 1rem;
  transition: background 0.2s, color 0.2s, border 0.2s;
  box-shadow: none;
  display: none;
}
.month-btn.active {
  background: var(--main-color);
  color: #fff !important;
  border-color: var(--main-color);
}
#monthPrevBtn, #monthNextBtn {
  background: transparent !important;
  border: 2px solid #ffd000 !important;
  color: #ffd000 !important;
  border-radius: 10px;
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  box-shadow: none;
  transition: background 0.2s, color 0.2s, border 0.2s;
}
#monthPrevBtn:disabled, #monthNextBtn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}
#monthEventsContents {
  height: 197px;
  overflow-y: auto;
}
</style>
<!-- staff -->
<div id="monthTabsWrapper">
  <div id="monthTabsArrows">
    <button class="btn btn-outline-secondary btn-sm" id="monthPrevBtn" aria-label="Previous months">
      <i class="bi bi-chevron-left"></i>
    </button>
    <button class="btn btn-outline-secondary btn-sm" id="monthNextBtn" aria-label="Next months">
      <i class="bi bi-chevron-right"></i>
    </button>
  </div>
  <div class="d-flex flex-nowrap" id="monthTabsContainer">
    <!-- Month buttons will be injected here by JS -->
  </div>
</div>
<div id="monthEventsContents">
  <div class="month-content" data-month="1">
    <div class="d-flex mb-3 align-items-center">
      <div class="bg-orange text-white text-center rounded p-2 me-3" style="width: 60px;">
        <div class="small">Jan</div>
        <div class="fw-bold">10</div>
      </div>
      <div>
        <h3 class="fw-bold mb-1 head-color">Staff Jan Event 1</h3>
        <small class="text-color">Jan 10 – Jan 12</small>
      </div>
    </div>
    <div class="d-flex mb-3 align-items-center">
      <div class="bg-blue text-white text-center rounded p-2 me-3" style="width: 60px;">
        <div class="small">Jan</div>
        <div class="fw-bold">22</div>
      </div>
      <div>
        <h3 class="fw-bold mb-1 head-color">Staff Jan Event 2</h3>
        <small class="text-color">Jan 22 – Jan 24</small>
      </div>
    </div>
  </div>
  <div class="month-content d-none" data-month="2">
    <div class="d-flex mb-3 align-items-center">
      <div class="bg-orange text-white text-center rounded p-2 me-3" style="width: 60px;">
        <div class="small">Feb</div>
        <div class="fw-bold">5</div>
      </div>
      <div>
        <h3 class="fw-bold mb-1 head-color">Staff Feb Event 1</h3>
        <small class="text-color">Feb 5 – Feb 7</small>
      </div>
    </div>
  </div>
  <div class="month-content d-none" data-month="3">
    <div class="d-flex mb-3 align-items-center">
      <div class="bg-blue text-white text-center rounded p-2 me-3" style="width: 60px;">
        <div class="small">Mar</div>
        <div class="fw-bold">12</div>
      </div>
      <div>
        <h3 class="fw-bold mb-1 head-color">Staff Mar Event 1</h3>
        <small class="text-color">Mar 12 – Mar 14</small>
      </div>
    </div>
  </div>
  <div class="month-content d-none" data-month="4">
    <div class="d-flex mb-3 align-items-center">
      <div class="bg-orange text-white text-center rounded p-2 me-3" style="width: 60px;">
        <div class="small">Apr</div>
        <div class="fw-bold">8</div>
      </div>
      <div>
        <h3 class="fw-bold mb-1 head-color">Staff Apr Event 1</h3>
        <small class="text-color">Apr 8 – Apr 10</small>
      </div>
    </div>
  </div>
  <div class="month-content d-none" data-month="5">
    <div class="d-flex mb-3 align-items-center">
      <div class="bg-blue text-white text-center rounded p-2 me-3" style="width: 60px;">
        <div class="small">May</div>
        <div class="fw-bold">15</div>
      </div>
      <div>
        <h3 class="fw-bold mb-1 head-color">Staff May Event 1</h3>
        <small class="text-color">May 15 – May 17</small>
      </div>
    </div>
  </div>
  <div class="month-content d-none" data-month="6">
    <div class="d-flex mb-3 align-items-center">
      <div class="bg-orange text-white text-center rounded p-2 me-3" style="width: 60px;">
        <div class="small">Jun</div>
        <div class="fw-bold">20</div>
      </div>
      <div>
        <h3 class="fw-bold mb-1 head-color">Staff Jun Event 1</h3>
        <small class="text-color">Jun 20 – Jun 22</small>
      </div>
    </div>
  </div>
  <div class="month-content d-none" data-month="7">
    <div class="d-flex mb-3 align-items-center">
      <div class="bg-blue text-white text-center rounded p-2 me-3" style="width: 60px;">
        <div class="small">Jul</div>
        <div class="fw-bold">3</div>
      </div>
      <div>
        <h3 class="fw-bold mb-1 head-color">Staff Jul Event 1</h3>
        <small class="text-color">Jul 3 – Jul 5</small>
      </div>
    </div>
  </div>
  <div class="month-content d-none" data-month="8">
    <div class="d-flex mb-3 align-items-center">
      <div class="bg-orange text-white text-center rounded p-2 me-3" style="width: 60px;">
        <div class="small">Aug</div>
        <div class="fw-bold">18</div>
      </div>
      <div>
        <h3 class="fw-bold mb-1 head-color">Staff Aug Event 1</h3>
        <small class="text-color">Aug 18 – Aug 20</small>
      </div>
    </div>
  </div>
  <div class="month-content d-none" data-month="9">
    <div class="d-flex mb-3 align-items-center">
      <div class="bg-blue text-white text-center rounded p-2 me-3" style="width: 60px;">
        <div class="small">Sep</div>
        <div class="fw-bold">9</div>
      </div>
      <div>
        <h3 class="fw-bold mb-1 head-color">Staff Sep Event 1</h3>
        <small class="text-color">Sep 9 – Sep 11</small>
      </div>
    </div>
  </div>
  <div class="month-content d-none" data-month="10">
    <div class="d-flex mb-3 align-items-center">
      <div class="bg-orange text-white text-center rounded p-2 me-3" style="width: 60px;">
        <div class="small">Oct</div>
        <div class="fw-bold">25</div>
      </div>
      <div>
        <h3 class="fw-bold mb-1 head-color">Staff Oct Event 1</h3>
        <small class="text-color">Oct 25 – Oct 27</small>
      </div>
    </div>
  </div>
  <div class="month-content d-none" data-month="11">
    <div class="d-flex mb-3 align-items-center">
      <div class="bg-blue text-white text-center rounded p-2 me-3" style="width: 60px;">
        <div class="small">Nov</div>
        <div class="fw-bold">14</div>
      </div>
      <div>
        <h3 class="fw-bold mb-1 head-color">Staff Nov Event 1</h3>
        <small class="text-color">Nov 14 – Nov 16</small>
      </div>
    </div>
  </div>
  <div class="month-content d-none" data-month="12">
    <div class="d-flex mb-3 align-items-center">
      <div class="bg-orange text-white text-center rounded p-2 me-3" style="width: 60px;">
        <div class="small">Dec</div>
        <div class="fw-bold">6</div>
      </div>
      <div>
        <h3 class="fw-bold mb-1 head-color">Staff Dec Event 1</h3>
        <small class="text-color">Dec 6 – Dec 8</small>
      </div>
    </div>
  </div>
</div>
<script>
(function() {
  const months = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ];
  const visibleCount = 6; // Number of months visible at once
  let startIdx = 0;
  const currentMonth = 6; // July (0-based)

  function renderMonthTabs() {
    const container = document.getElementById('monthTabsContainer');
    container.innerHTML = '';
    for (let i = 0; i < 12; i++) {
      const btn = document.createElement('button');
      btn.className = 'btn btn-outline-primary month-btn';
      if (i === currentMonth) btn.classList.add('active');
      btn.textContent = months[i];
      btn.dataset.month = (i + 1).toString();
      btn.onclick = function() {
        document.querySelectorAll('.month-btn').forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        document.querySelectorAll('.month-content').forEach(div => div.classList.add('d-none'));
        document.querySelector('.month-content[data-month="' + btn.dataset.month + '"]').classList.remove('d-none');
      };
      container.appendChild(btn);
    }
    updateMonthTabsView();
    // Show current month events
    document.querySelectorAll('.month-content').forEach(div => div.classList.add('d-none'));
    document.querySelector('.month-content[data-month="' + (currentMonth + 1) + '"]').classList.remove('d-none');
  }

  function updateMonthTabsView() {
    const btns = document.querySelectorAll('.month-btn');
    for (let i = 0; i < btns.length; i++) {
      if (i >= startIdx && i < startIdx + visibleCount) {
        btns[i].style.display = 'inline-block';
      } else {
        btns[i].style.display = 'none';
      }
    }
    document.getElementById('monthPrevBtn').disabled = startIdx === 0;
    document.getElementById('monthNextBtn').disabled = startIdx >= (12 - visibleCount);
  }

  document.getElementById('monthPrevBtn').onclick = function() {
    if (startIdx > 0) {
      startIdx--;
      updateMonthTabsView();
    }
  };
  document.getElementById('monthNextBtn').onclick = function() {
    if (startIdx < 12 - visibleCount) {
      startIdx++;
      updateMonthTabsView();
    }
  };

  // Ensure current month is visible on load
  if (currentMonth > visibleCount - 1) {
    startIdx = currentMonth - visibleCount + 1;
  } else {
    startIdx = 0;
  }

  renderMonthTabs();
})();
</script>
                  </div>
                </div>
              </div>
            </section>`

    // Initialize calendars for both tabs
    this.initializeCalendarTab('weekDaysContainer', 'calendarMonthLabel', 'prevWeekBtn', 'nextWeekBtn', 'tab1');
    this.initializeCalendarTab('weekDaysContainer2', 'calendarMonthLabel2', 'prevWeekBtn2', 'nextWeekBtn2', 'tab2');

  }

  private initializeCalendarTab(
    weekContainerId: string,
    monthLabelId: string,
    prevBtnId: string,
    nextBtnId: string,
    tabKey: 'tab1' | 'tab2'
  ): void {
    const weekContainer = document.getElementById(weekContainerId) as HTMLDivElement;
    const monthLabel = document.getElementById(monthLabelId) as HTMLElement;
    const prevBtn = document.getElementById(prevBtnId) as HTMLButtonElement;
    const nextBtn = document.getElementById(nextBtnId) as HTMLButtonElement;

    if (weekContainer && monthLabel && prevBtn && nextBtn) {
      const getStartOfWeek = (date: Date): Date => {
        const day = date.getDay();
        const newDate = new Date(date);
        newDate.setDate(newDate.getDate() - day);
        return newDate;
      };

      const renderWeek = (date: Date): void => {
        const start = getStartOfWeek(new Date(date));
        const dayNames = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
        weekContainer.innerHTML = '';

        monthLabel.textContent = start.toLocaleDateString('en-US', {
          month: 'long',
          year: 'numeric'
        });

        for (let i = 0; i < 7; i++) {
          const day = new Date(start);
          day.setDate(start.getDate() + i);

          const dayBlock = document.createElement('div');
          dayBlock.className = 'cursor-pointer';
          const isToday = new Date().toDateString() === day.toDateString();

          dayBlock.innerHTML = `
            <div class="fw-bold small">${dayNames[i]}</div>
            <div class="fw-semibold px-2 py-1 rounded ${isToday ? 'bg-main text-white' : ''}">
              ${day.getDate()}
            </div>
          `;
          weekContainer.appendChild(dayBlock);
        }
      };

      prevBtn.addEventListener('click', () => {
        this.calendarStates[tabKey].currentDate.setDate(this.calendarStates[tabKey].currentDate.getDate() - 7);
        renderWeek(this.calendarStates[tabKey].currentDate);
      });

      nextBtn.addEventListener('click', () => {
        this.calendarStates[tabKey].currentDate.setDate(this.calendarStates[tabKey].currentDate.getDate() + 7);
        renderWeek(this.calendarStates[tabKey].currentDate);
      });

      renderWeek(this.calendarStates[tabKey].currentDate);
    }
  }

}
