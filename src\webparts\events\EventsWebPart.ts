import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { HttpClient, HttpClientResponse } from '@microsoft/sp-http';

export interface IEventsWebPartProps {
  description: string;
}

interface Event {
  id: number;
  title: string;
  meta: {
    start_date: string;
    end_date: string;
    location: string;
    event_type: string;
    audience: string;
  };
  excerpt: string;
  featured_image?: {
    url: string;
  };
}

interface EventsApiResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Event[];
  tags: {
    event_types: string[];
    exhibition_types: string[];
    locations: string[];
    organisers: string[];
    audience: string[];
  };
  page_types: string[];
}

export default class EventsWebPart extends BaseClientSideWebPart<IEventsWebPartProps> {
  private events: Event[] = [];
  private publicEvents: Event[] = [];
  private staffEvents: Event[] = [];
  private currentPage: number = 1;
  private totalCount: number = 0;
  private isLoading: boolean = false;
  private selectedQuarter: string = '';
  private activeTab: string = 'public';


  public async render(): Promise<void> {
    this.domElement.innerHTML = `<div class="text-center py-4">Loading events...</div>`;
    await this.loadEvents();
    this.renderNewUI();
    this.setupEventListeners();
  }

  private async loadEvents(): Promise<void> {
    try {
      this.isLoading = true;
      console.log('Loading events from API...');

      const url = `https://qm.org.qa/api/v2/pages/events/?format=json&page=${this.currentPage}`;

      const response: HttpClientResponse = await this.context.httpClient.get(
        url,
        HttpClient.configurations.v1
      );

      if (response.ok) {
        const data: EventsApiResponse = await response.json();
        console.log('Events API response:', data);

        this.events = data.results || [];
        this.totalCount = data.count || 0;
        this.separateEventsByAudience();

        console.log(`Loaded ${this.events.length} events. Total: ${this.totalCount}`);
      } else {
        console.error('Failed to load events:', response.statusText);
        this.events = [];
      }

    } catch (error) {
      console.error('Error loading events:', error);
      this.events = [];
    } finally {
      this.isLoading = false;
    }
  }

  private separateEventsByAudience(): void {
    this.publicEvents = this.events.filter(event =>
      !event.meta.audience ||
      event.meta.audience.toLowerCase().includes('public') ||
      event.meta.audience.toLowerCase().includes('general')
    );

    this.staffEvents = this.events.filter(event =>
      event.meta.audience &&
      (event.meta.audience.toLowerCase().includes('staff') ||
       event.meta.audience.toLowerCase().includes('employee'))
    );

    console.log(`Public events: ${this.publicEvents.length}, Staff events: ${this.staffEvents.length}`);
  }

 

  // دالة renderUI جديدة
  private renderNewUI(): void {
    this.domElement.innerHTML = `
      <section class="events-section h-100">
        <div class="card section-card card-color px-3 pt-3 h-100">
          <h2 class="fw-bold tittle head-color">What's On</h2>

          <!-- Tabs -->
          <ul class="nav nav-tabs mb-3" id="eventTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link fw-bold ${this.activeTab === 'public' ? 'active' : ''}"
                      aria-label="preregister-tab" id="preregister-tab" data-bs-toggle="tab"
                      data-bs-target="#preregister" type="button" role="tab"
                      aria-controls="preregister" aria-selected="${this.activeTab === 'public' ? 'true' : 'false'}">
                Public
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link fw-bold ${this.activeTab === 'staff' ? 'active' : ''}"
                      aria-label="whatson-tab" id="whatson-tab" data-bs-toggle="tab"
                      data-bs-target="#whatson" type="button" role="tab"
                      aria-controls="whatson" aria-selected="${this.activeTab === 'staff' ? 'true' : 'false'}"
                      ${this.activeTab === 'staff' ? '' : 'tabindex="-1"'}>
                Staff
              </button>
            </li>
          </ul>

          <!-- Tab Content -->
          <div class="tab-content" id="eventTabsContent">

            <!-- Public Events Tab -->
            <div class="tab-pane fade ${this.activeTab === 'public' ? 'active show' : ''}"
                 id="preregister" role="tabpanel" aria-labelledby="preregister-tab">

              <div class="text-center flex-grow-1">
                <div class="d-flex justify-content-center align-items-center mb-3 gap-2">
                  <button class="btn btn-outline-primary quarter-btn ${this.selectedQuarter === 'Q1' ? 'active' : ''}" data-quarter="Q1">Q1</button>
                  <button class="btn btn-outline-primary quarter-btn ${this.selectedQuarter === 'Q2' ? 'active' : ''}" data-quarter="Q2">Q2</button>
                  <button class="btn btn-outline-primary quarter-btn ${this.selectedQuarter === 'Q3' ? 'active' : ''}" data-quarter="Q3">Q3</button>
                  <button class="btn btn-outline-primary quarter-btn ${this.selectedQuarter === 'Q4' ? 'active' : ''}" data-quarter="Q4">Q4</button>
                </div>
              </div>

              <div id="quarterContents">
                ${this.renderQuarterContent()}
              </div>

              <script>
                // Quarter tab logic
                document.querySelectorAll('.quarter-btn').forEach(btn => {
                  btn.addEventListener('click', function() {
                    // Tab button active state
                    document.querySelectorAll('.quarter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    // Show/hide quarter content
                    const selected = this.dataset.quarter;
                    document.querySelectorAll('.quarter-content').forEach(div => {
                      if (div.dataset.quarter === selected) {
                        div.classList.remove('d-none');
                      } else {
                        div.classList.add('d-none');
                      }
                    });
                  });
                });
              </script>

            </div>

            <!-- Staff Events Tab -->
            <div class="tab-pane fade ${this.activeTab === 'staff' ? 'active show' : ''}"
                 id="whatson" role="tabpanel" aria-labelledby="whatson-tab">

              ${this.renderStaffEventsContent()}

            </div>
          </div>
        </div>
      </section>
    `;
  }

  // دوال جديدة للـ API
  private renderQuarterContent(): string {
    return `
      <div class="quarter-content ${this.selectedQuarter === 'Q1' ? '' : 'd-none'}" data-quarter="Q1">
        ${this.renderQuarterEvents('Q1')}
      </div>
      <div class="quarter-content ${this.selectedQuarter === 'Q2' ? '' : 'd-none'}" data-quarter="Q2">
        ${this.renderQuarterEvents('Q2')}
      </div>
      <div class="quarter-content ${this.selectedQuarter === 'Q3' ? '' : 'd-none'}" data-quarter="Q3">
        ${this.renderQuarterEvents('Q3')}
      </div>
      <div class="quarter-content ${this.selectedQuarter === 'Q4' ? '' : 'd-none'}" data-quarter="Q4">
        ${this.renderQuarterEvents('Q4')}
      </div>
    `;
  }

  private renderQuarterEvents(quarter: string): string {
    // استخدام بيانات وهمية للعرض
    const quarterEvents = this.getQuarterEventsData(quarter);

    if (quarterEvents.length === 0) {
      return `
        <div class="text-center py-4">
          <div class="text-muted">No events found for ${quarter}</div>
        </div>
      `;
    }

    return quarterEvents.map(event => `
      <div class="d-flex mb-3 align-items-center">
        <div class="${event.bgColor} text-white text-center rounded p-2 me-3" style="width: 60px;">
          <div class="small">${event.month}</div>
          <div class="fw-bold">${event.day}</div>
        </div>
        <div>
          <h3 class="fw-bold mb-1 head-color">${event.title}</h3>
          <small class="text-color">${event.dateRange}</small>
        </div>
      </div>
    `).join('');
  }

  private getQuarterEventsData(quarter: string): any[] {
    const eventsData = {
      'Q1': [
        { title: 'Q1 Event Example 1', month: 'Jan', day: '15', dateRange: 'Jan 15 – Jan 18', bgColor: 'bg-orange' },
        { title: 'Q1 Event Example 2', month: 'Feb', day: '2', dateRange: 'Feb 2 – Feb 5', bgColor: 'bg-blue' },
        { title: 'Q1 Event Example 3', month: 'Mar', day: '10', dateRange: 'Mar 10 – Mar 12', bgColor: 'bg-orange' }
      ],
      'Q2': [
        { title: 'Q2 Event Example 1', month: 'Apr', day: '10', dateRange: 'Apr 10 – Apr 12', bgColor: 'bg-blue' },
        { title: 'Q2 Event Example 2', month: 'May', day: '5', dateRange: 'May 5 – May 8', bgColor: 'bg-orange' },
        { title: 'Q2 Event Example 3', month: 'Jun', day: '18', dateRange: 'Jun 18 – Jun 20', bgColor: 'bg-blue' }
      ],
      'Q3': [
        { title: 'Q3 Event Example 1', month: 'Jul', day: '22', dateRange: 'Jul 22 – Jul 25', bgColor: 'bg-orange' },
        { title: 'Q3 Event Example 2', month: 'Aug', day: '8', dateRange: 'Aug 8 – Aug 10', bgColor: 'bg-blue' },
        { title: 'Q3 Event Example 3', month: 'Sep', day: '14', dateRange: 'Sep 14 – Sep 16', bgColor: 'bg-orange' }
      ],
      'Q4': [
        { title: 'Q4 Event Example 1', month: 'Oct', day: '5', dateRange: 'Oct 5 – Oct 8', bgColor: 'bg-blue' },
        { title: 'Q4 Event Example 2', month: 'Nov', day: '12', dateRange: 'Nov 12 – Nov 15', bgColor: 'bg-orange' },
        { title: 'Q4 Event Example 3', month: 'Dec', day: '20', dateRange: 'Dec 20 – Dec 22', bgColor: 'bg-blue' }
      ]
    };

    return eventsData[quarter as keyof typeof eventsData] || [];
  }

  private renderStaffEventsContent(): string {
    return `
      <style>
        #monthTabsWrapper {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          margin-bottom: 1rem;
        }
        #monthTabsArrows {
          display: flex;
          gap: 0.5rem;
          margin-bottom: 0.5rem;
        }
        #monthTabsContainer {
          display: flex;
          gap: 0.5rem;
          width: 100%;
          overflow: hidden;
        }
        .month-btn {
          min-width: 56px;
          border-radius: 12px;
          border: 2px solid var(--border-color);
          background: transparent;
          color: var(--text-color);
          font-weight: 600;
          font-size: 1rem;
          transition: background 0.2s, color 0.2s, border 0.2s;
          box-shadow: none;
          display: none;
        }
        .month-btn.active {
          background: var(--main-color);
          color: #fff !important;
          border-color: var(--main-color);
        }
        #monthPrevBtn, #monthNextBtn {
          background: transparent !important;
          border: 2px solid #ffd000 !important;
          color: #ffd000 !important;
          border-radius: 10px;
          width: 38px;
          height: 38px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0;
          box-shadow: none;
          transition: background 0.2s, color 0.2s, border 0.2s;
        }
        #monthPrevBtn:disabled, #monthNextBtn:disabled {
          opacity: 0.4;
          cursor: not-allowed;
        }
        #monthEventsContents {
          height: 197px;
          overflow-y: auto;
        }
      </style>

      <div id="monthTabsWrapper">
        <div id="monthTabsArrows">
          <button class="btn btn-outline-secondary btn-sm" id="monthPrevBtn" aria-label="Previous months">
            <i class="bi bi-chevron-left"></i>
          </button>
          <button class="btn btn-outline-secondary btn-sm" id="monthNextBtn" aria-label="Next months">
            <i class="bi bi-chevron-right"></i>
          </button>
        </div>
        <div class="d-flex flex-nowrap" id="monthTabsContainer">
          ${this.renderMonthButtons()}
        </div>
      </div>

      <div id="monthEventsContents">
        ${this.renderMonthContents()}
      </div>
    `;
  }

  private renderMonthButtons(): string {
    const months = [
      { num: 1, name: 'Jan', visible: false },
      { num: 2, name: 'Feb', visible: true },
      { num: 3, name: 'Mar', visible: true },
      { num: 4, name: 'Apr', visible: true },
      { num: 5, name: 'May', visible: true },
      { num: 6, name: 'Jun', visible: true },
      { num: 7, name: 'Jul', visible: true, active: true },
      { num: 8, name: 'Aug', visible: false },
      { num: 9, name: 'Sep', visible: false },
      { num: 10, name: 'Oct', visible: false },
      { num: 11, name: 'Nov', visible: false },
      { num: 12, name: 'Dec', visible: false }
    ];

    return months.map(month => `
      <button class="btn btn-outline-primary month-btn ${month.active ? 'active' : ''}"
              data-month="${month.num}"
              style="display: ${month.visible ? 'inline-block' : 'none'};">
        ${month.name}
      </button>
    `).join('');
  }

  private renderMonthContents(): string {
    const monthsData = {
      1: [{ title: 'Staff Jan Event 1', day: '10', dateRange: 'Jan 10 – Jan 12', bgColor: 'bg-orange' }, { title: 'Staff Jan Event 2', day: '22', dateRange: 'Jan 22 – Jan 24', bgColor: 'bg-blue' }],
      2: [{ title: 'Staff Feb Event 1', day: '5', dateRange: 'Feb 5 – Feb 7', bgColor: 'bg-orange' }],
      3: [{ title: 'Staff Mar Event 1', day: '12', dateRange: 'Mar 12 – Mar 14', bgColor: 'bg-blue' }],
      4: [{ title: 'Staff Apr Event 1', day: '8', dateRange: 'Apr 8 – Apr 10', bgColor: 'bg-orange' }],
      5: [{ title: 'Staff May Event 1', day: '15', dateRange: 'May 15 – May 17', bgColor: 'bg-blue' }],
      6: [{ title: 'Staff Jun Event 1', day: '20', dateRange: 'Jun 20 – Jun 22', bgColor: 'bg-orange' }],
      7: [{ title: 'Staff Jul Event 1', day: '3', dateRange: 'Jul 3 – Jul 5', bgColor: 'bg-blue' }],
      8: [{ title: 'Staff Aug Event 1', day: '18', dateRange: 'Aug 18 – Aug 20', bgColor: 'bg-orange' }],
      9: [{ title: 'Staff Sep Event 1', day: '9', dateRange: 'Sep 9 – Sep 11', bgColor: 'bg-blue' }],
      11: [{ title: 'Staff Nov Event 1', day: '14', dateRange: 'Nov 14 – Nov 16', bgColor: 'bg-blue' }],
      12: [{ title: 'Staff Dec Event 1', day: '6', dateRange: 'Dec 6 – Dec 8', bgColor: 'bg-orange' }]
    };

    return Object.entries(monthsData).map(([monthNum, events]) => {
      const isActive = monthNum === '7'; // July is active by default
      const monthName = new Date(2024, parseInt(monthNum) - 1, 1).toLocaleDateString('en-US', { month: 'short' });

      return `
        <div class="month-content ${isActive ? '' : 'd-none'}" data-month="${monthNum}">
          ${events.map(event => `
            <div class="d-flex mb-3 align-items-center">
              <div class="${event.bgColor} text-white text-center rounded p-2 me-3" style="width: 60px;">
                <div class="small">${monthName}</div>
                <div class="fw-bold">${event.day}</div>
              </div>
              <div>
                <h3 class="fw-bold mb-1 head-color">${event.title}</h3>
                <small class="text-color">${event.dateRange}</small>
              </div>
            </div>
          `).join('')}
        </div>
      `;
    }).join('');
  }



  private setupEventListeners(): void {
    // Bootstrap tab switching
    this.domElement.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
      tab.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        const tabId = target.getAttribute('data-bs-target');

        if (tabId === '#preregister') {
          this.activeTab = 'public';
        } else if (tabId === '#whatson') {
          this.activeTab = 'staff';
        }

        // Update active states
        this.domElement.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
        target.classList.add('active');

        // Update tab panes
        this.domElement.querySelectorAll('.tab-pane').forEach(pane => {
          pane.classList.remove('active', 'show');
        });

        if (tabId) {
          const targetPane = this.domElement.querySelector(tabId);
          if (targetPane) {
            targetPane.classList.add('active', 'show');
          }
        }
      });
    });

    // Quarter switching (already handled by inline script)
    this.domElement.querySelectorAll('.quarter-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        const quarter = target.getAttribute('data-quarter');
        if (quarter) {
          this.selectedQuarter = quarter;

          // Update active states
          this.domElement.querySelectorAll('.quarter-btn').forEach(b => b.classList.remove('active'));
          target.classList.add('active');

          // Show/hide quarter content
          this.domElement.querySelectorAll('.quarter-content').forEach(div => {
            const divElement = div as HTMLElement;
            if (divElement.dataset.quarter === quarter) {
              divElement.classList.remove('d-none');
            } else {
              divElement.classList.add('d-none');
            }
          });
        }
      });
    });

    // Month switching for staff events
    this.domElement.querySelectorAll('.month-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        const month = target.getAttribute('data-month');
        if (month) {
          // Update active states
          this.domElement.querySelectorAll('.month-btn').forEach(b => b.classList.remove('active'));
          target.classList.add('active');

          // Show/hide month content
          this.domElement.querySelectorAll('.month-content').forEach(div => {
            const divElement = div as HTMLElement;
            if (divElement.dataset.month === month) {
              divElement.classList.remove('d-none');
            } else {
              divElement.classList.add('d-none');
            }
          });
        }
      });
    });

    // Month navigation arrows
    const prevBtn = this.domElement.querySelector('#monthPrevBtn');
    const nextBtn = this.domElement.querySelector('#monthNextBtn');

    if (prevBtn) {
      prevBtn.addEventListener('click', () => {
        this.navigateMonths(-1);
      });
    }

    if (nextBtn) {
      nextBtn.addEventListener('click', () => {
        this.navigateMonths(1);
      });
    }
  }

  private navigateMonths(direction: number): void {
    // Simple navigation logic - can be enhanced
    console.log(`Navigate months: ${direction > 0 ? 'next' : 'previous'}`);
    // TODO: Implement month navigation logic
  }

  // دوال للاختبار
  public showEventsInfo(): void {
    console.log('=== Events Info ===');
    console.log('Total events:', this.events.length);
    console.log('Public events:', this.publicEvents.length);
    console.log('Staff events:', this.staffEvents.length);
    console.log('Current page:', this.currentPage);
    console.log('Total count:', this.totalCount);
    console.log('Active tab:', this.activeTab);
    console.log('Selected quarter:', this.selectedQuarter);
  }

  public switchToQuarter(quarter: string): void {
    if (['Q1', 'Q2', 'Q3', 'Q4'].includes(quarter)) {
      this.selectedQuarter = quarter;
      this.renderNewUI();
      this.setupEventListeners();
      console.log(`Switched to ${quarter}`);
    } else {
      console.log('Invalid quarter. Use Q1, Q2, Q3, or Q4');
    }
  }

  public switchTab(tab: string): void {
    if (['public', 'staff'].includes(tab)) {
      this.activeTab = tab;
      this.renderNewUI();
      this.setupEventListeners();
      console.log(`Switched to ${tab} tab`);
    } else {
      console.log('Invalid tab. Use "public" or "staff"');
    }
  }

  public async refreshEvents(): Promise<void> {
    console.log('Refreshing events...');
    this.currentPage = 1;
    await this.loadEvents();
    this.renderNewUI();
    this.setupEventListeners();
  }

  protected onInit(): Promise<void> {
    (window as any).eventsWebPart = this;
    return Promise.resolve();
  }

}
